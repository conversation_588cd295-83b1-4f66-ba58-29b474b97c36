import { Component, EventEmitter, Input, OnInit, Output, OnDestroy } from '@angular/core';
import { HttpResponse } from '@angular/common/http';
import { CommonModule, NgFor } from '@angular/common';
import { tap } from 'rxjs';
import { GetHouseReview } from '../../../../../services/api/models';
import { ButtonModule } from 'primeng/button';
import { BaseFilePipe } from '../../../../shared/pipes/base-file.pipe';
import { HouseService } from '../../../../../services/api/services';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import { FileService } from '../../../../../services/File.service';

@Component({
  selector: 'app-step-v-choice',
  standalone: true, imports: [
    CommonModule,
    ButtonModule,
    NgFor
  ],
  templateUrl: './step-v-choice.component.html',
  styleUrl: './step-v-choice.component.scss'
})
export class StepVChoiceComponent implements OnInit, OnDestroy {
  @Input() listHouseReview: GetHouseReview[] = []
  @Output() nextEvent = new EventEmitter()
  @Output() refreshHouseReview = new EventEmitter()
  @Output() listHouseReviewChange = new EventEmitter();

  currentHouseReview!: GetHouseReview
  isReviewing: boolean = false;
  baseFilePipe: BaseFilePipe;
  currentPdfUrl: SafeResourceUrl | null = null; // 快取當前 PDF 的安全 URL
  // PDF 處理相關屬性
  pdfBlobUrls: Map<string, string> = new Map(); // 儲存每個 PDF 的 blob URL
  pdfDataUrls: Map<string, string> = new Map(); // 儲存每個 PDF 的 data URL
  loadingPdfUrls: Set<string> = new Set(); // 追蹤正在載入的 PDF URL

  constructor(
    private _houseService: HouseService,
    private sanitizer: DomSanitizer,
    private _fileService: FileService
  ) {
    this.baseFilePipe = new BaseFilePipe(this.sanitizer);
  }  // 添加安全 URL 處理方法
  getSafePdfUrl(fileUrl: string): SafeResourceUrl {
    // 首先檢查是否已經有對應的 blob URL
    const existingBlobUrl = this.pdfBlobUrls.get(fileUrl);
    if (existingBlobUrl) {
      return this.sanitizer.bypassSecurityTrustResourceUrl(existingBlobUrl);
    }

    // 檢查是否有 data URL
    const existingDataUrl = this.pdfDataUrls.get(fileUrl);
    if (existingDataUrl) {
      return this.sanitizer.bypassSecurityTrustResourceUrl(existingDataUrl);
    }

    // 如果沒有快取的 URL，返回空字串或預設值，不觸發載入
    return this.sanitizer.bypassSecurityTrustResourceUrl('');
  }  // 載入 PDF 文件並建立 blob URL
  private loadPdfFile(fileUrl: string, fileName: string): void {
    // 檢查是否正在載入或已經載入完成
    if (this.loadingPdfUrls.has(fileUrl) || this.pdfBlobUrls.has(fileUrl)) {
      return;
    }

    // 標記為正在載入
    this.loadingPdfUrls.add(fileUrl);

    this._fileService.getFile(fileUrl, fileName).subscribe(
      (response: HttpResponse<Blob>) => {
        if (response.body) {
          // 方法1: 創建 blob URL
          const blobUrl = URL.createObjectURL(response.body);
          this.pdfBlobUrls.set(fileUrl, blobUrl);

          // 方法2: 轉換為 base64 data URL (更好的兼容性)
          const reader = new FileReader();
          reader.onload = () => {
            const dataUrl = reader.result as string;
            this.pdfDataUrls.set(fileUrl, dataUrl);
            console.log('PDF Data URL 已準備就緒:', fileUrl);

            // 如果這是當前顯示的 PDF，更新快取的 URL
            if (this.currentHouseReview?.CFileUrl === fileUrl) {
              this.updateCurrentPdfUrl();
            }
          };
          reader.readAsDataURL(response.body);
        }

        console.log('PDF Blob URL 已準備就緒:', fileUrl);

        // 移除載入狀態
        this.loadingPdfUrls.delete(fileUrl);

        // 如果這是當前顯示的 PDF，更新快取的 URL
        if (this.currentHouseReview?.CFileUrl === fileUrl) {
          this.updateCurrentPdfUrl();
        }
      },
      (error) => {
        console.error('取得 PDF 文件時發生錯誤:', fileUrl, error);
        // 載入失敗時也要移除載入狀態
        this.loadingPdfUrls.delete(fileUrl);
      }
    );
  }



  ngOnInit(): void {
    // 如果沒有資料，觸發重新載入
    if (!this.listHouseReview || this.listHouseReview.length === 0) {
      this.refreshHouseReview.emit();
    }
  }

  // 從檔案 URL 或名稱提取擴展名
  private getFileExtension(houseReview: GetHouseReview): string {
    let fileName = '';

    // 優先從 CReviewName 提取檔名（因為它包含完整的檔案名稱和擴展名）
    if (houseReview.CReviewName) {
      fileName = houseReview.CReviewName.trim(); // 移除前後空白
    } else if (houseReview.CFileUrl) {
      const urlParts = houseReview.CFileUrl.split('/');
      fileName = urlParts[urlParts.length - 1];
    }

    if (fileName && fileName.includes('.')) {
      return fileName.toLowerCase().substring(fileName.lastIndexOf('.'));
    }

    return '';
  }

  // 判斷是否為 CAD 檔案
  private isCADFile(houseReview: GetHouseReview): boolean {
    const extension = this.getFileExtension(houseReview);
    return ['.dwg', '.dxf', '.dwf'].includes(extension);
  }

  // 下載檔案
  downloadFile(houseReview: GetHouseReview): void {
    if (!houseReview.CFileUrl || !houseReview.CReviewName) {
      console.error('檔案 URL 或名稱不存在');
      return;
    }

    this._fileService.getFile(houseReview.CFileUrl, houseReview.CReviewName).subscribe({
      next: (response: HttpResponse<Blob>) => {
        if (response.body) {
          // 創建下載鏈接
          const url = window.URL.createObjectURL(response.body);
          const link = document.createElement('a');
          link.href = url;
          link.download = houseReview.CReviewName || 'download';
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          window.URL.revokeObjectURL(url);
        }
      },
      error: (error) => {
        console.error('檔案下載失敗:', error);
      }
    });
  }

  gotoPickItem(houseReview: GetHouseReview) {
    // 如果是 CAD 檔案，直接下載
    if (this.isCADFile(houseReview)) {
      this.downloadFile(houseReview);
      return;
    }

    // PDF 檔案進入審閱頁面
    this.currentHouseReview = houseReview;
    this.isReviewing = true;

    // 更新當前 PDF URL
    this.updateCurrentPdfUrl();

    // 當進入審閱頁面時，載入對應的 PDF 檔案
    if (houseReview.CFileUrl && !this.pdfBlobUrls.has(houseReview.CFileUrl) && !this.loadingPdfUrls.has(houseReview.CFileUrl)) {
      this.loadPdfFile(houseReview.CFileUrl, houseReview.CReviewName || 'document.pdf');
    }
  }

  // 更新當前 PDF URL 的快取
  private updateCurrentPdfUrl(): void {
    if (!this.currentHouseReview?.CFileUrl) {
      this.currentPdfUrl = null;
      return;
    }

    this.currentPdfUrl = this.getSafePdfUrl(this.currentHouseReview.CFileUrl);
  }

  handleStyleOption(isReview: boolean) {
    if (isReview) {
      return { 'background': 'linear-gradient(90deg, #AE9B66 0%, #B8A676 100%)', 'color': 'white' }
    }
    return { 'backgroundColor': '#E5E3E1', 'color': '#3A4246B2' }
  }

  // 根據檔案類型和審閱狀態決定按鈕文字
  hanldeTitleOption(houseReview: GetHouseReview): string {
    const isCAD = this.isCADFile(houseReview);

    if (isCAD) {
      // CAD 檔案顯示下載按鈕
      return '下載';
    } else {
      // PDF 檔案顯示審閱狀態
      return houseReview.CIsReview ? '已審閱' : '未審閱';
    }
  }

  next() {
    this.nextEvent.emit();
  }

  handleReview() {
    this._houseService.apiHouseUpdateHouseReviewPost$Json({
      body: {
        CHouseReviewID: this.currentHouseReview.CHouseReviewId!
      }
    }).pipe(
      tap(() => this.refreshHouseReview.emit()),
      tap(res => {
        if (res.StatusCode == 0) {
          this.currentHouseReview = {} as GetHouseReview
          this.isReviewing = false;
        }
      })
    ).subscribe();
  }
  checkDisable() {
    // 過濾出需要審閱的檔案（非 CAD 檔案）
    const reviewableFiles = this.listHouseReview.filter(x => !this.isCADFile(x));

    // 如果沒有需要審閱的檔案，則啟用下一步
    if (reviewableFiles.length === 0) {
      return false;
    }

    // 檢查所有需要審閱的檔案是否都已審閱
    return !reviewableFiles.every(x => x.CIsReview);
  }
  ngOnDestroy(): void {
    // 清理所有 blob URL 以避免記憶體洩漏
    this.pdfBlobUrls.forEach((blobUrl) => {
      URL.revokeObjectURL(blobUrl);
    });
    this.pdfBlobUrls.clear();
    this.pdfDataUrls.clear();
    this.loadingPdfUrls.clear();
  }
}
