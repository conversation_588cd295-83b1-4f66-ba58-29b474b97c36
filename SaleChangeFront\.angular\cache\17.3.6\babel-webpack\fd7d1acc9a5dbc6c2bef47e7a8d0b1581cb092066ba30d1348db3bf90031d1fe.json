{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { tap } from 'rxjs';\nimport { ButtonModule } from 'primeng/button';\nimport { BaseFilePipe } from '../../../../shared/pipes/base-file.pipe';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../../services/api/services\";\nimport * as i2 from \"@angular/platform-browser\";\nimport * as i3 from \"../../../../../services/File.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"primeng/button\";\nconst _c0 = (a0, a1) => ({\n  \"btn-enable\": a0,\n  \"btn-disable\": a1\n});\nfunction StepVChoiceComponent_Conditional_0_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 7)(1, \"div\", 8)(2, \"span\", 9);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"button\", 10);\n    i0.ɵɵlistener(\"click\", function StepVChoiceComponent_Conditional_0_div_7_Template_button_click_4_listener() {\n      const houseReview_r3 = i0.ɵɵrestoreView(_r2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.gotoPickItem(houseReview_r3));\n    });\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const houseReview_r3 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", houseReview_r3.CReviewName, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", ctx_r3.handleStyleOption(houseReview_r3.CIsReview));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.hanldeTitleOption(houseReview_r3), \" \");\n  }\n}\nfunction StepVChoiceComponent_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"section\", 0)(1, \"div\", 1)(2, \"span\", 2);\n    i0.ɵɵtext(3, \" \\u6A19\\u6E96\\u5716\\u9762\\u5BE9\\u95B1\\u7E3D\\u89BD \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 3);\n    i0.ɵɵtext(5, \" \\u9EDE\\u64CA\\u9805\\u76EE\\u53EF\\u524D\\u5F80\\u5BE9\\u95B1 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 4);\n    i0.ɵɵtemplate(7, StepVChoiceComponent_Conditional_0_div_7_Template, 6, 3, \"div\", 5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 6);\n    i0.ɵɵlistener(\"click\", function StepVChoiceComponent_Conditional_0_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.next());\n    });\n    i0.ɵɵtext(9, \" \\u4E0B\\u4E00\\u6B65 \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.listHouseReview);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r3.checkDisable())(\"ngClass\", i0.ɵɵpureFunction2(3, _c0, !ctx_r3.checkDisable(), ctx_r3.checkDisable()));\n  }\n}\nfunction StepVChoiceComponent_Conditional_1_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"object\", 11)(1, \"iframe\", 13)(2, \"p\");\n    i0.ɵɵtext(3, \"\\u60A8\\u7684\\u700F\\u89BD\\u5668\\u7121\\u6CD5\\u986F\\u793A PDF\\u3002\\u8ACB \");\n    i0.ɵɵelementStart(4, \"a\", 14);\n    i0.ɵɵtext(5, \" \\u9EDE\\u64CA\\u6B64\\u8655\\u4E0B\\u8F09 PDF \\u6587\\u4EF6 \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"data\", ctx_r3.currentPdfUrl, i0.ɵɵsanitizeResourceUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", ctx_r3.currentPdfUrl, i0.ɵɵsanitizeResourceUrl)(\"title\", (ctx_r3.currentHouseReview.CReviewType == 1 ? \"\\u6A19\\u6E96\\u5716\" : \"\\u8A2D\\u5099\\u5716\") + \"PDF\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"href\", ctx_r3.currentPdfUrl, i0.ɵɵsanitizeUrl)(\"download\", (ctx_r3.currentHouseReview.CReviewType == 1 ? \"\\u6A19\\u6E96\\u5716\" : \"\\u8A2D\\u5099\\u5716\") + \".pdf\");\n  }\n}\nfunction StepVChoiceComponent_Conditional_1_Conditional_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"div\", 16);\n    i0.ɵɵtext(2, \"\\u6A19\\u6E96\\u5716\\u5716\\u9762\");\n    i0.ɵɵelement(3, \"br\");\n    i0.ɵɵtext(4, \" \\uFF08\\u5167\\u5D4CPDF\\uFF09\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction StepVChoiceComponent_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"section\", 0)(1, \"div\", 0)(2, \"span\", 2);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, StepVChoiceComponent_Conditional_1_Conditional_4_Template, 6, 5, \"object\", 11)(5, StepVChoiceComponent_Conditional_1_Conditional_5_Template, 5, 0);\n    i0.ɵɵelementStart(6, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function StepVChoiceComponent_Conditional_1_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.handleReview());\n    });\n    i0.ɵɵtext(7, \" \\u78BA\\u8A8D \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.currentHouseReview.CReviewType == 1 ? \"\\u6A19\\u6E96\\u5716\" : \"\\u8A2D\\u5099\\u5716\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(4, !!ctx_r3.currentHouseReview.CFileUrl ? 4 : 5);\n  }\n}\nexport class StepVChoiceComponent {\n  constructor(_houseService, sanitizer, _fileService) {\n    this._houseService = _houseService;\n    this.sanitizer = sanitizer;\n    this._fileService = _fileService;\n    this.listHouseReview = [];\n    this.nextEvent = new EventEmitter();\n    this.refreshHouseReview = new EventEmitter();\n    this.listHouseReviewChange = new EventEmitter();\n    this.isReviewing = false;\n    this.currentPdfUrl = null; // 快取當前 PDF 的安全 URL\n    // PDF 處理相關屬性\n    this.pdfBlobUrls = new Map(); // 儲存每個 PDF 的 blob URL\n    this.pdfDataUrls = new Map(); // 儲存每個 PDF 的 data URL\n    this.loadingPdfUrls = new Set(); // 追蹤正在載入的 PDF URL\n    this.baseFilePipe = new BaseFilePipe(this.sanitizer);\n  } // 添加安全 URL 處理方法\n  getSafePdfUrl(fileUrl) {\n    // 首先檢查是否已經有對應的 blob URL\n    const existingBlobUrl = this.pdfBlobUrls.get(fileUrl);\n    if (existingBlobUrl) {\n      return this.sanitizer.bypassSecurityTrustResourceUrl(existingBlobUrl);\n    }\n    // 檢查是否有 data URL\n    const existingDataUrl = this.pdfDataUrls.get(fileUrl);\n    if (existingDataUrl) {\n      return this.sanitizer.bypassSecurityTrustResourceUrl(existingDataUrl);\n    }\n    // 如果沒有快取的 URL，返回空字串或預設值，不觸發載入\n    return this.sanitizer.bypassSecurityTrustResourceUrl('');\n  } // 載入 PDF 文件並建立 blob URL\n  loadPdfFile(fileUrl, fileName) {\n    // 檢查是否正在載入或已經載入完成\n    if (this.loadingPdfUrls.has(fileUrl) || this.pdfBlobUrls.has(fileUrl)) {\n      return;\n    }\n    // 標記為正在載入\n    this.loadingPdfUrls.add(fileUrl);\n    this._fileService.getFile(fileUrl, fileName).subscribe(response => {\n      if (response.body) {\n        // 方法1: 創建 blob URL\n        const blobUrl = URL.createObjectURL(response.body);\n        this.pdfBlobUrls.set(fileUrl, blobUrl);\n        // 方法2: 轉換為 base64 data URL (更好的兼容性)\n        const reader = new FileReader();\n        reader.onload = () => {\n          const dataUrl = reader.result;\n          this.pdfDataUrls.set(fileUrl, dataUrl);\n          console.log('PDF Data URL 已準備就緒:', fileUrl);\n          // 如果這是當前顯示的 PDF，更新快取的 URL\n          if (this.currentHouseReview?.CFileUrl === fileUrl) {\n            this.updateCurrentPdfUrl();\n          }\n        };\n        reader.readAsDataURL(response.body);\n      }\n      console.log('PDF Blob URL 已準備就緒:', fileUrl, blobUrl);\n      // 移除載入狀態\n      this.loadingPdfUrls.delete(fileUrl);\n      // 如果這是當前顯示的 PDF，更新快取的 URL\n      if (this.currentHouseReview?.CFileUrl === fileUrl) {\n        this.updateCurrentPdfUrl();\n      }\n    }, error => {\n      console.error('取得 PDF 文件時發生錯誤:', fileUrl, error);\n      // 載入失敗時也要移除載入狀態\n      this.loadingPdfUrls.delete(fileUrl);\n    });\n  }\n  ngOnInit() {\n    // 如果沒有資料，觸發重新載入\n    if (!this.listHouseReview || this.listHouseReview.length === 0) {\n      this.refreshHouseReview.emit();\n    }\n  }\n  // 從檔案 URL 或名稱提取擴展名\n  getFileExtension(houseReview) {\n    let fileName = '';\n    // 優先從 CReviewName 提取檔名（因為它包含完整的檔案名稱和擴展名）\n    if (houseReview.CReviewName) {\n      fileName = houseReview.CReviewName.trim(); // 移除前後空白\n    } else if (houseReview.CFileUrl) {\n      const urlParts = houseReview.CFileUrl.split('/');\n      fileName = urlParts[urlParts.length - 1];\n    }\n    if (fileName && fileName.includes('.')) {\n      return fileName.toLowerCase().substring(fileName.lastIndexOf('.'));\n    }\n    return '';\n  }\n  // 判斷是否為 CAD 檔案\n  isCADFile(houseReview) {\n    const extension = this.getFileExtension(houseReview);\n    return ['.dwg', '.dxf', '.dwf'].includes(extension);\n  }\n  // 下載檔案\n  downloadFile(houseReview) {\n    if (!houseReview.CFileUrl || !houseReview.CReviewName) {\n      console.error('檔案 URL 或名稱不存在');\n      return;\n    }\n    this._fileService.getFile(houseReview.CFileUrl, houseReview.CReviewName).subscribe({\n      next: response => {\n        if (response.body) {\n          // 創建下載鏈接\n          const url = window.URL.createObjectURL(response.body);\n          const link = document.createElement('a');\n          link.href = url;\n          link.download = houseReview.CReviewName || 'download';\n          document.body.appendChild(link);\n          link.click();\n          document.body.removeChild(link);\n          window.URL.revokeObjectURL(url);\n        }\n      },\n      error: error => {\n        console.error('檔案下載失敗:', error);\n      }\n    });\n  }\n  gotoPickItem(houseReview) {\n    // 如果是 CAD 檔案，直接下載\n    if (this.isCADFile(houseReview)) {\n      this.downloadFile(houseReview);\n      return;\n    }\n    // PDF 檔案進入審閱頁面\n    this.currentHouseReview = houseReview;\n    this.isReviewing = true;\n    // 更新當前 PDF URL\n    this.updateCurrentPdfUrl();\n    // 當進入審閱頁面時，載入對應的 PDF 檔案\n    if (houseReview.CFileUrl && !this.pdfBlobUrls.has(houseReview.CFileUrl) && !this.loadingPdfUrls.has(houseReview.CFileUrl)) {\n      this.loadPdfFile(houseReview.CFileUrl, houseReview.CReviewName || 'document.pdf');\n    }\n  }\n  // 更新當前 PDF URL 的快取\n  updateCurrentPdfUrl() {\n    if (!this.currentHouseReview?.CFileUrl) {\n      this.currentPdfUrl = null;\n      return;\n    }\n    this.currentPdfUrl = this.getSafePdfUrl(this.currentHouseReview.CFileUrl);\n  }\n  handleStyleOption(isReview) {\n    if (isReview) {\n      return {\n        'background': 'linear-gradient(90deg, #AE9B66 0%, #B8A676 100%)',\n        'color': 'white'\n      };\n    }\n    return {\n      'backgroundColor': '#E5E3E1',\n      'color': '#3A4246B2'\n    };\n  }\n  // 根據檔案類型和審閱狀態決定按鈕文字\n  hanldeTitleOption(houseReview) {\n    const isCAD = this.isCADFile(houseReview);\n    if (isCAD) {\n      // CAD 檔案顯示下載按鈕\n      return '下載';\n    } else {\n      // PDF 檔案顯示審閱狀態\n      return houseReview.CIsReview ? '已審閱' : '未審閱';\n    }\n  }\n  next() {\n    this.nextEvent.emit();\n  }\n  handleReview() {\n    this._houseService.apiHouseUpdateHouseReviewPost$Json({\n      body: {\n        CHouseReviewID: this.currentHouseReview.CHouseReviewId\n      }\n    }).pipe(tap(() => this.refreshHouseReview.emit()), tap(res => {\n      if (res.StatusCode == 0) {\n        this.currentHouseReview = {};\n        this.isReviewing = false;\n      }\n    })).subscribe();\n  }\n  checkDisable() {\n    // 過濾出需要審閱的檔案（非 CAD 檔案）\n    const reviewableFiles = this.listHouseReview.filter(x => !this.isCADFile(x));\n    // 如果沒有需要審閱的檔案，則啟用下一步\n    if (reviewableFiles.length === 0) {\n      return false;\n    }\n    // 檢查所有需要審閱的檔案是否都已審閱\n    return !reviewableFiles.every(x => x.CIsReview);\n  }\n  ngOnDestroy() {\n    // 清理所有 blob URL 以避免記憶體洩漏\n    this.pdfBlobUrls.forEach(blobUrl => {\n      URL.revokeObjectURL(blobUrl);\n    });\n    this.pdfBlobUrls.clear();\n    this.pdfDataUrls.clear();\n    this.loadingPdfUrls.clear();\n  }\n  static #_ = this.ɵfac = function StepVChoiceComponent_Factory(t) {\n    return new (t || StepVChoiceComponent)(i0.ɵɵdirectiveInject(i1.HouseService), i0.ɵɵdirectiveInject(i2.DomSanitizer), i0.ɵɵdirectiveInject(i3.FileService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: StepVChoiceComponent,\n    selectors: [[\"app-step-v-choice\"]],\n    inputs: {\n      listHouseReview: \"listHouseReview\"\n    },\n    outputs: {\n      nextEvent: \"nextEvent\",\n      refreshHouseReview: \"refreshHouseReview\",\n      listHouseReviewChange: \"listHouseReviewChange\"\n    },\n    standalone: true,\n    features: [i0.ɵɵStandaloneFeature],\n    decls: 2,\n    vars: 1,\n    consts: [[1, \"flex\", \"flex-col\", \"justify-center\", \"items-center\", \"w-full\"], [1, \"flex\", \"flex-col\", \"text-center\"], [1, \"text-2xl\", \"text-black\"], [1, \"text-base\", \"mt-2\"], [1, \"flex\", \"flex-col\", \"lg:!w-[800px]\", \"w-full\", \"m-auto\", \"my-6\", \"px-4\"], [\"class\", \"flex lg:!flex-row flex-col\\n        sm:items-center items-start justify-between\\n        cursor-pointer bg-[#F3F1EA99] mb-4 rounded-lg\\n        py-4 lg:!px-[22px] px-4\", 4, \"ngFor\", \"ngForOf\"], [\"pButton\", \"\", 1, \"btn-next\", \"butn1\", \"flex\", \"justify-center\", \"items-center\", \"my-6\", \"w-[180px]\", \"h-[47px]\", 3, \"click\", \"disabled\", \"ngClass\"], [1, \"flex\", \"lg:!flex-row\", \"flex-col\", \"sm:items-center\", \"items-start\", \"justify-between\", \"cursor-pointer\", \"bg-[#F3F1EA99]\", \"mb-4\", \"rounded-lg\", \"py-4\", \"lg:!px-[22px]\", \"px-4\"], [1, \"w-full\", \"items-center\", \"sm:mx-0\", \"sm:pl-3\", \"mx-4\", \"mt-3\", \"sm:mt-1\"], [1, \"text-base\", \"block\", \"w-full\", \"text-left\", \"text-black\"], [\"pButton\", \"\", 1, \"flex\", \"justify-center\", \"text-white\", \"font-medium\", \"items-center\", \"rounded-3xl\", \"bg-gray-500\", \"p-2\", \"lg:!mx-0\", \"py-3\", \"lg:!w-[180px]\", \"w-full\", \"h-[47px]\", \"mt-3\", \"lg:!mt-0\", 3, \"click\", \"ngStyle\"], [\"type\", \"application/pdf\", \"width\", \"100%\", \"height\", \"500\", 1, \"mt-3\", \"w-full\", 2, \"border\", \"1px solid #ccc\", 3, \"data\"], [\"pButton\", \"\", 1, \"button2\", \"!w-48\", \"butn1\", \"flex\", \"justify-center\", \"items-center\", \"mt-4\", 3, \"click\"], [\"width\", \"100%\", \"height\", \"500\", 2, \"border\", \"1px solid #ccc\", 3, \"src\", \"title\"], [\"target\", \"_blank\", 3, \"href\", \"download\"], [1, \"w-full\", \"h-[40vh]\", \"bg-[#d9d9d9]\", \"flex\", \"justify-center\", \"items-center\"], [1, \"font-semibold\", \"text-lg\"]],\n    template: function StepVChoiceComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, StepVChoiceComponent_Conditional_0_Template, 10, 6, \"section\", 0)(1, StepVChoiceComponent_Conditional_1_Template, 8, 2);\n      }\n      if (rf & 2) {\n        i0.ɵɵconditional(0, !ctx.isReviewing ? 0 : 1);\n      }\n    },\n    dependencies: [CommonModule, i4.NgClass, i4.NgForOf, i4.NgStyle, ButtonModule, i5.ButtonDirective],\n    styles: [\"@charset \\\"UTF-8\\\";@keyframes _ngcontent-%COMP%_radioAppear{0%{transform:translate(-50%,-50%) scale(0);opacity:0}50%{transform:translate(-50%,-50%) scale(1.3);opacity:.8}to{transform:translate(-50%,-50%) scale(1);opacity:1}}@keyframes _ngcontent-%COMP%_radioRipple{0%{width:0;height:0;opacity:.6}to{width:44px;height:44px;opacity:0}}@keyframes _ngcontent-%COMP%_buttonPress{0%{transform:scale(1)}50%{transform:scale(.98)}to{transform:scale(1)}}@keyframes _ngcontent-%COMP%_fadeInUp{0%{opacity:0;transform:translateY(20px)}to{opacity:1;transform:translateY(0)}}@keyframes _ngcontent-%COMP%_slideInRight{0%{opacity:0;transform:translate(30px)}to{opacity:1;transform:translate(0)}}@keyframes _ngcontent-%COMP%_shimmer{0%{background-position:-200px 0}to{background-position:200px 0}}@keyframes _ngcontent-%COMP%_pulse{0%,to{transform:scale(1);opacity:1}50%{transform:scale(1.05);opacity:.8}}.animate-fade-in-up[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fadeInUp .6s ease-out}.animate-slide-in-right[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_slideInRight .4s ease-out}.animate-pulse[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_pulse 2s infinite}.animate-shimmer[_ngcontent-%COMP%]{background:linear-gradient(90deg,transparent,rgba(255,255,255,.4),transparent);background-size:200px 100%;animation:_ngcontent-%COMP%_shimmer 1.5s infinite}.hover-lift[_ngcontent-%COMP%]{transition:transform .3s ease,box-shadow .3s ease}.hover-lift[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 4px 12px #00000026}.hover-scale[_ngcontent-%COMP%]{transition:transform .3s ease}.hover-scale[_ngcontent-%COMP%]:hover{transform:scale(1.02)}.hover-glow[_ngcontent-%COMP%]{transition:box-shadow .3s ease}.hover-glow[_ngcontent-%COMP%]:hover{box-shadow:0 0 20px #b8a6764d}html[_ngcontent-%COMP%]{line-height:initial;-webkit-text-size-adjust:100%;width:100%;height:100%}body[_ngcontent-%COMP%]{margin:0;font-size:1rem;color:#23181599;font-family:Noto Sans TC!important;box-sizing:border-box;width:100%;height:auto;overflow:auto;overscroll-behavior:none;letter-spacing:.32px}html[_ngcontent-%COMP%], body[_ngcontent-%COMP%]{-webkit-text-size-adjust:none;text-size-adjust:none}main[_ngcontent-%COMP%]{display:block}h1[_ngcontent-%COMP%]{font-size:1.75rem;font-weight:400}hr[_ngcontent-%COMP%]{box-sizing:content-box;height:0;overflow:visible;margin:10px 0;border:none;border-top:1px solid #000}pre[_ngcontent-%COMP%]{font-family:monospace,monospace;font-size:1rem}a[_ngcontent-%COMP%]{background-color:transparent;text-decoration:none;-webkit-tap-highlight-color:transparent}abbr[title][_ngcontent-%COMP%]{border-bottom:none;text-decoration:underline;-webkit-text-decoration:underline dotted;text-decoration:underline dotted}b[_ngcontent-%COMP%]{font-family:Noto Sans TC;font-weight:500}strong[_ngcontent-%COMP%]{font-family:Noto Sans TC;font-weight:600}code[_ngcontent-%COMP%], kbd[_ngcontent-%COMP%], samp[_ngcontent-%COMP%]{font-family:monospace,monospace;font-size:1em}small[_ngcontent-%COMP%]{font-size:80%}sub[_ngcontent-%COMP%], sup[_ngcontent-%COMP%]{font-size:75%;line-height:0;position:relative;vertical-align:baseline}sub[_ngcontent-%COMP%]{bottom:-.25em}sup[_ngcontent-%COMP%]{top:-.5em}p[_ngcontent-%COMP%]{font-size:1rem}img[_ngcontent-%COMP%]{border-style:none;max-width:100%}button[_ngcontent-%COMP%], input[_ngcontent-%COMP%], optgroup[_ngcontent-%COMP%], select[_ngcontent-%COMP%], textarea[_ngcontent-%COMP%]{font-family:Noto Sans TC,\\\\5fae\\\\8edf\\\\6b63\\\\9ed1\\\\9ad4,Arial,sans-serif;font-size:1rem;line-height:1.15;margin:0;color:#2f2f2f;-webkit-tap-highlight-color:transparent;letter-spacing:2px}select[_ngcontent-%COMP%]:focus, input[_ngcontent-%COMP%]:focus, button[_ngcontent-%COMP%]:focus{outline:none}button[_ngcontent-%COMP%]{padding:0;background-color:transparent;border:none;cursor:pointer}button[_ngcontent-%COMP%], input[_ngcontent-%COMP%]{overflow:visible}button[_ngcontent-%COMP%], select[_ngcontent-%COMP%]{text-transform:none}button[_ngcontent-%COMP%], [type=button][_ngcontent-%COMP%], [type=reset][_ngcontent-%COMP%], [type=submit][_ngcontent-%COMP%]{-webkit-appearance:button}button[_ngcontent-%COMP%]::-moz-focus-inner, [type=button][_ngcontent-%COMP%]::-moz-focus-inner, [type=reset][_ngcontent-%COMP%]::-moz-focus-inner, [type=submit][_ngcontent-%COMP%]::-moz-focus-inner{border-style:none;padding:0}button[_ngcontent-%COMP%]:-moz-focusring, [type=button][_ngcontent-%COMP%]:-moz-focusring, [type=reset][_ngcontent-%COMP%]:-moz-focusring, [type=submit][_ngcontent-%COMP%]:-moz-focusring{outline:1px dotted ButtonText}fieldset[_ngcontent-%COMP%]{padding:.35em .75em .625em}div[role=tab][_ngcontent-%COMP%]{-webkit-tap-highlight-color:transparent}legend[_ngcontent-%COMP%]{box-sizing:border-box;color:inherit;display:table;max-width:100%;padding:0;white-space:normal}progress[_ngcontent-%COMP%]{vertical-align:baseline}textarea[_ngcontent-%COMP%]{overflow:auto}[type=checkbox][_ngcontent-%COMP%], [type=radio][_ngcontent-%COMP%]{box-sizing:border-box;padding:0}[type=number][_ngcontent-%COMP%]::-webkit-inner-spin-button, [type=number][_ngcontent-%COMP%]::-webkit-outer-spin-button{height:auto}[type=search][_ngcontent-%COMP%]{-webkit-appearance:textfield;outline-offset:-2px}[type=search][_ngcontent-%COMP%]::-webkit-search-decoration{-webkit-appearance:none}[_ngcontent-%COMP%]::-webkit-file-upload-button{-webkit-appearance:button;font:inherit}details[_ngcontent-%COMP%]{display:block}summary[_ngcontent-%COMP%]{display:list-item}template[_ngcontent-%COMP%]{display:none}[hidden][_ngcontent-%COMP%]{display:none}table[_ngcontent-%COMP%]{border-spacing:0px}ol[_ngcontent-%COMP%], ul[_ngcontent-%COMP%]{padding-left:0;margin-top:0}.form-check-input[_ngcontent-%COMP%]:focus{outline:none;box-shadow:none}.container[_ngcontent-%COMP%]{width:100%}@media (min-width: 640px){.container[_ngcontent-%COMP%]{max-width:640px}}@media (min-width: 768px){.container[_ngcontent-%COMP%]{max-width:768px}}@media (min-width: 1024px){.container[_ngcontent-%COMP%]{max-width:1024px}}@media (min-width: 1280px){.container[_ngcontent-%COMP%]{max-width:1280px}}@media (min-width: 1536px){.container[_ngcontent-%COMP%]{max-width:1536px}}.visible[_ngcontent-%COMP%]{visibility:visible}.collapse[_ngcontent-%COMP%]{visibility:collapse}.static[_ngcontent-%COMP%]{position:static}.fixed[_ngcontent-%COMP%]{position:fixed}.absolute[_ngcontent-%COMP%]{position:absolute}.relative[_ngcontent-%COMP%]{position:relative}.inset-0[_ngcontent-%COMP%]{inset:0}.inset-y-0[_ngcontent-%COMP%]{top:0;bottom:0}.bottom-0[_ngcontent-%COMP%]{bottom:0}.bottom-1[_ngcontent-%COMP%]{bottom:.25rem}.bottom-2[_ngcontent-%COMP%]{bottom:.5rem}.bottom-3[_ngcontent-%COMP%]{bottom:.75rem}.left-0[_ngcontent-%COMP%]{left:0}.left-1\\\\/2[_ngcontent-%COMP%]{left:50%}.left-2[_ngcontent-%COMP%]{left:.5rem}.left-3[_ngcontent-%COMP%]{left:.75rem}.right-0[_ngcontent-%COMP%]{right:0}.right-1[_ngcontent-%COMP%]{right:.25rem}.right-2[_ngcontent-%COMP%]{right:.5rem}.right-3[_ngcontent-%COMP%]{right:.75rem}.top-1[_ngcontent-%COMP%]{top:.25rem}.top-1\\\\/2[_ngcontent-%COMP%]{top:50%}.top-2[_ngcontent-%COMP%]{top:.5rem}.top-3[_ngcontent-%COMP%]{top:.75rem}.z-10[_ngcontent-%COMP%]{z-index:10}.z-50[_ngcontent-%COMP%]{z-index:50}.m-0[_ngcontent-%COMP%]{margin:0}.m-2[_ngcontent-%COMP%]{margin:.5rem}.m-auto[_ngcontent-%COMP%]{margin:auto}.mx-3[_ngcontent-%COMP%]{margin-left:.75rem;margin-right:.75rem}.mx-4[_ngcontent-%COMP%]{margin-left:1rem;margin-right:1rem}.mx-5[_ngcontent-%COMP%]{margin-left:1.25rem;margin-right:1.25rem}.mx-\\\\__ph-0__[_ngcontent-%COMP%]{margin-left:20%;margin-right:20%}.my-12[_ngcontent-%COMP%]{margin-top:3rem;margin-bottom:3rem}.my-2[_ngcontent-%COMP%]{margin-top:.5rem;margin-bottom:.5rem}.my-4[_ngcontent-%COMP%]{margin-top:1rem;margin-bottom:1rem}.my-6[_ngcontent-%COMP%]{margin-top:1.5rem;margin-bottom:1.5rem}.\\\\!mt-0[_ngcontent-%COMP%]{margin-top:0!important}.mb-2[_ngcontent-%COMP%]{margin-bottom:.5rem}.mb-3[_ngcontent-%COMP%]{margin-bottom:.75rem}.mb-4[_ngcontent-%COMP%]{margin-bottom:1rem}.mb-6[_ngcontent-%COMP%]{margin-bottom:1.5rem}.ml-1[_ngcontent-%COMP%]{margin-left:.25rem}.ml-2[_ngcontent-%COMP%]{margin-left:.5rem}.mr-1[_ngcontent-%COMP%]{margin-right:.25rem}.mr-2[_ngcontent-%COMP%]{margin-right:.5rem}.mr-3[_ngcontent-%COMP%]{margin-right:.75rem}.mr-4[_ngcontent-%COMP%]{margin-right:1rem}.mr-5[_ngcontent-%COMP%]{margin-right:1.25rem}.mr-8[_ngcontent-%COMP%]{margin-right:2rem}.mt-1[_ngcontent-%COMP%]{margin-top:.25rem}.mt-10[_ngcontent-%COMP%]{margin-top:2.5rem}.mt-14[_ngcontent-%COMP%]{margin-top:3.5rem}.mt-2[_ngcontent-%COMP%]{margin-top:.5rem}.mt-3[_ngcontent-%COMP%]{margin-top:.75rem}.mt-4[_ngcontent-%COMP%]{margin-top:1rem}.mt-5[_ngcontent-%COMP%]{margin-top:1.25rem}.mt-6[_ngcontent-%COMP%]{margin-top:1.5rem}.mt-7[_ngcontent-%COMP%]{margin-top:1.75rem}.mt-8[_ngcontent-%COMP%]{margin-top:2rem}.mt-\\\\__ph-0__[_ngcontent-%COMP%]{margin-top:16px}.block[_ngcontent-%COMP%]{display:block}.flex[_ngcontent-%COMP%]{display:flex}.table[_ngcontent-%COMP%]{display:table}.table-row[_ngcontent-%COMP%]{display:table-row}.grid[_ngcontent-%COMP%]{display:grid}.hidden[_ngcontent-%COMP%]{display:none}.\\\\!h-\\\\__ph-0__[_ngcontent-%COMP%]{height:107px!important}.\\\\!h-full[_ngcontent-%COMP%]{height:100%!important}.h-10[_ngcontent-%COMP%]{height:2.5rem}.h-16[_ngcontent-%COMP%]{height:4rem}.h-28[_ngcontent-%COMP%]{height:7rem}.h-32[_ngcontent-%COMP%]{height:8rem}.h-6[_ngcontent-%COMP%]{height:1.5rem}.h-8[_ngcontent-%COMP%]{height:2rem}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:100px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:145px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:280px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:40vh}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:47px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:480px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:4px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:50px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:55px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:665px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:80px}.h-auto[_ngcontent-%COMP%]{height:auto}.h-fit[_ngcontent-%COMP%]{height:-moz-fit-content;height:fit-content}.max-h-\\\\__ph-0__[_ngcontent-%COMP%]{max-height:200px}.max-h-\\\\__ph-0__[_ngcontent-%COMP%]{max-height:500px}.max-h-full[_ngcontent-%COMP%]{max-height:100%}.max-h-screen[_ngcontent-%COMP%]{max-height:100vh}.\\\\!min-h-\\\\__ph-0__[_ngcontent-%COMP%]{min-height:60vh!important}.\\\\!w-40[_ngcontent-%COMP%]{width:10rem!important}.\\\\!w-48[_ngcontent-%COMP%]{width:12rem!important}.\\\\!w-\\\\__ph-0__[_ngcontent-%COMP%]{width:107px!important}.\\\\!w-\\\\__ph-0__[_ngcontent-%COMP%]{width:120px!important}.\\\\!w-full[_ngcontent-%COMP%]{width:100%!important}.w-1\\\\/3[_ngcontent-%COMP%]{width:33.333333%}.w-10[_ngcontent-%COMP%]{width:2.5rem}.w-16[_ngcontent-%COMP%]{width:4rem}.w-2\\\\/3[_ngcontent-%COMP%]{width:66.666667%}.w-28[_ngcontent-%COMP%]{width:7rem}.w-6[_ngcontent-%COMP%]{width:1.5rem}.w-7\\\\/12[_ngcontent-%COMP%]{width:58.333333%}.w-8[_ngcontent-%COMP%]{width:2rem}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:100%}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:150px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:160px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:180px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:30rem}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:400px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:45%}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:485px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:50%}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:550px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:660px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:760px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:80%}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:80px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:968px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:97%}.w-full[_ngcontent-%COMP%]{width:100%}.\\\\!max-w-full[_ngcontent-%COMP%]{max-width:100%!important}.max-w-4xl[_ngcontent-%COMP%]{max-width:56rem}.max-w-\\\\__ph-0__[_ngcontent-%COMP%]{max-width:1216px}.max-w-full[_ngcontent-%COMP%]{max-width:100%}.flex-1[_ngcontent-%COMP%]{flex:1 1 0%}.flex-shrink[_ngcontent-%COMP%]{flex-shrink:1}.flex-shrink-0[_ngcontent-%COMP%]{flex-shrink:0}.grow[_ngcontent-%COMP%]{flex-grow:1}.border-collapse[_ngcontent-%COMP%]{border-collapse:collapse}.-translate-x-1\\\\/2[_ngcontent-%COMP%]{--tw-translate-x: -50%;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.-translate-y-1\\\\/2[_ngcontent-%COMP%]{--tw-translate-y: -50%;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.transform[_ngcontent-%COMP%]{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.grid-cols-1[_ngcontent-%COMP%]{grid-template-columns:repeat(1,minmax(0,1fr))}.grid-cols-10[_ngcontent-%COMP%]{grid-template-columns:repeat(10,minmax(0,1fr))}.grid-cols-2[_ngcontent-%COMP%]{grid-template-columns:repeat(2,minmax(0,1fr))}.grid-cols-3[_ngcontent-%COMP%]{grid-template-columns:repeat(3,minmax(0,1fr))}.grid-cols-4[_ngcontent-%COMP%]{grid-template-columns:repeat(4,minmax(0,1fr))}.flex-row[_ngcontent-%COMP%]{flex-direction:row}.flex-col[_ngcontent-%COMP%]{flex-direction:column}.flex-wrap[_ngcontent-%COMP%]{flex-wrap:wrap}.items-start[_ngcontent-%COMP%]{align-items:flex-start}.items-center[_ngcontent-%COMP%]{align-items:center}.justify-start[_ngcontent-%COMP%]{justify-content:flex-start}.justify-end[_ngcontent-%COMP%]{justify-content:flex-end}.justify-center[_ngcontent-%COMP%]{justify-content:center}.justify-between[_ngcontent-%COMP%]{justify-content:space-between}.gap-2[_ngcontent-%COMP%]{gap:.5rem}.gap-3[_ngcontent-%COMP%]{gap:.75rem}.gap-4[_ngcontent-%COMP%]{gap:1rem}.gap-x-1[_ngcontent-%COMP%]{column-gap:.25rem}.self-center[_ngcontent-%COMP%]{align-self:center}.overflow-hidden[_ngcontent-%COMP%]{overflow:hidden}.overflow-y-scroll[_ngcontent-%COMP%]{overflow-y:scroll}.break-words[_ngcontent-%COMP%]{overflow-wrap:break-word}.rounded[_ngcontent-%COMP%]{border-radius:.25rem}.rounded-3xl[_ngcontent-%COMP%]{border-radius:1.5rem}.rounded-full[_ngcontent-%COMP%]{border-radius:9999px}.rounded-lg[_ngcontent-%COMP%]{border-radius:.5rem}.rounded-md[_ngcontent-%COMP%]{border-radius:.375rem}.rounded-b[_ngcontent-%COMP%]{border-bottom-right-radius:.25rem;border-bottom-left-radius:.25rem}.border[_ngcontent-%COMP%]{border-width:1px}.border-2[_ngcontent-%COMP%]{border-width:2px}.border-solid[_ngcontent-%COMP%]{border-style:solid}.border-blue-400[_ngcontent-%COMP%]{--tw-border-opacity: 1;border-color:rgb(96 165 250 / var(--tw-border-opacity))}.border-blue-500[_ngcontent-%COMP%]{--tw-border-opacity: 1;border-color:rgb(59 130 246 / var(--tw-border-opacity))}.border-gray-300[_ngcontent-%COMP%]{--tw-border-opacity: 1;border-color:rgb(209 213 219 / var(--tw-border-opacity))}.bg-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-bg-opacity: 1;background-color:rgb(0 143 199 / var(--tw-bg-opacity))}.bg-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-bg-opacity: 1;background-color:rgb(184 166 118 / var(--tw-bg-opacity))}.bg-\\\\__ph-0__[_ngcontent-%COMP%]{background-color:#f3f1ea80}.bg-\\\\__ph-0__[_ngcontent-%COMP%]{background-color:#f3f1ea99}.bg-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-bg-opacity: 1;background-color:rgb(217 217 217 / var(--tw-bg-opacity))}.bg-black[_ngcontent-%COMP%]{--tw-bg-opacity: 1;background-color:rgb(0 0 0 / var(--tw-bg-opacity))}.bg-gray-500[_ngcontent-%COMP%]{--tw-bg-opacity: 1;background-color:rgb(107 114 128 / var(--tw-bg-opacity))}.bg-white[_ngcontent-%COMP%]{--tw-bg-opacity: 1;background-color:rgb(255 255 255 / var(--tw-bg-opacity))}.bg-opacity-50[_ngcontent-%COMP%]{--tw-bg-opacity: .5}.bg-opacity-70[_ngcontent-%COMP%]{--tw-bg-opacity: .7}.bg-opacity-75[_ngcontent-%COMP%]{--tw-bg-opacity: .75}.bg-opacity-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-bg-opacity: .04}.object-contain[_ngcontent-%COMP%]{object-fit:contain}.object-cover[_ngcontent-%COMP%]{object-fit:cover}.p-2[_ngcontent-%COMP%]{padding:.5rem}.p-4[_ngcontent-%COMP%]{padding:1rem}.p-\\\\__ph-0__[_ngcontent-%COMP%]{padding:16px}.px-1[_ngcontent-%COMP%]{padding-left:.25rem;padding-right:.25rem}.px-2[_ngcontent-%COMP%]{padding-left:.5rem;padding-right:.5rem}.px-3[_ngcontent-%COMP%]{padding-left:.75rem;padding-right:.75rem}.px-4[_ngcontent-%COMP%]{padding-left:1rem;padding-right:1rem}.px-6[_ngcontent-%COMP%]{padding-left:1.5rem;padding-right:1.5rem}.py-0[_ngcontent-%COMP%]{padding-top:0;padding-bottom:0}.py-0\\\\.5[_ngcontent-%COMP%]{padding-top:.125rem;padding-bottom:.125rem}.py-1[_ngcontent-%COMP%]{padding-top:.25rem;padding-bottom:.25rem}.py-2[_ngcontent-%COMP%]{padding-top:.5rem;padding-bottom:.5rem}.py-3[_ngcontent-%COMP%]{padding-top:.75rem;padding-bottom:.75rem}.py-4[_ngcontent-%COMP%]{padding-top:1rem;padding-bottom:1rem}.pb-4[_ngcontent-%COMP%]{padding-bottom:1rem}.pb-5[_ngcontent-%COMP%]{padding-bottom:1.25rem}.pb-8[_ngcontent-%COMP%]{padding-bottom:2rem}.pl-4[_ngcontent-%COMP%]{padding-left:1rem}.pl-6[_ngcontent-%COMP%]{padding-left:1.5rem}.pr-1[_ngcontent-%COMP%]{padding-right:.25rem}.pr-3[_ngcontent-%COMP%]{padding-right:.75rem}.pr-4[_ngcontent-%COMP%]{padding-right:1rem}.text-left[_ngcontent-%COMP%]{text-align:left}.\\\\!text-center[_ngcontent-%COMP%]{text-align:center!important}.text-center[_ngcontent-%COMP%]{text-align:center}.text-right[_ngcontent-%COMP%]{text-align:right}.text-2xl[_ngcontent-%COMP%]{font-size:1.5rem;line-height:2rem}.text-3xl[_ngcontent-%COMP%]{font-size:1.875rem;line-height:2.25rem}.text-base[_ngcontent-%COMP%]{font-size:1rem;line-height:1.5rem}.text-lg[_ngcontent-%COMP%]{font-size:1.125rem;line-height:1.75rem}.text-sm[_ngcontent-%COMP%]{font-size:.875rem;line-height:1.25rem}.text-xl[_ngcontent-%COMP%]{font-size:1.25rem;line-height:1.75rem}.text-xs[_ngcontent-%COMP%]{font-size:.75rem;line-height:1rem}.font-bold[_ngcontent-%COMP%]{font-weight:700}.font-light[_ngcontent-%COMP%]{font-weight:300}.font-medium[_ngcontent-%COMP%]{font-weight:500}.font-normal[_ngcontent-%COMP%]{font-weight:400}.font-semibold[_ngcontent-%COMP%]{font-weight:600}.leading-relaxed[_ngcontent-%COMP%]{line-height:1.625}.tracking-wider[_ngcontent-%COMP%]{letter-spacing:.05em}.text-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(0 143 199 / var(--tw-text-opacity))}.text-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(35 24 21 / var(--tw-text-opacity))}.text-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(51 51 51 / var(--tw-text-opacity))}.text-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(182 152 103 / var(--tw-text-opacity))}.text-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(184 166 118 / var(--tw-text-opacity))}.text-black[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(0 0 0 / var(--tw-text-opacity))}.text-blue-400[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(96 165 250 / var(--tw-text-opacity))}.text-gray-500[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(107 114 128 / var(--tw-text-opacity))}.text-gray-600[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(75 85 99 / var(--tw-text-opacity))}.text-stone-600[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(87 83 78 / var(--tw-text-opacity))}.text-stone-900[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(28 25 23 / var(--tw-text-opacity))}.text-white[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(255 255 255 / var(--tw-text-opacity))}.opacity-50[_ngcontent-%COMP%]{opacity:.5}.shadow[_ngcontent-%COMP%]{--tw-shadow: 0 1px 3px 0 rgb(0 0 0 / .1), 0 1px 2px -1px rgb(0 0 0 / .1);--tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.grayscale[_ngcontent-%COMP%]{--tw-grayscale: grayscale(100%);filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)}.filter[_ngcontent-%COMP%]{filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)}.transition[_ngcontent-%COMP%]{transition-property:color,background-color,border-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,-webkit-backdrop-filter;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,backdrop-filter;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,backdrop-filter,-webkit-backdrop-filter;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.15s}.transition-all[_ngcontent-%COMP%]{transition-property:all;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.15s}.transition-shadow[_ngcontent-%COMP%]{transition-property:box-shadow;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.15s}.duration-200[_ngcontent-%COMP%]{transition-duration:.2s}.ease-in-out[_ngcontent-%COMP%]{transition-timing-function:cubic-bezier(.4,0,.2,1)}.content[_ngcontent-%COMP%]{width:100%}@media screen and (max-width: 1024px){.content[_ngcontent-%COMP%]{width:100%;padding:0 16px 50px}}.pc[_ngcontent-%COMP%], .inpc[_ngcontent-%COMP%]{display:initial}@media screen and (max-width: 1024px){.inpc[_ngcontent-%COMP%]{display:none}}.flat[_ngcontent-%COMP%]{display:none}@media screen and (max-width: 1024px){.flat[_ngcontent-%COMP%]{display:initial}}.mb[_ngcontent-%COMP%]{display:none}@media screen and (max-width: 912px){.mb[_ngcontent-%COMP%]{display:initial}}.cursor-pointer[_ngcontent-%COMP%]{cursor:pointer}.wrapper[_ngcontent-%COMP%]{display:flex;justify-content:center}.wrapper[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]{position:relative}.wrapper[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]:after{content:\\\"\\\";position:absolute;background-image:url(/assets/background/background2.png);width:180px;height:150px;background-size:unset;background-position:left;z-index:-1;right:0;bottom:20px}.button1[_ngcontent-%COMP%]{width:200px;height:47px;border:1px solid #CDCDCD;color:#3a4246;border-radius:28px;background-origin:border-box;background-clip:content-box,border-box;transition:all .3s ease}.button1[_ngcontent-%COMP%]:hover{border-color:#b8a676;color:#ae9b66;background-color:#b8a6760d}.button1[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#b8a676;transition:color .3s ease}.button1[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{color:#ae9b66}@media screen and (max-width: 912px){.button1[_ngcontent-%COMP%]{width:100%}}.button2[_ngcontent-%COMP%]{width:200px;padding:12px 24px;height:47px;color:#fff;background:linear-gradient(90deg,#ae9b66,#b8a676);border-radius:24px;box-shadow:0 2px 8px #ae9b6633;transition:all .3s ease}.button2[_ngcontent-%COMP%]:hover{background:linear-gradient(90deg,#9b8a5a,#ae9b66);transform:translateY(-1px);box-shadow:0 4px 12px #ae9b664d}.button2[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#fff}@media screen and (max-width: 912px){.button2[_ngcontent-%COMP%]{width:100%}}.button3[_ngcontent-%COMP%]{width:180px;padding:12px 24px;height:47px;color:#fff;background:linear-gradient(90deg,#ae9b66,#b8a676);border-radius:24px;box-shadow:0 2px 8px #ae9b6633;transition:all .3s ease}.button3[_ngcontent-%COMP%]:hover{background:linear-gradient(90deg,#9b8a5a,#ae9b66);transform:translateY(-1px);box-shadow:0 4px 12px #ae9b664d}.button3[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#fff}@media screen and (max-width: 912px){.button3[_ngcontent-%COMP%]{width:100%}}.button4[_ngcontent-%COMP%]{width:180px;padding:12px 24px;height:47px;color:#fff;background:#979797;border-radius:24px;transition:all .3s ease}.button4[_ngcontent-%COMP%]:hover{background:#7e7e7e}.button4[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#fff}@media screen and (max-width: 912px){.button4[_ngcontent-%COMP%]{width:100%}}.checkbox-zone[_ngcontent-%COMP%]{padding:2px;border-radius:2px;position:relative;background:linear-gradient(90deg,#ae9b66,#b8a676);box-shadow:0 1px 4px #ae9b664d}.avoid-resize-img[_ngcontent-%COMP%]{max-width:none}.fit-size[_ngcontent-%COMP%]{height:inherit;width:inherit;object-fit:cover;object-position:center}.p-calendar[_ngcontent-%COMP%]{width:100%}.p-calendar[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{border-bottom:1px solid rgba(144,150,157,.4);border-radius:0}.p-calendar[_ngcontent-%COMP%]   .p-inputtext[_ngcontent-%COMP%]{border:none;border-radius:0;border-bottom:1px solid rgba(144,150,157,.4);padding:12.5px 0;color:#23181599}@media screen and (max-width: 912px){.p-calendar[_ngcontent-%COMP%]   .p-inputtext[_ngcontent-%COMP%]{background:transparent}}.p-dropdown[_ngcontent-%COMP%]{border:none;width:100%}.p-dropdown[_ngcontent-%COMP%]   .p-inputtext[_ngcontent-%COMP%]{border:none;border-radius:0;border-bottom:1px solid rgba(144,150,157,.4);padding:12.5px 0;color:#231815}@media screen and (max-width: 912px){.p-dropdown[_ngcontent-%COMP%]   .p-inputtext[_ngcontent-%COMP%]{background:transparent}}.p-dropdown[_ngcontent-%COMP%]   .p-dropdown-trigger[_ngcontent-%COMP%]{border-radius:0;border-bottom:1px solid rgba(144,150,157,.4)}.p-dropdown[_ngcontent-%COMP%]:not(.p-disabled).p-focus{box-shadow:none}.p-radiobutton[_ngcontent-%COMP%]{position:relative;display:inline-flex;align-items:center}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box[_ngcontent-%COMP%]{width:22px;height:22px;border:2px solid #CDCDCD;background-color:#fff;border-radius:50%;transition:all .3s cubic-bezier(.25,.46,.45,.94);position:relative;box-shadow:0 1px 3px #ae9b661a;cursor:pointer}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box[_ngcontent-%COMP%]:hover{border-color:#b8a676;background:radial-gradient(circle at center,#b8a67614,#b8a67605);transform:scale(1.05);box-shadow:0 2px 8px #ae9b6626}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box[_ngcontent-%COMP%]:focus{outline:none;box-shadow:0 0 0 3px #b8a67640,0 2px 8px #ae9b6633;border-color:#a39460}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box[_ngcontent-%COMP%]:active{transform:scale(.98)}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box.p-highlight[_ngcontent-%COMP%]{background:linear-gradient(90deg,#ae9b66,#b8a676);border-color:#ae9b66;box-shadow:0 2px 12px #ae9b6640,inset 0 1px 2px #fff3}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box.p-highlight[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:50%;left:50%;width:8px;height:8px;background:radial-gradient(circle,#fff,#ffffffe6);border-radius:50%;transform:translate(-50%,-50%) scale(0);box-shadow:0 1px 2px #0003;animation:_ngcontent-%COMP%_radioAppear .2s ease forwards}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box.p-highlight[_ngcontent-%COMP%]:hover{background:linear-gradient(90deg,#9b8a5a,#ae9b66);border-color:#9b8a5a;transform:scale(1.05);box-shadow:0 3px 15px #ae9b6659,inset 0 1px 2px #ffffff40}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box.p-highlight[_ngcontent-%COMP%]:focus{box-shadow:0 0 0 3px #b8a6764d,0 3px 15px #ae9b6659}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-icon[_ngcontent-%COMP%]{display:none}@keyframes _ngcontent-%COMP%_radioAppear{0%{transform:translate(-50%,-50%) scale(0);opacity:0}50%{transform:translate(-50%,-50%) scale(1.2);opacity:.8}to{transform:translate(-50%,-50%) scale(1);opacity:1}}@keyframes _ngcontent-%COMP%_radioRipple{0%{transform:translate(-50%,-50%) scale(0);opacity:.6}to{transform:translate(-50%,-50%) scale(4);opacity:0}}.p-panel[_ngcontent-%COMP%]   .p-panel-header[_ngcontent-%COMP%]{background-color:#fff;color:#846a52;font-size:16px;border:1px solid #E6F0F3;border-radius:6px;padding:16px 32px}.p-panel-expanded[_ngcontent-%COMP%]   .p-panel-header[_ngcontent-%COMP%]{padding:16px 32px 8px;border-bottom:none!important;border-bottom-right-radius:0;border-bottom-left-radius:0}@media screen and (max-width: 912px){.p-panel-expanded[_ngcontent-%COMP%]   .p-panel-header[_ngcontent-%COMP%]{padding:16px 16px 8px}}.p-panel[_ngcontent-%COMP%]   .p-panel-header[_ngcontent-%COMP%]   .p-panel-header-icon[_ngcontent-%COMP%]{transform:rotate(-180deg)!important;transition:transform .3s linear}.p-panel-expanded[_ngcontent-%COMP%]   .p-panel-header[_ngcontent-%COMP%]   .p-panel-header-icon[_ngcontent-%COMP%]{transform:rotate(0)!important;transition:transform .3s linear}.p-panel[_ngcontent-%COMP%]   .p-panel-header[_ngcontent-%COMP%]   .p-panel-header-icon[_ngcontent-%COMP%]:focus{box-shadow:none}.p-panel[_ngcontent-%COMP%]   .p-panel-content[_ngcontent-%COMP%]{padding:0 32px 16px}@media screen and (max-width: 912px){.p-panel[_ngcontent-%COMP%]   .p-panel-content[_ngcontent-%COMP%]{padding:0 16px 16px}}.p-panel[_ngcontent-%COMP%]   .p-panel-header[_ngcontent-%COMP%]   .p-panel-header-icon[_ngcontent-%COMP%]{width:24px;height:24px}.p-dialog-mask[_ngcontent-%COMP%]{width:100%;height:100%;background-color:#0006;top:0;z-index:20;pointer-events:all!important}.p-dialog[_ngcontent-%COMP%]   .p-dialog-header[_ngcontent-%COMP%]{padding:32px 24px 16px;color:#231815;font-size:24px;text-align:center;display:flex;justify-content:center}.p-dialog[_ngcontent-%COMP%]   .p-dialog-content[_ngcontent-%COMP%]{padding:32px 24px 0;background:linear-gradient(180deg,#f3f1ea,#fff)}.p-dialog[_ngcontent-%COMP%]   .p-dialog-header[_ngcontent-%COMP%]   .p-dialog-header-icon[_ngcontent-%COMP%]{display:none}.p-dialog[_ngcontent-%COMP%]   .p-dialog-footer[_ngcontent-%COMP%]{padding:24px 24px 40px;display:flex;justify-content:center}.p-checkbox[_ngcontent-%COMP%]{width:18px;height:18px;display:inline-flex;align-items:center}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box[_ngcontent-%COMP%]{width:18px;height:18px;border:2px solid #CDCDCD;border-radius:3px;transition:.3s ease;background:#fff;position:relative;display:flex;align-items:center;justify-content:center}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box[_ngcontent-%COMP%]:hover{border-color:#b8a676}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box[_ngcontent-%COMP%]:focus{outline:none;border-color:#b8a676;box-shadow:0 0 0 2px #b8a67633}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box.p-highlight[_ngcontent-%COMP%]{background:#b8a676!important;border-color:#b8a676!important}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box[_ngcontent-%COMP%]   .p-checkbox-icon[_ngcontent-%COMP%]{color:#fff!important;font-size:12px;width:12px;height:12px;display:flex;align-items:center;justify-content:center}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box.p-highlight[_ngcontent-%COMP%]   .p-checkbox-icon[_ngcontent-%COMP%]{transform:scale(1);opacity:1}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box[_ngcontent-%COMP%]:not(.p-highlight)   .p-checkbox-icon[_ngcontent-%COMP%]{transform:scale(0);opacity:0}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box.p-highlight[_ngcontent-%COMP%]   .p-checkbox-icon[_ngcontent-%COMP%]:before{content:\\\"\\\\e915\\\";font-family:primeicons;font-weight:400;font-style:normal;font-size:12px;display:inline-block}.p-checkbox-label[_ngcontent-%COMP%]{margin-left:8px;cursor:pointer;-webkit-user-select:none;user-select:none}.input[_ngcontent-%COMP%]{width:100%;border:none;padding:12.5px 0;border-bottom:1px solid rgba(144,150,157,.4);border-radius:0}[_ngcontent-%COMP%]::-webkit-scrollbar{width:4px;height:4px}[_ngcontent-%COMP%]::-webkit-scrollbar-button{background:transparent;border-radius:2px}[_ngcontent-%COMP%]::-webkit-scrollbar-track-piece{background:transparent}[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{border-radius:4px;background-color:#90969d33}[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:transparent}.p-radiobutton-label[_ngcontent-%COMP%]{color:#231815!important;font-weight:400;cursor:pointer;transition:all .3s ease}.p-radiobutton-label[_ngcontent-%COMP%]:hover{color:#ae9b66!important}label[for*=requirement_][_ngcontent-%COMP%]{color:#231815!important;font-weight:400}label[for*=requirement_][_ngcontent-%COMP%]:hover{color:#ae9b66!important}input[type=radio][_ngcontent-%COMP%]:checked + label[_ngcontent-%COMP%], .p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box.p-highlight[_ngcontent-%COMP%] + label[_ngcontent-%COMP%]{color:#ae9b66!important;font-weight:600}.btn-enable[_ngcontent-%COMP%]{background:linear-gradient(90deg,#ae9b66,#b8a676);box-shadow:0 2px 8px #ae9b6633;transition:all .3s ease}.btn-enable[_ngcontent-%COMP%]:hover{background:linear-gradient(90deg,#9b8a5a,#ae9b66);transform:translateY(-1px);box-shadow:0 4px 12px #ae9b664d}.btn-disable[_ngcontent-%COMP%]{background-color:#979797!important;color:#231815!important;transition:all .3s ease}.btn-next[_ngcontent-%COMP%]{padding:12px 24px;color:#fff;border-radius:24px;transition:all .3s ease}.hover\\\\:bg-opacity-75[_ngcontent-%COMP%]:hover{--tw-bg-opacity: .75}.hover\\\\:shadow-lg[_ngcontent-%COMP%]:hover{--tw-shadow: 0 10px 15px -3px rgb(0 0 0 / .1), 0 4px 6px -4px rgb(0 0 0 / .1);--tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}@media not all and (min-width: 1280px){.max-xl\\\\:grid-cols-6[_ngcontent-%COMP%]{grid-template-columns:repeat(6,minmax(0,1fr))}}@media not all and (min-width: 1024px){.max-lg\\\\:bottom-12[_ngcontent-%COMP%]{bottom:3rem}.max-lg\\\\:w-full[_ngcontent-%COMP%]{width:100%}.max-lg\\\\:grid-cols-4[_ngcontent-%COMP%]{grid-template-columns:repeat(4,minmax(0,1fr))}}@media not all and (min-width: 768px){.max-md\\\\:bottom-14[_ngcontent-%COMP%]{bottom:3.5rem}.max-md\\\\:my-4[_ngcontent-%COMP%]{margin-top:1rem;margin-bottom:1rem}.max-md\\\\:ml-0[_ngcontent-%COMP%]{margin-left:0}.max-md\\\\:mt-10[_ngcontent-%COMP%]{margin-top:2.5rem}.max-md\\\\:mt-5[_ngcontent-%COMP%]{margin-top:1.25rem}.max-md\\\\:hidden[_ngcontent-%COMP%]{display:none}.max-md\\\\:h-\\\\__ph-0__[_ngcontent-%COMP%]{height:65%}.max-md\\\\:\\\\!w-\\\\__ph-0__[_ngcontent-%COMP%]{width:450px!important}.max-md\\\\:w-\\\\__ph-0__[_ngcontent-%COMP%]{width:500px}.max-md\\\\:w-full[_ngcontent-%COMP%]{width:100%}.max-md\\\\:max-w-full[_ngcontent-%COMP%]{max-width:100%}.max-md\\\\:flex-col[_ngcontent-%COMP%]{flex-direction:column}.max-md\\\\:flex-col-reverse[_ngcontent-%COMP%]{flex-direction:column-reverse}.max-md\\\\:flex-wrap[_ngcontent-%COMP%]{flex-wrap:wrap}.max-md\\\\:items-start[_ngcontent-%COMP%]{align-items:flex-start}}@media not all and (min-width: 640px){.max-sm\\\\:bottom-10[_ngcontent-%COMP%]{bottom:2.5rem}.max-sm\\\\:ml-8[_ngcontent-%COMP%]{margin-left:2rem}.max-sm\\\\:block[_ngcontent-%COMP%]{display:block}.max-sm\\\\:\\\\!w-\\\\__ph-0__[_ngcontent-%COMP%]{width:340px!important}.max-sm\\\\:w-\\\\__ph-0__[_ngcontent-%COMP%]{width:380px}.max-sm\\\\:\\\\!grid-cols-2[_ngcontent-%COMP%]{grid-template-columns:repeat(2,minmax(0,1fr))!important}.max-sm\\\\:\\\\!justify-between[_ngcontent-%COMP%]{justify-content:space-between!important}.max-sm\\\\:gap-0[_ngcontent-%COMP%]{gap:0px}.max-sm\\\\:px-0[_ngcontent-%COMP%]{padding-left:0;padding-right:0}.max-sm\\\\:pl-0[_ngcontent-%COMP%]{padding-left:0}.max-sm\\\\:text-base[_ngcontent-%COMP%]{font-size:1rem;line-height:1.5rem}}@media (min-width: 640px){.sm\\\\:mx-0[_ngcontent-%COMP%]{margin-left:0;margin-right:0}.sm\\\\:mb-0[_ngcontent-%COMP%]{margin-bottom:0}.sm\\\\:mt-1[_ngcontent-%COMP%]{margin-top:.25rem}.sm\\\\:flex[_ngcontent-%COMP%]{display:flex}.sm\\\\:grid-cols-2[_ngcontent-%COMP%]{grid-template-columns:repeat(2,minmax(0,1fr))}.sm\\\\:items-center[_ngcontent-%COMP%]{align-items:center}.sm\\\\:pl-3[_ngcontent-%COMP%]{padding-left:.75rem}}@media (min-width: 768px){.md\\\\:flex[_ngcontent-%COMP%]{display:flex}.md\\\\:grid-cols-3[_ngcontent-%COMP%]{grid-template-columns:repeat(3,minmax(0,1fr))}.md\\\\:pb-20[_ngcontent-%COMP%]{padding-bottom:5rem}}@media (min-width: 1024px){.lg\\\\:\\\\!mx-0[_ngcontent-%COMP%]{margin-left:0!important;margin-right:0!important}.lg\\\\:\\\\!mt-0[_ngcontent-%COMP%]{margin-top:0!important}.lg\\\\:flex[_ngcontent-%COMP%]{display:flex}.lg\\\\:\\\\!w-\\\\__ph-0__[_ngcontent-%COMP%]{width:180px!important}.lg\\\\:\\\\!w-\\\\__ph-0__[_ngcontent-%COMP%]{width:800px!important}.lg\\\\:\\\\!w-\\\\__ph-0__[_ngcontent-%COMP%]{width:900px!important}.lg\\\\:\\\\!flex-row[_ngcontent-%COMP%]{flex-direction:row!important}.lg\\\\:\\\\!px-4[_ngcontent-%COMP%]{padding-left:1rem!important;padding-right:1rem!important}.lg\\\\:\\\\!px-\\\\__ph-0__[_ngcontent-%COMP%]{padding-left:22px!important;padding-right:22px!important}.lg\\\\:\\\\!py-\\\\__ph-0__[_ngcontent-%COMP%]{padding-top:18px!important;padding-bottom:18px!important}}\"]\n  });\n}", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "tap", "ButtonModule", "BaseFilePipe", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "StepVChoiceComponent_Conditional_0_div_7_Template_button_click_4_listener", "houseReview_r3", "ɵɵrestoreView", "_r2", "$implicit", "ctx_r3", "ɵɵnextContext", "ɵɵresetView", "gotoPickItem", "ɵɵadvance", "ɵɵtextInterpolate1", "CReviewName", "ɵɵproperty", "handleStyleOption", "CIsReview", "hanldeTitleOption", "ɵɵtemplate", "StepVChoiceComponent_Conditional_0_div_7_Template", "StepVChoiceComponent_Conditional_0_Template_button_click_8_listener", "_r1", "next", "listHouseReview", "checkDisable", "ɵɵpureFunction2", "_c0", "currentPdfUrl", "ɵɵsanitizeResourceUrl", "currentHouseReview", "CReviewType", "ɵɵsanitizeUrl", "ɵɵelement", "StepVChoiceComponent_Conditional_1_Conditional_4_Template", "StepVChoiceComponent_Conditional_1_Conditional_5_Template", "StepVChoiceComponent_Conditional_1_Template_button_click_6_listener", "_r5", "handleReview", "ɵɵconditional", "CFileUrl", "StepVChoiceComponent", "constructor", "_houseService", "sanitizer", "_fileService", "nextEvent", "refreshHouseReview", "listHouseReviewChange", "isReviewing", "pdfBlobUrls", "Map", "pdfDataUrls", "loadingPdfUrls", "Set", "baseFilePipe", "getSafePdfUrl", "fileUrl", "existingBlobUrl", "get", "bypassSecurityTrustResourceUrl", "existingDataUrl", "loadPdfFile", "fileName", "has", "add", "getFile", "subscribe", "response", "body", "blobUrl", "URL", "createObjectURL", "set", "reader", "FileReader", "onload", "dataUrl", "result", "console", "log", "updateCurrentPdfUrl", "readAsDataURL", "delete", "error", "ngOnInit", "length", "emit", "getFileExtension", "houseReview", "trim", "urlParts", "split", "includes", "toLowerCase", "substring", "lastIndexOf", "isCADFile", "extension", "downloadFile", "url", "window", "link", "document", "createElement", "href", "download", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "isReview", "isCAD", "apiHouseUpdateHouseReviewPost$Json", "CHouseReviewID", "CHouseReviewId", "pipe", "res", "StatusCode", "reviewableFiles", "filter", "x", "every", "ngOnDestroy", "for<PERSON>ach", "clear", "_", "ɵɵdirectiveInject", "i1", "HouseService", "i2", "Dom<PERSON><PERSON><PERSON>zer", "i3", "FileService", "_2", "selectors", "inputs", "outputs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "StepVChoiceComponent_Template", "rf", "ctx", "StepVChoiceComponent_Conditional_0_Template", "StepVChoiceComponent_Conditional_1_Template", "i4", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgStyle", "i5", "ButtonDirective", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\SaleChangeFront\\src\\app\\pages\\choice\\components\\step-v-choice\\step-v-choice.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\SaleChangeFront\\src\\app\\pages\\choice\\components\\step-v-choice\\step-v-choice.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, OnInit, Output, OnDestroy } from '@angular/core';\r\nimport { CommonModule, NgFor } from '@angular/common';\r\nimport { tap } from 'rxjs';\r\nimport { GetHouseReview } from '../../../../../services/api/models';\r\nimport { ButtonModule } from 'primeng/button';\r\nimport { BaseFilePipe } from '../../../../shared/pipes/base-file.pipe';\r\nimport { HouseService } from '../../../../../services/api/services';\r\nimport { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';\r\nimport { FileService } from '../../../../../services/File.service';\r\n\r\n@Component({\r\n  selector: 'app-step-v-choice',\r\n  standalone: true, imports: [\r\n    CommonModule,\r\n    ButtonModule,\r\n    NgFor\r\n  ],\r\n  templateUrl: './step-v-choice.component.html',\r\n  styleUrl: './step-v-choice.component.scss'\r\n})\r\nexport class StepVChoiceComponent implements OnInit, OnDestroy {\r\n  @Input() listHouseReview: GetHouseReview[] = []\r\n  @Output() nextEvent = new EventEmitter()\r\n  @Output() refreshHouseReview = new EventEmitter()\r\n  @Output() listHouseReviewChange = new EventEmitter();\r\n\r\n  currentHouseReview!: GetHouseReview\r\n  isReviewing: boolean = false;\r\n  baseFilePipe: BaseFilePipe;\r\n  currentPdfUrl: SafeResourceUrl | null = null; // 快取當前 PDF 的安全 URL\r\n  // PDF 處理相關屬性\r\n  pdfBlobUrls: Map<string, string> = new Map(); // 儲存每個 PDF 的 blob URL\r\n  pdfDataUrls: Map<string, string> = new Map(); // 儲存每個 PDF 的 data URL\r\n  loadingPdfUrls: Set<string> = new Set(); // 追蹤正在載入的 PDF URL\r\n\r\n  constructor(\r\n    private _houseService: HouseService,\r\n    private sanitizer: DomSanitizer,\r\n    private _fileService: FileService\r\n  ) {\r\n    this.baseFilePipe = new BaseFilePipe(this.sanitizer);\r\n  }  // 添加安全 URL 處理方法\r\n  getSafePdfUrl(fileUrl: string): SafeResourceUrl {\r\n    // 首先檢查是否已經有對應的 blob URL\r\n    const existingBlobUrl = this.pdfBlobUrls.get(fileUrl);\r\n    if (existingBlobUrl) {\r\n      return this.sanitizer.bypassSecurityTrustResourceUrl(existingBlobUrl);\r\n    }\r\n\r\n    // 檢查是否有 data URL\r\n    const existingDataUrl = this.pdfDataUrls.get(fileUrl);\r\n    if (existingDataUrl) {\r\n      return this.sanitizer.bypassSecurityTrustResourceUrl(existingDataUrl);\r\n    }\r\n\r\n    // 如果沒有快取的 URL，返回空字串或預設值，不觸發載入\r\n    return this.sanitizer.bypassSecurityTrustResourceUrl('');\r\n  }  // 載入 PDF 文件並建立 blob URL\r\n  private loadPdfFile(fileUrl: string, fileName: string): void {\r\n    // 檢查是否正在載入或已經載入完成\r\n    if (this.loadingPdfUrls.has(fileUrl) || this.pdfBlobUrls.has(fileUrl)) {\r\n      return;\r\n    }\r\n\r\n    // 標記為正在載入\r\n    this.loadingPdfUrls.add(fileUrl);\r\n\r\n    this._fileService.getFile(fileUrl, fileName).subscribe(\r\n      (response: HttpResponse<Blob>) => {\r\n        if (response.body) {\r\n          // 方法1: 創建 blob URL\r\n          const blobUrl = URL.createObjectURL(response.body);\r\n          this.pdfBlobUrls.set(fileUrl, blobUrl);\r\n\r\n          // 方法2: 轉換為 base64 data URL (更好的兼容性)\r\n          const reader = new FileReader();\r\n          reader.onload = () => {\r\n            const dataUrl = reader.result as string;\r\n            this.pdfDataUrls.set(fileUrl, dataUrl);\r\n            console.log('PDF Data URL 已準備就緒:', fileUrl);\r\n\r\n            // 如果這是當前顯示的 PDF，更新快取的 URL\r\n            if (this.currentHouseReview?.CFileUrl === fileUrl) {\r\n              this.updateCurrentPdfUrl();\r\n            }\r\n          };\r\n          reader.readAsDataURL(response.body);\r\n        }\r\n\r\n        console.log('PDF Blob URL 已準備就緒:', fileUrl, blobUrl);\r\n\r\n        // 移除載入狀態\r\n        this.loadingPdfUrls.delete(fileUrl);\r\n\r\n        // 如果這是當前顯示的 PDF，更新快取的 URL\r\n        if (this.currentHouseReview?.CFileUrl === fileUrl) {\r\n          this.updateCurrentPdfUrl();\r\n        }\r\n      },\r\n      (error) => {\r\n        console.error('取得 PDF 文件時發生錯誤:', fileUrl, error);\r\n        // 載入失敗時也要移除載入狀態\r\n        this.loadingPdfUrls.delete(fileUrl);\r\n      }\r\n    );\r\n  }\r\n\r\n\r\n\r\n  ngOnInit(): void {\r\n    // 如果沒有資料，觸發重新載入\r\n    if (!this.listHouseReview || this.listHouseReview.length === 0) {\r\n      this.refreshHouseReview.emit();\r\n    }\r\n  }\r\n\r\n  // 從檔案 URL 或名稱提取擴展名\r\n  private getFileExtension(houseReview: GetHouseReview): string {\r\n    let fileName = '';\r\n\r\n    // 優先從 CReviewName 提取檔名（因為它包含完整的檔案名稱和擴展名）\r\n    if (houseReview.CReviewName) {\r\n      fileName = houseReview.CReviewName.trim(); // 移除前後空白\r\n    } else if (houseReview.CFileUrl) {\r\n      const urlParts = houseReview.CFileUrl.split('/');\r\n      fileName = urlParts[urlParts.length - 1];\r\n    }\r\n\r\n    if (fileName && fileName.includes('.')) {\r\n      return fileName.toLowerCase().substring(fileName.lastIndexOf('.'));\r\n    }\r\n\r\n    return '';\r\n  }\r\n\r\n  // 判斷是否為 CAD 檔案\r\n  private isCADFile(houseReview: GetHouseReview): boolean {\r\n    const extension = this.getFileExtension(houseReview);\r\n    return ['.dwg', '.dxf', '.dwf'].includes(extension);\r\n  }\r\n\r\n  // 下載檔案\r\n  downloadFile(houseReview: GetHouseReview): void {\r\n    if (!houseReview.CFileUrl || !houseReview.CReviewName) {\r\n      console.error('檔案 URL 或名稱不存在');\r\n      return;\r\n    }\r\n\r\n    this._fileService.getFile(houseReview.CFileUrl, houseReview.CReviewName).subscribe({\r\n      next: (response: HttpResponse<Blob>) => {\r\n        if (response.body) {\r\n          // 創建下載鏈接\r\n          const url = window.URL.createObjectURL(response.body);\r\n          const link = document.createElement('a');\r\n          link.href = url;\r\n          link.download = houseReview.CReviewName || 'download';\r\n          document.body.appendChild(link);\r\n          link.click();\r\n          document.body.removeChild(link);\r\n          window.URL.revokeObjectURL(url);\r\n        }\r\n      },\r\n      error: (error) => {\r\n        console.error('檔案下載失敗:', error);\r\n      }\r\n    });\r\n  }\r\n\r\n  gotoPickItem(houseReview: GetHouseReview) {\r\n    // 如果是 CAD 檔案，直接下載\r\n    if (this.isCADFile(houseReview)) {\r\n      this.downloadFile(houseReview);\r\n      return;\r\n    }\r\n\r\n    // PDF 檔案進入審閱頁面\r\n    this.currentHouseReview = houseReview;\r\n    this.isReviewing = true;\r\n\r\n    // 更新當前 PDF URL\r\n    this.updateCurrentPdfUrl();\r\n\r\n    // 當進入審閱頁面時，載入對應的 PDF 檔案\r\n    if (houseReview.CFileUrl && !this.pdfBlobUrls.has(houseReview.CFileUrl) && !this.loadingPdfUrls.has(houseReview.CFileUrl)) {\r\n      this.loadPdfFile(houseReview.CFileUrl, houseReview.CReviewName || 'document.pdf');\r\n    }\r\n  }\r\n\r\n  // 更新當前 PDF URL 的快取\r\n  private updateCurrentPdfUrl(): void {\r\n    if (!this.currentHouseReview?.CFileUrl) {\r\n      this.currentPdfUrl = null;\r\n      return;\r\n    }\r\n\r\n    this.currentPdfUrl = this.getSafePdfUrl(this.currentHouseReview.CFileUrl);\r\n  }\r\n\r\n  handleStyleOption(isReview: boolean) {\r\n    if (isReview) {\r\n      return { 'background': 'linear-gradient(90deg, #AE9B66 0%, #B8A676 100%)', 'color': 'white' }\r\n    }\r\n    return { 'backgroundColor': '#E5E3E1', 'color': '#3A4246B2' }\r\n  }\r\n\r\n  // 根據檔案類型和審閱狀態決定按鈕文字\r\n  hanldeTitleOption(houseReview: GetHouseReview): string {\r\n    const isCAD = this.isCADFile(houseReview);\r\n\r\n    if (isCAD) {\r\n      // CAD 檔案顯示下載按鈕\r\n      return '下載';\r\n    } else {\r\n      // PDF 檔案顯示審閱狀態\r\n      return houseReview.CIsReview ? '已審閱' : '未審閱';\r\n    }\r\n  }\r\n\r\n  next() {\r\n    this.nextEvent.emit();\r\n  }\r\n\r\n  handleReview() {\r\n    this._houseService.apiHouseUpdateHouseReviewPost$Json({\r\n      body: {\r\n        CHouseReviewID: this.currentHouseReview.CHouseReviewId!\r\n      }\r\n    }).pipe(\r\n      tap(() => this.refreshHouseReview.emit()),\r\n      tap(res => {\r\n        if (res.StatusCode == 0) {\r\n          this.currentHouseReview = {} as GetHouseReview\r\n          this.isReviewing = false;\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n  checkDisable() {\r\n    // 過濾出需要審閱的檔案（非 CAD 檔案）\r\n    const reviewableFiles = this.listHouseReview.filter(x => !this.isCADFile(x));\r\n\r\n    // 如果沒有需要審閱的檔案，則啟用下一步\r\n    if (reviewableFiles.length === 0) {\r\n      return false;\r\n    }\r\n\r\n    // 檢查所有需要審閱的檔案是否都已審閱\r\n    return !reviewableFiles.every(x => x.CIsReview);\r\n  }\r\n  ngOnDestroy(): void {\r\n    // 清理所有 blob URL 以避免記憶體洩漏\r\n    this.pdfBlobUrls.forEach((blobUrl) => {\r\n      URL.revokeObjectURL(blobUrl);\r\n    });\r\n    this.pdfBlobUrls.clear();\r\n    this.pdfDataUrls.clear();\r\n    this.loadingPdfUrls.clear();\r\n  }\r\n}\r\n", "@if (!isReviewing) {\r\n<section class=\"flex flex-col justify-center items-center w-full\">\r\n  <div class=\"flex flex-col text-center\">\r\n    <span class=\"text-2xl text-black\">\r\n      標準圖面審閱總覽\r\n    </span>\r\n    <span class=\"text-base mt-2\">\r\n      點擊項目可前往審閱\r\n    </span>\r\n  </div>\r\n  <div class=\"flex flex-col lg:!w-[800px] w-full m-auto my-6 px-4\">\r\n    <div class=\"flex lg:!flex-row flex-col\r\n        sm:items-center items-start justify-between\r\n        cursor-pointer bg-[#F3F1EA99] mb-4 rounded-lg\r\n        py-4 lg:!px-[22px] px-4\" *ngFor=\"let houseReview of listHouseReview\">\r\n      <div class=\"w-full items-center sm:mx-0 sm:pl-3 mx-4 mt-3 sm:mt-1\">\r\n        <span class=\"text-base block w-full text-left text-black\">\r\n          {{houseReview.CReviewName!}}\r\n        </span>\r\n      </div>\r\n      <button class=\"flex justify-center text-white font-medium items-center rounded-3xl bg-gray-500 p-2 lg:!mx-0 py-3\r\n          lg:!w-[180px] w-full h-[47px] mt-3 lg:!mt-0\" pButton (click)=\"gotoPickItem(houseReview)\"\r\n        [ngStyle]=\"handleStyleOption(houseReview.CIsReview!)\">\r\n        {{hanldeTitleOption(houseReview)}}\r\n      </button>\r\n    </div>\r\n  </div>\r\n\r\n  <button class=\"btn-next butn1 flex justify-center items-center my-6 w-[180px] h-[47px]\" pButton\r\n    [disabled]=\"checkDisable()\" [ngClass]=\"{'btn-enable': !checkDisable(), 'btn-disable': checkDisable()}\"\r\n    (click)=\"next()\">\r\n    下一步\r\n  </button>\r\n</section>\r\n}@else {\r\n<section class=\"flex flex-col justify-center items-center w-full\">\r\n  <div class=\"flex flex-col justify-center items-center w-full\"> <span class=\"text-2xl text-black\">\r\n      {{currentHouseReview.CReviewType == 1 ? '標準圖' : '設備圖'}}\r\n    </span> @if(!!currentHouseReview.CFileUrl) {\r\n    <!-- 備用方案：使用 object 標籤 -->\r\n    <object class=\"mt-3 w-full\" [data]=\"currentPdfUrl\" type=\"application/pdf\" width=\"100%\" height=\"500\"\r\n      style=\"border: 1px solid #ccc;\">\r\n      <!-- 如果 object 無法顯示，顯示備用內容 -->\r\n      <iframe [src]=\"currentPdfUrl\" width=\"100%\" height=\"500\" style=\"border: 1px solid #ccc;\"\r\n        [title]=\"(currentHouseReview.CReviewType == 1 ? '標準圖' : '設備圖') + 'PDF'\">\r\n        <!-- 如果 iframe 也無法顯示，提供下載連結 -->\r\n        <p>您的瀏覽器無法顯示 PDF。請\r\n          <a [href]=\"currentPdfUrl\" [download]=\"(currentHouseReview.CReviewType == 1 ? '標準圖' : '設備圖') + '.pdf'\"\r\n            target=\"_blank\">\r\n            點擊此處下載 PDF 文件\r\n          </a>\r\n        </p>\r\n      </iframe>\r\n    </object>\r\n    } @else {\r\n    <div class=\"w-full h-[40vh] bg-[#d9d9d9] flex justify-center items-center\">\r\n      <div class=\"font-semibold text-lg\">標準圖圖面<br>\r\n        （內嵌PDF）</div>\r\n    </div>\r\n    }\r\n\r\n    <button class=\"button2 !w-48 butn1 flex justify-center items-center mt-4\" pButton (click)=\"handleReview()\">\r\n      確認\r\n    </button>\r\n  </div>\r\n</section>\r\n}"], "mappings": "AAAA,SAAoBA,YAAY,QAA0C,eAAe;AACzF,SAASC,YAAY,QAAe,iBAAiB;AACrD,SAASC,GAAG,QAAQ,MAAM;AAE1B,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,YAAY,QAAQ,yCAAyC;;;;;;;;;;;;;;ICW9DC,EALJ,CAAAC,cAAA,aAGyE,aACJ,cACP;IACxDD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAO,EACH;IACNH,EAAA,CAAAC,cAAA,iBAEwD;IADCD,EAAA,CAAAI,UAAA,mBAAAC,0EAAA;MAAA,MAAAC,cAAA,GAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAG,YAAA,CAAAP,cAAA,CAAyB;IAAA,EAAC;IAE1FN,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAS,EACL;;;;;IARAH,EAAA,CAAAc,SAAA,GACF;IADEd,EAAA,CAAAe,kBAAA,MAAAT,cAAA,CAAAU,WAAA,MACF;IAIAhB,EAAA,CAAAc,SAAA,EAAqD;IAArDd,EAAA,CAAAiB,UAAA,YAAAP,MAAA,CAAAQ,iBAAA,CAAAZ,cAAA,CAAAa,SAAA,EAAqD;IACrDnB,EAAA,CAAAc,SAAA,EACF;IADEd,EAAA,CAAAe,kBAAA,MAAAL,MAAA,CAAAU,iBAAA,CAAAd,cAAA,OACF;;;;;;IArBFN,EAFJ,CAAAC,cAAA,iBAAkE,aACzB,cACH;IAChCD,EAAA,CAAAE,MAAA,yDACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,cAA6B;IAC3BD,EAAA,CAAAE,MAAA,+DACF;IACFF,EADE,CAAAG,YAAA,EAAO,EACH;IACNH,EAAA,CAAAC,cAAA,aAAiE;IAC/DD,EAAA,CAAAqB,UAAA,IAAAC,iDAAA,iBAGyE;IAY3EtB,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,gBAEmB;IAAjBD,EAAA,CAAAI,UAAA,mBAAAmB,oEAAA;MAAAvB,EAAA,CAAAO,aAAA,CAAAiB,GAAA;MAAA,MAAAd,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAe,IAAA,EAAM;IAAA,EAAC;IAChBzB,EAAA,CAAAE,MAAA,2BACF;IACFF,EADE,CAAAG,YAAA,EAAS,EACD;;;;IAnB+CH,EAAA,CAAAc,SAAA,GAAkB;IAAlBd,EAAA,CAAAiB,UAAA,YAAAP,MAAA,CAAAgB,eAAA,CAAkB;IAevE1B,EAAA,CAAAc,SAAA,EAA2B;IAACd,EAA5B,CAAAiB,UAAA,aAAAP,MAAA,CAAAiB,YAAA,GAA2B,YAAA3B,EAAA,CAAA4B,eAAA,IAAAC,GAAA,GAAAnB,MAAA,CAAAiB,YAAA,IAAAjB,MAAA,CAAAiB,YAAA,IAA2E;;;;;IAiBlG3B,EANJ,CAAAC,cAAA,iBACkC,iBAG0C,QAErE;IAAAD,EAAA,CAAAE,MAAA,8EACD;IAAAF,EAAA,CAAAC,cAAA,YACkB;IAChBD,EAAA,CAAAE,MAAA,8DACF;IAGNF,EAHM,CAAAG,YAAA,EAAI,EACF,EACG,EACF;;;;IAbmBH,EAAA,CAAAiB,UAAA,SAAAP,MAAA,CAAAoB,aAAA,EAAA9B,EAAA,CAAA+B,qBAAA,CAAsB;IAGxC/B,EAAA,CAAAc,SAAA,EAAqB;IAC3Bd,EADM,CAAAiB,UAAA,QAAAP,MAAA,CAAAoB,aAAA,EAAA9B,EAAA,CAAA+B,qBAAA,CAAqB,WAAArB,MAAA,CAAAsB,kBAAA,CAAAC,WAAA,6DAC4C;IAGlEjC,EAAA,CAAAc,SAAA,GAAsB;IAACd,EAAvB,CAAAiB,UAAA,SAAAP,MAAA,CAAAoB,aAAA,EAAA9B,EAAA,CAAAkC,aAAA,CAAsB,cAAAxB,MAAA,CAAAsB,kBAAA,CAAAC,WAAA,8DAA4E;;;;;IASzGjC,EADF,CAAAC,cAAA,cAA2E,cACtC;IAAAD,EAAA,CAAAE,MAAA,qCAAK;IAAAF,EAAA,CAAAmC,SAAA,SAAI;IAC1CnC,EAAA,CAAAE,MAAA,mCAAO;IACXF,EADW,CAAAG,YAAA,EAAM,EACX;;;;;;IAtBuDH,EADjE,CAAAC,cAAA,iBAAkE,aACF,cAAmC;IAC7FD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAgBLH,EAhBM,CAAAqB,UAAA,IAAAe,yDAAA,qBAAoC,IAAAC,yDAAA,OAgBnC;IAOTrC,EAAA,CAAAC,cAAA,iBAA2G;IAAzBD,EAAA,CAAAI,UAAA,mBAAAkC,oEAAA;MAAAtC,EAAA,CAAAO,aAAA,CAAAgC,GAAA;MAAA,MAAA7B,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAA8B,YAAA,EAAc;IAAA,EAAC;IACxGxC,EAAA,CAAAE,MAAA,qBACF;IAEJF,EAFI,CAAAG,YAAA,EAAS,EACL,EACE;;;;IA5BJH,EAAA,CAAAc,SAAA,GACF;IADEd,EAAA,CAAAe,kBAAA,MAAAL,MAAA,CAAAsB,kBAAA,CAAAC,WAAA,yDACF;IAAQjC,EAAA,CAAAc,SAAA,EAqBP;IArBOd,EAAA,CAAAyC,aAAA,MAAA/B,MAAA,CAAAsB,kBAAA,CAAAU,QAAA,SAqBP;;;ADvCL,OAAM,MAAOC,oBAAoB;EAe/BC,YACUC,aAA2B,EAC3BC,SAAuB,EACvBC,YAAyB;IAFzB,KAAAF,aAAa,GAAbA,aAAa;IACb,KAAAC,SAAS,GAATA,SAAS;IACT,KAAAC,YAAY,GAAZA,YAAY;IAjBb,KAAArB,eAAe,GAAqB,EAAE;IACrC,KAAAsB,SAAS,GAAG,IAAIrD,YAAY,EAAE;IAC9B,KAAAsD,kBAAkB,GAAG,IAAItD,YAAY,EAAE;IACvC,KAAAuD,qBAAqB,GAAG,IAAIvD,YAAY,EAAE;IAGpD,KAAAwD,WAAW,GAAY,KAAK;IAE5B,KAAArB,aAAa,GAA2B,IAAI,CAAC,CAAC;IAC9C;IACA,KAAAsB,WAAW,GAAwB,IAAIC,GAAG,EAAE,CAAC,CAAC;IAC9C,KAAAC,WAAW,GAAwB,IAAID,GAAG,EAAE,CAAC,CAAC;IAC9C,KAAAE,cAAc,GAAgB,IAAIC,GAAG,EAAE,CAAC,CAAC;IAOvC,IAAI,CAACC,YAAY,GAAG,IAAI1D,YAAY,CAAC,IAAI,CAAC+C,SAAS,CAAC;EACtD,CAAC,CAAE;EACHY,aAAaA,CAACC,OAAe;IAC3B;IACA,MAAMC,eAAe,GAAG,IAAI,CAACR,WAAW,CAACS,GAAG,CAACF,OAAO,CAAC;IACrD,IAAIC,eAAe,EAAE;MACnB,OAAO,IAAI,CAACd,SAAS,CAACgB,8BAA8B,CAACF,eAAe,CAAC;;IAGvE;IACA,MAAMG,eAAe,GAAG,IAAI,CAACT,WAAW,CAACO,GAAG,CAACF,OAAO,CAAC;IACrD,IAAII,eAAe,EAAE;MACnB,OAAO,IAAI,CAACjB,SAAS,CAACgB,8BAA8B,CAACC,eAAe,CAAC;;IAGvE;IACA,OAAO,IAAI,CAACjB,SAAS,CAACgB,8BAA8B,CAAC,EAAE,CAAC;EAC1D,CAAC,CAAE;EACKE,WAAWA,CAACL,OAAe,EAAEM,QAAgB;IACnD;IACA,IAAI,IAAI,CAACV,cAAc,CAACW,GAAG,CAACP,OAAO,CAAC,IAAI,IAAI,CAACP,WAAW,CAACc,GAAG,CAACP,OAAO,CAAC,EAAE;MACrE;;IAGF;IACA,IAAI,CAACJ,cAAc,CAACY,GAAG,CAACR,OAAO,CAAC;IAEhC,IAAI,CAACZ,YAAY,CAACqB,OAAO,CAACT,OAAO,EAAEM,QAAQ,CAAC,CAACI,SAAS,CACnDC,QAA4B,IAAI;MAC/B,IAAIA,QAAQ,CAACC,IAAI,EAAE;QACjB;QACA,MAAMC,OAAO,GAAGC,GAAG,CAACC,eAAe,CAACJ,QAAQ,CAACC,IAAI,CAAC;QAClD,IAAI,CAACnB,WAAW,CAACuB,GAAG,CAAChB,OAAO,EAAEa,OAAO,CAAC;QAEtC;QACA,MAAMI,MAAM,GAAG,IAAIC,UAAU,EAAE;QAC/BD,MAAM,CAACE,MAAM,GAAG,MAAK;UACnB,MAAMC,OAAO,GAAGH,MAAM,CAACI,MAAgB;UACvC,IAAI,CAAC1B,WAAW,CAACqB,GAAG,CAAChB,OAAO,EAAEoB,OAAO,CAAC;UACtCE,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEvB,OAAO,CAAC;UAE3C;UACA,IAAI,IAAI,CAAC3B,kBAAkB,EAAEU,QAAQ,KAAKiB,OAAO,EAAE;YACjD,IAAI,CAACwB,mBAAmB,EAAE;;QAE9B,CAAC;QACDP,MAAM,CAACQ,aAAa,CAACd,QAAQ,CAACC,IAAI,CAAC;;MAGrCU,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEvB,OAAO,EAAEa,OAAO,CAAC;MAEpD;MACA,IAAI,CAACjB,cAAc,CAAC8B,MAAM,CAAC1B,OAAO,CAAC;MAEnC;MACA,IAAI,IAAI,CAAC3B,kBAAkB,EAAEU,QAAQ,KAAKiB,OAAO,EAAE;QACjD,IAAI,CAACwB,mBAAmB,EAAE;;IAE9B,CAAC,EACAG,KAAK,IAAI;MACRL,OAAO,CAACK,KAAK,CAAC,iBAAiB,EAAE3B,OAAO,EAAE2B,KAAK,CAAC;MAChD;MACA,IAAI,CAAC/B,cAAc,CAAC8B,MAAM,CAAC1B,OAAO,CAAC;IACrC,CAAC,CACF;EACH;EAIA4B,QAAQA,CAAA;IACN;IACA,IAAI,CAAC,IAAI,CAAC7D,eAAe,IAAI,IAAI,CAACA,eAAe,CAAC8D,MAAM,KAAK,CAAC,EAAE;MAC9D,IAAI,CAACvC,kBAAkB,CAACwC,IAAI,EAAE;;EAElC;EAEA;EACQC,gBAAgBA,CAACC,WAA2B;IAClD,IAAI1B,QAAQ,GAAG,EAAE;IAEjB;IACA,IAAI0B,WAAW,CAAC3E,WAAW,EAAE;MAC3BiD,QAAQ,GAAG0B,WAAW,CAAC3E,WAAW,CAAC4E,IAAI,EAAE,CAAC,CAAC;KAC5C,MAAM,IAAID,WAAW,CAACjD,QAAQ,EAAE;MAC/B,MAAMmD,QAAQ,GAAGF,WAAW,CAACjD,QAAQ,CAACoD,KAAK,CAAC,GAAG,CAAC;MAChD7B,QAAQ,GAAG4B,QAAQ,CAACA,QAAQ,CAACL,MAAM,GAAG,CAAC,CAAC;;IAG1C,IAAIvB,QAAQ,IAAIA,QAAQ,CAAC8B,QAAQ,CAAC,GAAG,CAAC,EAAE;MACtC,OAAO9B,QAAQ,CAAC+B,WAAW,EAAE,CAACC,SAAS,CAAChC,QAAQ,CAACiC,WAAW,CAAC,GAAG,CAAC,CAAC;;IAGpE,OAAO,EAAE;EACX;EAEA;EACQC,SAASA,CAACR,WAA2B;IAC3C,MAAMS,SAAS,GAAG,IAAI,CAACV,gBAAgB,CAACC,WAAW,CAAC;IACpD,OAAO,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAACI,QAAQ,CAACK,SAAS,CAAC;EACrD;EAEA;EACAC,YAAYA,CAACV,WAA2B;IACtC,IAAI,CAACA,WAAW,CAACjD,QAAQ,IAAI,CAACiD,WAAW,CAAC3E,WAAW,EAAE;MACrDiE,OAAO,CAACK,KAAK,CAAC,eAAe,CAAC;MAC9B;;IAGF,IAAI,CAACvC,YAAY,CAACqB,OAAO,CAACuB,WAAW,CAACjD,QAAQ,EAAEiD,WAAW,CAAC3E,WAAW,CAAC,CAACqD,SAAS,CAAC;MACjF5C,IAAI,EAAG6C,QAA4B,IAAI;QACrC,IAAIA,QAAQ,CAACC,IAAI,EAAE;UACjB;UACA,MAAM+B,GAAG,GAAGC,MAAM,CAAC9B,GAAG,CAACC,eAAe,CAACJ,QAAQ,CAACC,IAAI,CAAC;UACrD,MAAMiC,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;UACxCF,IAAI,CAACG,IAAI,GAAGL,GAAG;UACfE,IAAI,CAACI,QAAQ,GAAGjB,WAAW,CAAC3E,WAAW,IAAI,UAAU;UACrDyF,QAAQ,CAAClC,IAAI,CAACsC,WAAW,CAACL,IAAI,CAAC;UAC/BA,IAAI,CAACM,KAAK,EAAE;UACZL,QAAQ,CAAClC,IAAI,CAACwC,WAAW,CAACP,IAAI,CAAC;UAC/BD,MAAM,CAAC9B,GAAG,CAACuC,eAAe,CAACV,GAAG,CAAC;;MAEnC,CAAC;MACDhB,KAAK,EAAGA,KAAK,IAAI;QACfL,OAAO,CAACK,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MACjC;KACD,CAAC;EACJ;EAEAzE,YAAYA,CAAC8E,WAA2B;IACtC;IACA,IAAI,IAAI,CAACQ,SAAS,CAACR,WAAW,CAAC,EAAE;MAC/B,IAAI,CAACU,YAAY,CAACV,WAAW,CAAC;MAC9B;;IAGF;IACA,IAAI,CAAC3D,kBAAkB,GAAG2D,WAAW;IACrC,IAAI,CAACxC,WAAW,GAAG,IAAI;IAEvB;IACA,IAAI,CAACgC,mBAAmB,EAAE;IAE1B;IACA,IAAIQ,WAAW,CAACjD,QAAQ,IAAI,CAAC,IAAI,CAACU,WAAW,CAACc,GAAG,CAACyB,WAAW,CAACjD,QAAQ,CAAC,IAAI,CAAC,IAAI,CAACa,cAAc,CAACW,GAAG,CAACyB,WAAW,CAACjD,QAAQ,CAAC,EAAE;MACzH,IAAI,CAACsB,WAAW,CAAC2B,WAAW,CAACjD,QAAQ,EAAEiD,WAAW,CAAC3E,WAAW,IAAI,cAAc,CAAC;;EAErF;EAEA;EACQmE,mBAAmBA,CAAA;IACzB,IAAI,CAAC,IAAI,CAACnD,kBAAkB,EAAEU,QAAQ,EAAE;MACtC,IAAI,CAACZ,aAAa,GAAG,IAAI;MACzB;;IAGF,IAAI,CAACA,aAAa,GAAG,IAAI,CAAC4B,aAAa,CAAC,IAAI,CAAC1B,kBAAkB,CAACU,QAAQ,CAAC;EAC3E;EAEAxB,iBAAiBA,CAAC+F,QAAiB;IACjC,IAAIA,QAAQ,EAAE;MACZ,OAAO;QAAE,YAAY,EAAE,kDAAkD;QAAE,OAAO,EAAE;MAAO,CAAE;;IAE/F,OAAO;MAAE,iBAAiB,EAAE,SAAS;MAAE,OAAO,EAAE;IAAW,CAAE;EAC/D;EAEA;EACA7F,iBAAiBA,CAACuE,WAA2B;IAC3C,MAAMuB,KAAK,GAAG,IAAI,CAACf,SAAS,CAACR,WAAW,CAAC;IAEzC,IAAIuB,KAAK,EAAE;MACT;MACA,OAAO,IAAI;KACZ,MAAM;MACL;MACA,OAAOvB,WAAW,CAACxE,SAAS,GAAG,KAAK,GAAG,KAAK;;EAEhD;EAEAM,IAAIA,CAAA;IACF,IAAI,CAACuB,SAAS,CAACyC,IAAI,EAAE;EACvB;EAEAjD,YAAYA,CAAA;IACV,IAAI,CAACK,aAAa,CAACsE,kCAAkC,CAAC;MACpD5C,IAAI,EAAE;QACJ6C,cAAc,EAAE,IAAI,CAACpF,kBAAkB,CAACqF;;KAE3C,CAAC,CAACC,IAAI,CACLzH,GAAG,CAAC,MAAM,IAAI,CAACoD,kBAAkB,CAACwC,IAAI,EAAE,CAAC,EACzC5F,GAAG,CAAC0H,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAACxF,kBAAkB,GAAG,EAAoB;QAC9C,IAAI,CAACmB,WAAW,GAAG,KAAK;;IAE5B,CAAC,CAAC,CACH,CAACkB,SAAS,EAAE;EACf;EACA1C,YAAYA,CAAA;IACV;IACA,MAAM8F,eAAe,GAAG,IAAI,CAAC/F,eAAe,CAACgG,MAAM,CAACC,CAAC,IAAI,CAAC,IAAI,CAACxB,SAAS,CAACwB,CAAC,CAAC,CAAC;IAE5E;IACA,IAAIF,eAAe,CAACjC,MAAM,KAAK,CAAC,EAAE;MAChC,OAAO,KAAK;;IAGd;IACA,OAAO,CAACiC,eAAe,CAACG,KAAK,CAACD,CAAC,IAAIA,CAAC,CAACxG,SAAS,CAAC;EACjD;EACA0G,WAAWA,CAAA;IACT;IACA,IAAI,CAACzE,WAAW,CAAC0E,OAAO,CAAEtD,OAAO,IAAI;MACnCC,GAAG,CAACuC,eAAe,CAACxC,OAAO,CAAC;IAC9B,CAAC,CAAC;IACF,IAAI,CAACpB,WAAW,CAAC2E,KAAK,EAAE;IACxB,IAAI,CAACzE,WAAW,CAACyE,KAAK,EAAE;IACxB,IAAI,CAACxE,cAAc,CAACwE,KAAK,EAAE;EAC7B;EAAC,QAAAC,CAAA,G;qBA7OUrF,oBAAoB,EAAA3C,EAAA,CAAAiI,iBAAA,CAAAC,EAAA,CAAAC,YAAA,GAAAnI,EAAA,CAAAiI,iBAAA,CAAAG,EAAA,CAAAC,YAAA,GAAArI,EAAA,CAAAiI,iBAAA,CAAAK,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;UAApB7F,oBAAoB;IAAA8F,SAAA;IAAAC,MAAA;MAAAhH,eAAA;IAAA;IAAAiH,OAAA;MAAA3F,SAAA;MAAAC,kBAAA;MAAAC,qBAAA;IAAA;IAAA0F,UAAA;IAAAC,QAAA,GAAA7I,EAAA,CAAA8I,mBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCchCpJ,EAlCD,CAAAqB,UAAA,IAAAiI,2CAAA,sBAAoB,IAAAC,2CAAA,OAkCZ;;;QAlCRvJ,EAAA,CAAAyC,aAAA,KAAA4G,GAAA,CAAAlG,WAAA,SAkEC;;;mBDrDGvD,YAAY,EAAA4J,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,OAAA,EACZ7J,YAAY,EAAA8J,EAAA,CAAAC,eAAA;IAAAC,MAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}