import { Component, EventEmitter, Input, OnInit, Output, OnDestroy } from '@angular/core';
import { HttpResponse } from '@angular/common/http';
import { CheckboxModule } from 'primeng/checkbox';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { ButtonModule } from 'primeng/button';
import { HouseService } from '../../../../../services/api/services';
import { FileService } from '../../../../../services/File.service';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';

@Component({
  selector: 'app-step-iv-choice',
  standalone: true, imports: [
    CheckboxModule,
    ButtonModule,
    FormsModule,
    CommonModule
  ],
  templateUrl: './step-iv-choice.component.html',
  styleUrl: './step-iv-choice.component.scss'
})

export class StepIvChoiceComponent implements OnInit, OnD<PERSON>roy {
  @Input() agreeToGoNextStep4: boolean = false;
  @Output() agreeToGoNextStep4Change = new EventEmitter();
  @Output() nextEvent = new EventEmitter()
  detailPicture: Blob | string | any
  detailPictureUrl: string | null = null;
  safePdfUrl: SafeResourceUrl | null = null;
  pdfDataUrl: string | null = null; // 添加 base64 data URL
  isLoadingPdf: boolean = false; // 加入載入狀態
  constructor(
    private _houseService: HouseService,
    private _fileService: FileService,
    private sanitizer: DomSanitizer
  ) {

  }

  ngOnInit(): void {
    this.getImage()
  }
  getImage() {
    this.isLoadingPdf = true; // 開始載入
    this._houseService.apiHouseGetSpecialNoticeFilePost$Json({})
      .subscribe(res => {
        if (res.Entries && res.Entries.CFileName && res.Entries.CFileUrl) {          // 從 CFileUrl 中解析出相對路徑和檔名
          const fileUrl = res.Entries.CFileUrl;
          const fileName = res.Entries.CFileName;          // 使用 FileService 取得 PDF blob
          this._fileService.getFile(fileUrl, fileName).subscribe(
            (response: HttpResponse<Blob>) => {
              if (response.body) {
                this.detailPicture = response.body;

                // 方法1: 創建 blob URL
                this.detailPictureUrl = URL.createObjectURL(response.body);
                this.safePdfUrl = this.sanitizer.bypassSecurityTrustResourceUrl(this.detailPictureUrl);

                // 方法2: 轉換為 base64 data URL (更好的兼容性)
                const reader = new FileReader();
                reader.onload = () => {
                  this.pdfDataUrl = reader.result as string;
                  // 如果 blob URL 不工作，使用 data URL
                  if (this.pdfDataUrl) {
                    this.safePdfUrl = this.sanitizer.bypassSecurityTrustResourceUrl(this.pdfDataUrl);
                  }
                  this.isLoadingPdf = false; // 載入完成
                  console.log('PDF Data URL 已準備就緒');
              };
                reader.readAsDataURL(response.body);
              }

              console.log('PDF URL 已準備就緒:', this.detailPictureUrl);
            },
            (error) => {
              console.error('取得 PDF 文件時發生錯誤:', error);
              this.isLoadingPdf = false; // 載入錯誤時也要停止載入狀態
            }
          );
        } else {
          this.isLoadingPdf = false; // 沒有檔案時停止載入狀態
        }
      }, (error) => {
        console.error('取得檔案資訊時發生錯誤:', error);
        this.isLoadingPdf = false; // 載入錯誤時也要停止載入狀態
      });
  }

  next() {
    this.nextEvent.emit();
  }
  changeCheckbox(event: any) {
    this.agreeToGoNextStep4Change.emit(event)
  }

  refreshPdf() {
    // 重新載入PDF文件
    this.ngOnInit();
  }

  nextWithoutPdf() {
    // 無PDF檔案時的下一步，可以加入額外的確認邏輯
    this.nextEvent.emit();
  }

  ngOnDestroy(): void {
    // 清理 blob URL 以避免記憶體洩漏
    if (this.detailPictureUrl) {
      URL.revokeObjectURL(this.detailPictureUrl);
    }
  }
}
