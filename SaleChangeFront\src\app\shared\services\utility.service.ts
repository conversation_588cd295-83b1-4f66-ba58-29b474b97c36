import { Injectable } from '@angular/core';
import { HttpResponse } from '@angular/common/http';
import { environment } from '../../../environments/environment';
import { BuildCaseGetFileRespone } from '../../../services/api/models';
@Injectable({
  providedIn: 'root'
})
export class UtilityService {
  readonly BASE_FILE = environment.BASE_URL_API;

  constructor() { }

  downloadFileFromUrl(file: BuildCaseGetFileRespone) {
    const xhr = new XMLHttpRequest();
    xhr.open('GET', `${this.BASE_FILE}${file.CFile}`
      , true);
    xhr.responseType = 'blob';
    xhr.onload = () => {
      if (xhr.status === 200) {
        const url = window.URL.createObjectURL(xhr.response);
        const link = document.createElement('a');
        link.href = url;
        link.download = file.CName!;
        link.style.display = 'none';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
      }
    };
    xhr.send();
  }

  getFileNameFromUrl(url: string) {
    const parts = url.split('/');
    const fileName = parts.pop();
    return fileName;
  }

  downloadFileFullUrl(file: { CFile: string | any; CName: string | any }) {
    const xhr = new XMLHttpRequest();
    xhr.open('GET', `${file.CFile}?date=${new Date().getTime()}`
      , true);
    xhr.responseType = 'blob';
    xhr.onload = () => {
      if (xhr.status === 200) {
        const url = window.URL.createObjectURL(xhr.response);
        const link = document.createElement('a');
        link.href = url;
        link.download = file.CName!;
        link.style.display = 'none';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
      }
    };
    xhr.send();
  }

  DownloadFile = (data: Blob | HttpResponse<Blob>) => {
    if (data) {
      let downloadedFile: Blob;
      let fileName: string = 'download';
      let mimeType: string = '';

      if (data instanceof Blob) {
        // Handle direct Blob
        downloadedFile = data;
        mimeType = data.type || 'application/octet-stream';
      } else {
        // Handle HttpResponse<Blob>
        if (!data.body) {
          console.error('Response body is null');
          return;
        }
        downloadedFile = new Blob([data.body], { type: data.body.type });
        mimeType = data.body.type || 'application/octet-stream';
        const contentDisposition = data.headers.get('Content-Disposition');
        fileName = this.parseFileName(contentDisposition || '', 'download');
      }

      // Check if filename already has an extension
      if (!this.hasFileExtension(fileName)) {
        const extension = this.getMimeTypeExtension(mimeType);
        alert(extension)
        fileName += extension;
      }

      const a = document.createElement('a');
      a.setAttribute('style', 'display:none;');
      document.body.appendChild(a);
      a.download = fileName;
      a.href = URL.createObjectURL(downloadedFile);
      a.target = '_blank';
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(a.href);
    }
  }

  private parseFileName(header: string, def: string = 'download'): string {
    if (!header) {
      return def;
    }

    try {
      // Try different Content-Disposition formats
      // Format 1: filename="example.pdf"
      let match = header.match(/filename="?([^";\n]*?)"?/i);
      if (match && match[1]) {
        return decodeURIComponent(match[1].replace(/['"]/g, ''));
      }

      // Format 2: filename*=UTF-8''example.pdf
      match = header.match(/filename\*=UTF-8''([^;\n]*)/i);
      if (match && match[1]) {
        return decodeURIComponent(match[1].replace(/['"]/g, ''));
      }

      // Format 3: Split by semicolon (original logic as fallback)
      const parts = header.split(';');
      if (parts.length >= 3) {
        const namePart = parts[2].trim().split('=')[1];
        if (namePart) {
          return decodeURI(namePart.replace(/"/g, '')).replace('UTF-8\'\'', '');
        }
      }
    } catch (error) {
      console.warn('Failed to parse filename from Content-Disposition header:', error);
    }

    return def;
  }

  private getMimeTypeExtension(mimeType: string): string {
    const mimeToExtension: { [key: string]: string } = {
      'application/pdf': '.pdf',
      'application/msword': '.doc',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': '.docx',
      'application/vnd.ms-excel': '.xls',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': '.xlsx',
      'application/vnd.ms-powerpoint': '.ppt',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation': '.pptx',
      'application/zip': '.zip',
      'application/x-zip-compressed': '.zip',
      'text/plain': '.txt',
      'text/csv': '.csv',
      'image/jpeg': '.jpg',
      'image/png': '.png',
      'image/gif': '.gif',
      'image/bmp': '.bmp',
      'application/json': '.json',
      'text/html': '.html',
      'application/xml': '.xml',
      'text/xml': '.xml'
    };

    return mimeToExtension[mimeType] || '.bin';
  }

  private hasFileExtension(filename: string): boolean {
    const lastDotIndex = filename.lastIndexOf('.');
    if (lastDotIndex === -1 || lastDotIndex === 0) {
      return false;
    }

    const extension = filename.substring(lastDotIndex + 1);
    // Check if extension is reasonable (1-5 characters, alphanumeric)
    return /^[a-zA-Z0-9]{1,5}$/.test(extension);
  }
}
