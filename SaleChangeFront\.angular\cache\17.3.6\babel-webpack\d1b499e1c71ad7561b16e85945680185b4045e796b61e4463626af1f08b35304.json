{"ast": null, "code": "import { environment } from '../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nexport class UtilityService {\n  constructor() {\n    this.BASE_FILE = environment.BASE_URL_API;\n    this.DownloadFile = data => {\n      if (data) {\n        let downloadedFile;\n        let fileName = 'download';\n        let mimeType = '';\n        if (data instanceof Blob) {\n          // Handle direct Blob\n          downloadedFile = data;\n          mimeType = data.type || 'application/octet-stream';\n        } else {\n          // Handle HttpResponse<Blob>\n          if (!data.body) {\n            console.error('Response body is null');\n            return;\n          }\n          downloadedFile = new Blob([data.body], {\n            type: data.body.type\n          });\n          mimeType = data.body.type || 'application/octet-stream';\n          const contentDisposition = data.headers.get('Content-Disposition');\n          fileName = this.parseFileName(contentDisposition || '', 'download');\n        }\n        // Check if filename already has an extension\n        if (!this.hasFileExtension(fileName)) {\n          const extension = this.getMimeTypeExtension(mimeType);\n          fileName += extension;\n        }\n        const a = document.createElement('a');\n        a.setAttribute('style', 'display:none;');\n        document.body.appendChild(a);\n        a.download = fileName;\n        a.href = URL.createObjectURL(downloadedFile);\n        a.target = '_blank';\n        a.click();\n        document.body.removeChild(a);\n        URL.revokeObjectURL(a.href);\n      }\n    };\n  }\n  downloadFileFromUrl(file) {\n    const xhr = new XMLHttpRequest();\n    xhr.open('GET', `${this.BASE_FILE}${file.CFile}`, true);\n    xhr.responseType = 'blob';\n    xhr.onload = () => {\n      if (xhr.status === 200) {\n        const url = window.URL.createObjectURL(xhr.response);\n        const link = document.createElement('a');\n        link.href = url;\n        link.download = file.CName;\n        link.style.display = 'none';\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        window.URL.revokeObjectURL(url);\n      }\n    };\n    xhr.send();\n  }\n  getFileNameFromUrl(url) {\n    const parts = url.split('/');\n    const fileName = parts.pop();\n    return fileName;\n  }\n  downloadFileFullUrl(file) {\n    const xhr = new XMLHttpRequest();\n    xhr.open('GET', `${file.CFile}?date=${new Date().getTime()}`, true);\n    xhr.responseType = 'blob';\n    xhr.onload = () => {\n      if (xhr.status === 200) {\n        const url = window.URL.createObjectURL(xhr.response);\n        const link = document.createElement('a');\n        link.href = url;\n        link.download = file.CName;\n        link.style.display = 'none';\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        window.URL.revokeObjectURL(url);\n      }\n    };\n    xhr.send();\n  }\n  parseFileName(header, def = 'download') {\n    if (!header) {\n      return def;\n    }\n    try {\n      // Try different Content-Disposition formats\n      // Format 1: filename=\"example.pdf\"\n      let match = header.match(/filename=\"?([^\";\\n]*?)\"?/i);\n      if (match && match[1]) {\n        return decodeURIComponent(match[1].replace(/['\"]/g, ''));\n      }\n      // Format 2: filename*=UTF-8''example.pdf\n      match = header.match(/filename\\*=UTF-8''([^;\\n]*)/i);\n      if (match && match[1]) {\n        return decodeURIComponent(match[1].replace(/['\"]/g, ''));\n      }\n      // Format 3: Split by semicolon (original logic as fallback)\n      const parts = header.split(';');\n      if (parts.length >= 3) {\n        const namePart = parts[2].trim().split('=')[1];\n        if (namePart) {\n          return decodeURI(namePart.replace(/\"/g, '')).replace('UTF-8\\'\\'', '');\n        }\n      }\n    } catch (error) {\n      console.warn('Failed to parse filename from Content-Disposition header:', error);\n    }\n    return def;\n  }\n  getMimeTypeExtension(mimeType) {\n    const mimeToExtension = {\n      'application/pdf': '.pdf',\n      'application/msword': '.doc',\n      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': '.docx',\n      'application/vnd.ms-excel': '.xls',\n      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': '.xlsx',\n      'application/vnd.ms-powerpoint': '.ppt',\n      'application/vnd.openxmlformats-officedocument.presentationml.presentation': '.pptx',\n      'application/zip': '.zip',\n      'application/x-zip-compressed': '.zip',\n      'text/plain': '.txt',\n      'text/csv': '.csv',\n      'image/jpeg': '.jpg',\n      'image/png': '.png',\n      'image/gif': '.gif',\n      'image/bmp': '.bmp',\n      'application/json': '.json',\n      'text/html': '.html',\n      'application/xml': '.xml',\n      'text/xml': '.xml'\n    };\n    return mimeToExtension[mimeType] || '.bin';\n  }\n  hasFileExtension(filename) {\n    const lastDotIndex = filename.lastIndexOf('.');\n    if (lastDotIndex === -1 || lastDotIndex === 0) {\n      return false;\n    }\n    const extension = filename.substring(lastDotIndex + 1);\n    // Check if extension is reasonable (1-5 characters, alphanumeric)\n    return /^[a-zA-Z0-9]{1,5}$/.test(extension);\n  }\n  static #_ = this.ɵfac = function UtilityService_Factory(t) {\n    return new (t || UtilityService)();\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: UtilityService,\n    factory: UtilityService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["environment", "UtilityService", "constructor", "BASE_FILE", "BASE_URL_API", "DownloadFile", "data", "downloadedFile", "fileName", "mimeType", "Blob", "type", "body", "console", "error", "contentDisposition", "headers", "get", "parseFileName", "hasFileExtension", "extension", "getMimeTypeExtension", "a", "document", "createElement", "setAttribute", "append<PERSON><PERSON><PERSON>", "download", "href", "URL", "createObjectURL", "target", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "downloadFileFromUrl", "file", "xhr", "XMLHttpRequest", "open", "CFile", "responseType", "onload", "status", "url", "window", "response", "link", "CName", "style", "display", "send", "getFileNameFromUrl", "parts", "split", "pop", "downloadFileFullUrl", "Date", "getTime", "header", "def", "match", "decodeURIComponent", "replace", "length", "namePart", "trim", "decodeURI", "warn", "mimeToExtension", "filename", "lastDotIndex", "lastIndexOf", "substring", "test", "_", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\SaleChangeFront\\src\\app\\shared\\services\\utility.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpResponse } from '@angular/common/http';\r\nimport { environment } from '../../../environments/environment';\r\nimport { BuildCaseGetFileRespone } from '../../../services/api/models';\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class UtilityService {\r\n  readonly BASE_FILE = environment.BASE_URL_API;\r\n\r\n  constructor() { }\r\n\r\n  downloadFileFromUrl(file: BuildCaseGetFileRespone) {\r\n    const xhr = new XMLHttpRequest();\r\n    xhr.open('GET', `${this.BASE_FILE}${file.CFile}`\r\n      , true);\r\n    xhr.responseType = 'blob';\r\n    xhr.onload = () => {\r\n      if (xhr.status === 200) {\r\n        const url = window.URL.createObjectURL(xhr.response);\r\n        const link = document.createElement('a');\r\n        link.href = url;\r\n        link.download = file.CName!;\r\n        link.style.display = 'none';\r\n        document.body.appendChild(link);\r\n        link.click();\r\n        document.body.removeChild(link);\r\n        window.URL.revokeObjectURL(url);\r\n      }\r\n    };\r\n    xhr.send();\r\n  }\r\n\r\n  getFileNameFromUrl(url: string) {\r\n    const parts = url.split('/');\r\n    const fileName = parts.pop();\r\n    return fileName;\r\n  }\r\n\r\n  downloadFileFullUrl(file: { CFile: string | any; CName: string | any }) {\r\n    const xhr = new XMLHttpRequest();\r\n    xhr.open('GET', `${file.CFile}?date=${new Date().getTime()}`\r\n      , true);\r\n    xhr.responseType = 'blob';\r\n    xhr.onload = () => {\r\n      if (xhr.status === 200) {\r\n        const url = window.URL.createObjectURL(xhr.response);\r\n        const link = document.createElement('a');\r\n        link.href = url;\r\n        link.download = file.CName!;\r\n        link.style.display = 'none';\r\n        document.body.appendChild(link);\r\n        link.click();\r\n        document.body.removeChild(link);\r\n        window.URL.revokeObjectURL(url);\r\n      }\r\n    };\r\n    xhr.send();\r\n  }\r\n\r\n  DownloadFile = (data: Blob | HttpResponse<Blob>) => {\r\n    if (data) {\r\n      let downloadedFile: Blob;\r\n      let fileName: string = 'download';\r\n      let mimeType: string = '';\r\n      \r\n      if (data instanceof Blob) {\r\n        // Handle direct Blob\r\n        downloadedFile = data;\r\n        mimeType = data.type || 'application/octet-stream';\r\n      } else {\r\n        // Handle HttpResponse<Blob>\r\n        if (!data.body) {\r\n          console.error('Response body is null');\r\n          return;\r\n        }\r\n        downloadedFile = new Blob([data.body], { type: data.body.type });\r\n        mimeType = data.body.type || 'application/octet-stream';\r\n        const contentDisposition = data.headers.get('Content-Disposition');\r\n        fileName = this.parseFileName(contentDisposition || '', 'download');\r\n      }\r\n      \r\n      // Check if filename already has an extension\r\n      if (!this.hasFileExtension(fileName)) {\r\n        const extension = this.getMimeTypeExtension(mimeType);\r\n        fileName += extension;\r\n      }\r\n      \r\n      const a = document.createElement('a');\r\n      a.setAttribute('style', 'display:none;');\r\n      document.body.appendChild(a);\r\n      a.download = fileName;\r\n      a.href = URL.createObjectURL(downloadedFile);\r\n      a.target = '_blank';\r\n      a.click();\r\n      document.body.removeChild(a);\r\n      URL.revokeObjectURL(a.href);\r\n    }\r\n  }\r\n\r\n  private parseFileName(header: string, def: string = 'download'): string {\r\n    if (!header) {\r\n      return def;\r\n    }\r\n    \r\n    try {\r\n      // Try different Content-Disposition formats\r\n      // Format 1: filename=\"example.pdf\"\r\n      let match = header.match(/filename=\"?([^\";\\n]*?)\"?/i);\r\n      if (match && match[1]) {\r\n        return decodeURIComponent(match[1].replace(/['\"]/g, ''));\r\n      }\r\n      \r\n      // Format 2: filename*=UTF-8''example.pdf\r\n      match = header.match(/filename\\*=UTF-8''([^;\\n]*)/i);\r\n      if (match && match[1]) {\r\n        return decodeURIComponent(match[1].replace(/['\"]/g, ''));\r\n      }\r\n      \r\n      // Format 3: Split by semicolon (original logic as fallback)\r\n      const parts = header.split(';');\r\n      if (parts.length >= 3) {\r\n        const namePart = parts[2].trim().split('=')[1];\r\n        if (namePart) {\r\n          return decodeURI(namePart.replace(/\"/g, '')).replace('UTF-8\\'\\'', '');\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.warn('Failed to parse filename from Content-Disposition header:', error);\r\n    }\r\n    \r\n    return def;\r\n  }\r\n\r\n  private getMimeTypeExtension(mimeType: string): string {\r\n    const mimeToExtension: { [key: string]: string } = {\r\n      'application/pdf': '.pdf',\r\n      'application/msword': '.doc',\r\n      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': '.docx',\r\n      'application/vnd.ms-excel': '.xls',\r\n      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': '.xlsx',\r\n      'application/vnd.ms-powerpoint': '.ppt',\r\n      'application/vnd.openxmlformats-officedocument.presentationml.presentation': '.pptx',\r\n      'application/zip': '.zip',\r\n      'application/x-zip-compressed': '.zip',\r\n      'text/plain': '.txt',\r\n      'text/csv': '.csv',\r\n      'image/jpeg': '.jpg',\r\n      'image/png': '.png',\r\n      'image/gif': '.gif',\r\n      'image/bmp': '.bmp',\r\n      'application/json': '.json',\r\n      'text/html': '.html',\r\n      'application/xml': '.xml',\r\n      'text/xml': '.xml'\r\n    };\r\n    \r\n    return mimeToExtension[mimeType] || '.bin';\r\n  }\r\n\r\n  private hasFileExtension(filename: string): boolean {\r\n    const lastDotIndex = filename.lastIndexOf('.');\r\n    if (lastDotIndex === -1 || lastDotIndex === 0) {\r\n      return false;\r\n    }\r\n    \r\n    const extension = filename.substring(lastDotIndex + 1);\r\n    // Check if extension is reasonable (1-5 characters, alphanumeric)\r\n    return /^[a-zA-Z0-9]{1,5}$/.test(extension);\r\n  }\r\n}\r\n"], "mappings": "AAEA,SAASA,WAAW,QAAQ,mCAAmC;;AAK/D,OAAM,MAAOC,cAAc;EAGzBC,YAAA;IAFS,KAAAC,SAAS,GAAGH,WAAW,CAACI,YAAY;IAoD7C,KAAAC,YAAY,GAAIC,IAA+B,IAAI;MACjD,IAAIA,IAAI,EAAE;QACR,IAAIC,cAAoB;QACxB,IAAIC,QAAQ,GAAW,UAAU;QACjC,IAAIC,QAAQ,GAAW,EAAE;QAEzB,IAAIH,IAAI,YAAYI,IAAI,EAAE;UACxB;UACAH,cAAc,GAAGD,IAAI;UACrBG,QAAQ,GAAGH,IAAI,CAACK,IAAI,IAAI,0BAA0B;SACnD,MAAM;UACL;UACA,IAAI,CAACL,IAAI,CAACM,IAAI,EAAE;YACdC,OAAO,CAACC,KAAK,CAAC,uBAAuB,CAAC;YACtC;;UAEFP,cAAc,GAAG,IAAIG,IAAI,CAAC,CAACJ,IAAI,CAACM,IAAI,CAAC,EAAE;YAAED,IAAI,EAAEL,IAAI,CAACM,IAAI,CAACD;UAAI,CAAE,CAAC;UAChEF,QAAQ,GAAGH,IAAI,CAACM,IAAI,CAACD,IAAI,IAAI,0BAA0B;UACvD,MAAMI,kBAAkB,GAAGT,IAAI,CAACU,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;UAClET,QAAQ,GAAG,IAAI,CAACU,aAAa,CAACH,kBAAkB,IAAI,EAAE,EAAE,UAAU,CAAC;;QAGrE;QACA,IAAI,CAAC,IAAI,CAACI,gBAAgB,CAACX,QAAQ,CAAC,EAAE;UACpC,MAAMY,SAAS,GAAG,IAAI,CAACC,oBAAoB,CAACZ,QAAQ,CAAC;UACrDD,QAAQ,IAAIY,SAAS;;QAGvB,MAAME,CAAC,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;QACrCF,CAAC,CAACG,YAAY,CAAC,OAAO,EAAE,eAAe,CAAC;QACxCF,QAAQ,CAACX,IAAI,CAACc,WAAW,CAACJ,CAAC,CAAC;QAC5BA,CAAC,CAACK,QAAQ,GAAGnB,QAAQ;QACrBc,CAAC,CAACM,IAAI,GAAGC,GAAG,CAACC,eAAe,CAACvB,cAAc,CAAC;QAC5Ce,CAAC,CAACS,MAAM,GAAG,QAAQ;QACnBT,CAAC,CAACU,KAAK,EAAE;QACTT,QAAQ,CAACX,IAAI,CAACqB,WAAW,CAACX,CAAC,CAAC;QAC5BO,GAAG,CAACK,eAAe,CAACZ,CAAC,CAACM,IAAI,CAAC;;IAE/B,CAAC;EAxFe;EAEhBO,mBAAmBA,CAACC,IAA6B;IAC/C,MAAMC,GAAG,GAAG,IAAIC,cAAc,EAAE;IAChCD,GAAG,CAACE,IAAI,CAAC,KAAK,EAAE,GAAG,IAAI,CAACpC,SAAS,GAAGiC,IAAI,CAACI,KAAK,EAAE,EAC5C,IAAI,CAAC;IACTH,GAAG,CAACI,YAAY,GAAG,MAAM;IACzBJ,GAAG,CAACK,MAAM,GAAG,MAAK;MAChB,IAAIL,GAAG,CAACM,MAAM,KAAK,GAAG,EAAE;QACtB,MAAMC,GAAG,GAAGC,MAAM,CAAChB,GAAG,CAACC,eAAe,CAACO,GAAG,CAACS,QAAQ,CAAC;QACpD,MAAMC,IAAI,GAAGxB,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;QACxCuB,IAAI,CAACnB,IAAI,GAAGgB,GAAG;QACfG,IAAI,CAACpB,QAAQ,GAAGS,IAAI,CAACY,KAAM;QAC3BD,IAAI,CAACE,KAAK,CAACC,OAAO,GAAG,MAAM;QAC3B3B,QAAQ,CAACX,IAAI,CAACc,WAAW,CAACqB,IAAI,CAAC;QAC/BA,IAAI,CAACf,KAAK,EAAE;QACZT,QAAQ,CAACX,IAAI,CAACqB,WAAW,CAACc,IAAI,CAAC;QAC/BF,MAAM,CAAChB,GAAG,CAACK,eAAe,CAACU,GAAG,CAAC;;IAEnC,CAAC;IACDP,GAAG,CAACc,IAAI,EAAE;EACZ;EAEAC,kBAAkBA,CAACR,GAAW;IAC5B,MAAMS,KAAK,GAAGT,GAAG,CAACU,KAAK,CAAC,GAAG,CAAC;IAC5B,MAAM9C,QAAQ,GAAG6C,KAAK,CAACE,GAAG,EAAE;IAC5B,OAAO/C,QAAQ;EACjB;EAEAgD,mBAAmBA,CAACpB,IAAkD;IACpE,MAAMC,GAAG,GAAG,IAAIC,cAAc,EAAE;IAChCD,GAAG,CAACE,IAAI,CAAC,KAAK,EAAE,GAAGH,IAAI,CAACI,KAAK,SAAS,IAAIiB,IAAI,EAAE,CAACC,OAAO,EAAE,EAAE,EACxD,IAAI,CAAC;IACTrB,GAAG,CAACI,YAAY,GAAG,MAAM;IACzBJ,GAAG,CAACK,MAAM,GAAG,MAAK;MAChB,IAAIL,GAAG,CAACM,MAAM,KAAK,GAAG,EAAE;QACtB,MAAMC,GAAG,GAAGC,MAAM,CAAChB,GAAG,CAACC,eAAe,CAACO,GAAG,CAACS,QAAQ,CAAC;QACpD,MAAMC,IAAI,GAAGxB,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;QACxCuB,IAAI,CAACnB,IAAI,GAAGgB,GAAG;QACfG,IAAI,CAACpB,QAAQ,GAAGS,IAAI,CAACY,KAAM;QAC3BD,IAAI,CAACE,KAAK,CAACC,OAAO,GAAG,MAAM;QAC3B3B,QAAQ,CAACX,IAAI,CAACc,WAAW,CAACqB,IAAI,CAAC;QAC/BA,IAAI,CAACf,KAAK,EAAE;QACZT,QAAQ,CAACX,IAAI,CAACqB,WAAW,CAACc,IAAI,CAAC;QAC/BF,MAAM,CAAChB,GAAG,CAACK,eAAe,CAACU,GAAG,CAAC;;IAEnC,CAAC;IACDP,GAAG,CAACc,IAAI,EAAE;EACZ;EA0CQjC,aAAaA,CAACyC,MAAc,EAAEC,GAAA,GAAc,UAAU;IAC5D,IAAI,CAACD,MAAM,EAAE;MACX,OAAOC,GAAG;;IAGZ,IAAI;MACF;MACA;MACA,IAAIC,KAAK,GAAGF,MAAM,CAACE,KAAK,CAAC,2BAA2B,CAAC;MACrD,IAAIA,KAAK,IAAIA,KAAK,CAAC,CAAC,CAAC,EAAE;QACrB,OAAOC,kBAAkB,CAACD,KAAK,CAAC,CAAC,CAAC,CAACE,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;;MAG1D;MACAF,KAAK,GAAGF,MAAM,CAACE,KAAK,CAAC,8BAA8B,CAAC;MACpD,IAAIA,KAAK,IAAIA,KAAK,CAAC,CAAC,CAAC,EAAE;QACrB,OAAOC,kBAAkB,CAACD,KAAK,CAAC,CAAC,CAAC,CAACE,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;;MAG1D;MACA,MAAMV,KAAK,GAAGM,MAAM,CAACL,KAAK,CAAC,GAAG,CAAC;MAC/B,IAAID,KAAK,CAACW,MAAM,IAAI,CAAC,EAAE;QACrB,MAAMC,QAAQ,GAAGZ,KAAK,CAAC,CAAC,CAAC,CAACa,IAAI,EAAE,CAACZ,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC9C,IAAIW,QAAQ,EAAE;UACZ,OAAOE,SAAS,CAACF,QAAQ,CAACF,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,CAACA,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC;;;KAG1E,CAAC,OAAOjD,KAAK,EAAE;MACdD,OAAO,CAACuD,IAAI,CAAC,2DAA2D,EAAEtD,KAAK,CAAC;;IAGlF,OAAO8C,GAAG;EACZ;EAEQvC,oBAAoBA,CAACZ,QAAgB;IAC3C,MAAM4D,eAAe,GAA8B;MACjD,iBAAiB,EAAE,MAAM;MACzB,oBAAoB,EAAE,MAAM;MAC5B,yEAAyE,EAAE,OAAO;MAClF,0BAA0B,EAAE,MAAM;MAClC,mEAAmE,EAAE,OAAO;MAC5E,+BAA+B,EAAE,MAAM;MACvC,2EAA2E,EAAE,OAAO;MACpF,iBAAiB,EAAE,MAAM;MACzB,8BAA8B,EAAE,MAAM;MACtC,YAAY,EAAE,MAAM;MACpB,UAAU,EAAE,MAAM;MAClB,YAAY,EAAE,MAAM;MACpB,WAAW,EAAE,MAAM;MACnB,WAAW,EAAE,MAAM;MACnB,WAAW,EAAE,MAAM;MACnB,kBAAkB,EAAE,OAAO;MAC3B,WAAW,EAAE,OAAO;MACpB,iBAAiB,EAAE,MAAM;MACzB,UAAU,EAAE;KACb;IAED,OAAOA,eAAe,CAAC5D,QAAQ,CAAC,IAAI,MAAM;EAC5C;EAEQU,gBAAgBA,CAACmD,QAAgB;IACvC,MAAMC,YAAY,GAAGD,QAAQ,CAACE,WAAW,CAAC,GAAG,CAAC;IAC9C,IAAID,YAAY,KAAK,CAAC,CAAC,IAAIA,YAAY,KAAK,CAAC,EAAE;MAC7C,OAAO,KAAK;;IAGd,MAAMnD,SAAS,GAAGkD,QAAQ,CAACG,SAAS,CAACF,YAAY,GAAG,CAAC,CAAC;IACtD;IACA,OAAO,oBAAoB,CAACG,IAAI,CAACtD,SAAS,CAAC;EAC7C;EAAC,QAAAuD,CAAA,G;qBAlKU1E,cAAc;EAAA;EAAA,QAAA2E,EAAA,G;WAAd3E,cAAc;IAAA4E,OAAA,EAAd5E,cAAc,CAAA6E,IAAA;IAAAC,UAAA,EAFb;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}