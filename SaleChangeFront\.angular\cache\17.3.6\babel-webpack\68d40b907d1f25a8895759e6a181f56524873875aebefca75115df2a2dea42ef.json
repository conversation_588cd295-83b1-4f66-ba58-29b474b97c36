{"ast": null, "code": "import { ServiceBase } from './service-base';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class FileService extends ServiceBase {\n  constructor(http) {\n    super(http);\n    this.baseUrl = `${this.apiBaseUrl}/File`;\n  }\n  getFile(relativePath, fileName) {\n    const url = `${this.baseUrl}/GetFile`;\n    const params = {\n      relativePath: relativePath,\n      fileName: fileName\n    };\n    return this.http.get(url, {\n      params: params,\n      responseType: 'blob',\n      observe: 'response'\n    });\n  }\n  static #_ = this.ɵfac = function FileService_Factory(t) {\n    return new (t || FileService)(i0.ɵɵinject(i1.HttpClient));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: FileService,\n    factory: FileService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["ServiceBase", "FileService", "constructor", "http", "baseUrl", "apiBaseUrl", "getFile", "relativePath", "fileName", "url", "params", "get", "responseType", "observe", "_", "i0", "ɵɵinject", "i1", "HttpClient", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\SaleChangeFront\\src\\services\\File.service.ts"], "sourcesContent": ["import { HttpClient, HttpResponse } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { Observable } from 'rxjs';\r\nimport { ServiceBase } from './service-base';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class FileService extends ServiceBase {\r\n\r\n  protected baseUrl = `${this.apiBaseUrl}/File`;\r\n  constructor(\r\n    http: HttpClient\r\n  ) {\r\n    super(http);\r\n  }\r\n\r\n  getFile(relativePath: string, fileName: string): Observable<HttpResponse<Blob>> {\r\n    const url = `${this.baseUrl}/GetFile`;\r\n    const params = {\r\n      relativePath: relativePath,\r\n      fileName: fileName\r\n    };\r\n\r\n    return this.http.get(url, {\r\n      params: params,\r\n      responseType: 'blob',\r\n      observe: 'response'\r\n    });\r\n  }\r\n}\r\n"], "mappings": "AAGA,SAASA,WAAW,QAAQ,gBAAgB;;;AAK5C,OAAM,MAAOC,WAAY,SAAQD,WAAW;EAG1CE,YACEC,IAAgB;IAEhB,KAAK,CAACA,IAAI,CAAC;IAJH,KAAAC,OAAO,GAAG,GAAG,IAAI,CAACC,UAAU,OAAO;EAK7C;EAEAC,OAAOA,CAACC,YAAoB,EAAEC,QAAgB;IAC5C,MAAMC,GAAG,GAAG,GAAG,IAAI,CAACL,OAAO,UAAU;IACrC,MAAMM,MAAM,GAAG;MACbH,YAAY,EAAEA,YAAY;MAC1BC,QAAQ,EAAEA;KACX;IAED,OAAO,IAAI,CAACL,IAAI,CAACQ,GAAG,CAACF,GAAG,EAAE;MACxBC,MAAM,EAAEA,MAAM;MACdE,YAAY,EAAE,MAAM;MACpBC,OAAO,EAAE;KACV,CAAC;EACJ;EAAC,QAAAC,CAAA,G;qBArBUb,WAAW,EAAAc,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAXlB,WAAW;IAAAmB,OAAA,EAAXnB,WAAW,CAAAoB,IAAA;IAAAC,UAAA,EAFV;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}