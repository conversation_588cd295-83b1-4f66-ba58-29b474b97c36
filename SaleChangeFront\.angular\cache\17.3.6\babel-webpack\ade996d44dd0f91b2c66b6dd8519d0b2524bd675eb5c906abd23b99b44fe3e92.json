{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { MessageService } from 'primeng/api';\nimport { PanelModule } from 'primeng/panel';\nimport { PanelMenuModule } from 'primeng/panelmenu';\nimport { ToastModule } from 'primeng/toast';\nimport { DateFormatPipe } from '../../shared/pipes/date-format.pipe';\nimport { LocalStorageService } from '../../shared/services/local-storage.service';\nimport { STORAGE_KEY } from '../../shared/constant/constant';\nimport { ToastMessage } from '../../shared/services/message.service';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../services/api/services\";\nimport * as i2 from \"../../shared/services/utility.service\";\nimport * as i3 from \"../../shared/services/message.service\";\nimport * as i4 from \"../../../services/File.service\";\nimport * as i5 from \"@angular/router\";\nimport * as i6 from \"primeng/panel\";\nimport * as i7 from \"primeng/api\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"primeng/toast\";\nfunction HistoryComponent_span_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u6211\\u8981\\u4E0A\\u50B3\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HistoryComponent_span_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u8655\\u7406\\u4E2D...\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HistoryComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"div\", 22);\n    i0.ɵɵelement(2, \"i\", 23);\n    i0.ɵɵtext(3, \" \\u652F\\u63F4 PDF\\u3001DWG\\u3001DXF\\u3001DWF \\u683C\\u5F0F\\uFF0C\\u6A94\\u6848\\u5927\\u5C0F\\u9650\\u5236 10MB \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 24)(5, \"div\", 25);\n    i0.ɵɵelement(6, \"i\", 26);\n    i0.ɵɵelementStart(7, \"span\");\n    i0.ɵɵtext(8, \"\\u652F\\u63F4\\u591A\\u6A94\\u6848\\u540C\\u6642\\u9078\\u64C7\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 25);\n    i0.ɵɵelement(10, \"i\", 26);\n    i0.ɵɵelementStart(11, \"span\");\n    i0.ɵɵtext(12, \"\\u62D6\\u62FD\\u6A94\\u6848\\u5230\\u6B64\\u8655\\u4E0A\\u50B3\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction HistoryComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 27);\n    i0.ɵɵlistener(\"drop\", function HistoryComponent_div_14_Template_div_drop_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onDragDrop($event));\n    })(\"dragover\", function HistoryComponent_div_14_Template_div_dragover_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onDragOver($event));\n    })(\"dragleave\", function HistoryComponent_div_14_Template_div_dragleave_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onDragLeave($event));\n    })(\"click\", function HistoryComponent_div_14_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      i0.ɵɵnextContext();\n      const fileInput_r4 = i0.ɵɵreference(16);\n      return i0.ɵɵresetView(fileInput_r4.click());\n    });\n    i0.ɵɵelementStart(1, \"div\", 28);\n    i0.ɵɵelement(2, \"i\", 29);\n    i0.ɵɵelementStart(3, \"div\", 30);\n    i0.ɵɵtext(4, \"\\u62D6\\u62FD\\u6A94\\u6848\\u5230\\u6B64\\u8655\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 31);\n    i0.ɵɵtext(6, \"\\u6216\\u9EDE\\u64CA\\u9078\\u64C7\\u591A\\u500B\\u6A94\\u6848\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 32)(8, \"span\", 33);\n    i0.ɵɵtext(9, \"PDF\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"span\", 33);\n    i0.ɵɵtext(11, \"DWG\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\", 33);\n    i0.ɵɵtext(13, \"DXF\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"span\", 33);\n    i0.ɵɵtext(15, \"DWF\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"drag-over\", ctx_r2.isDragOver);\n  }\n}\nfunction HistoryComponent_div_17_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 45);\n  }\n}\nfunction HistoryComponent_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"p-panel\", 35);\n    i0.ɵɵtemplate(2, HistoryComponent_div_17_ng_template_2_Template, 1, 0, \"ng-template\", 36);\n    i0.ɵɵelementStart(3, \"div\", 37)(4, \"span\", 38);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 39)(7, \"button\", 40);\n    i0.ɵɵlistener(\"click\", function HistoryComponent_div_17_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.downloadFileWithoutRedirect(ctx_r2.houseHoldDetaiPicture));\n    });\n    i0.ɵɵelementStart(8, \"div\", 41);\n    i0.ɵɵelement(9, \"img\", 42);\n    i0.ɵɵelementStart(10, \"span\", 43);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(12, \"i\", 44);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"toggleable\", true);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r2.houseHoldDetaiPicture.CName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"title\", ctx_r2.getDownloadTitle(ctx_r2.houseHoldDetaiPicture.CName));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", ctx_r2.getDownloadIcon(ctx_r2.houseHoldDetaiPicture.CName), i0.ɵɵsanitizeUrl)(\"alt\", ctx_r2.getFileTypeText(ctx_r2.houseHoldDetaiPicture.CName));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.getFileTypeText(ctx_r2.houseHoldDetaiPicture.CName));\n  }\n}\nfunction HistoryComponent_div_18_ng_template_2_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 59);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\\u5171 \", ctx_r2.getTotalFilesCount(), \" \\u500B\\u6A94\\u6848\");\n  }\n}\nfunction HistoryComponent_div_18_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 50)(1, \"div\", 51);\n    i0.ɵɵelement(2, \"i\", 52);\n    i0.ɵɵelementStart(3, \"span\", 53);\n    i0.ɵɵtext(4, \"\\u6A94\\u6848\\u8A18\\u9304\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, HistoryComponent_div_18_ng_template_2_span_5_Template, 2, 1, \"span\", 54);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 55)(7, \"div\", 56)(8, \"button\", 57);\n    i0.ɵɵlistener(\"click\", function HistoryComponent_div_18_ng_template_2_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.expandAllGroups());\n    });\n    i0.ɵɵtext(9, \" \\u5C55\\u958B\\u5168\\u90E8 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 58);\n    i0.ɵɵlistener(\"click\", function HistoryComponent_div_18_ng_template_2_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.collapseAllGroups());\n    });\n    i0.ɵɵtext(11, \" \\u6536\\u5408\\u5168\\u90E8 \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getTotalFilesCount() > 0);\n  }\n}\nfunction HistoryComponent_div_18_div_3_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 64);\n    i0.ɵɵlistener(\"click\", function HistoryComponent_div_18_div_3_div_1_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const file_r8 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.toggleDateGroup(file_r8.CChangeDate));\n    });\n    i0.ɵɵelementStart(1, \"div\", 65);\n    i0.ɵɵelement(2, \"i\", 66);\n    i0.ɵɵelementStart(3, \"span\", 67);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"dateFormat\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 55)(7, \"div\", 68)(8, \"span\", 69);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"span\", 70);\n    i0.ɵɵtext(11, \"\\u500B\\u6A94\\u6848\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 71);\n    i0.ɵɵelement(13, \"i\", 72);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const file_r8 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(5, 6, file_r8.CChangeDate));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(file_r8.SpecialChangeFiles.length);\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"pi-chevron-down\", !ctx_r2.isDateGroupCollapsed(file_r8.CChangeDate))(\"pi-chevron-up\", ctx_r2.isDateGroupCollapsed(file_r8.CChangeDate));\n  }\n}\nfunction HistoryComponent_div_18_div_3_div_3_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 78)(1, \"span\", 79);\n    i0.ɵɵelement(2, \"i\", 72);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r9 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(ctx_r2.getSourceClass(item_r9.CSource));\n    i0.ɵɵproperty(\"title\", ctx_r2.getSourceDescription(item_r9.CSource));\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(ctx_r2.getSourceIcon(item_r9.CSource));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.getSourceName(item_r9.CSource), \" \");\n  }\n}\nfunction HistoryComponent_div_18_div_3_div_3_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"button\", 40);\n    i0.ɵɵlistener(\"click\", function HistoryComponent_div_18_div_3_div_3_div_7_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const item_r9 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.downloadFileWithoutRedirect({\n        CFile: item_r9.CFile || \"\",\n        CName: item_r9.CFileName || \"\"\n      }));\n    });\n    i0.ɵɵelementStart(2, \"div\", 41);\n    i0.ɵɵelement(3, \"img\", 42);\n    i0.ɵɵelementStart(4, \"span\", 43);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(6, \"i\", 44);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r9 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"title\", ctx_r2.getDownloadTitle(item_r9.CFileName));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", ctx_r2.getDownloadIcon(item_r9.CFileName), i0.ɵɵsanitizeUrl)(\"alt\", ctx_r2.getFileTypeText(item_r9.CFileName));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.getFileTypeText(item_r9.CFileName));\n  }\n}\nfunction HistoryComponent_div_18_div_3_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 73)(1, \"div\", 74)(2, \"div\", 75);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 38);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, HistoryComponent_div_18_div_3_div_3_div_6_Template, 4, 6, \"div\", 76);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, HistoryComponent_div_18_div_3_div_3_div_7_Template, 7, 4, \"div\", 77);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r9 = ctx.$implicit;\n    const i_r11 = ctx.index;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i_r11 + 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r9.CFileName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getSourceName(item_r9.CSource));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r9.CFile);\n  }\n}\nfunction HistoryComponent_div_18_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 60);\n    i0.ɵɵtemplate(1, HistoryComponent_div_18_div_3_div_1_Template, 14, 8, \"div\", 61);\n    i0.ɵɵelementStart(2, \"div\", 62);\n    i0.ɵɵtemplate(3, HistoryComponent_div_18_div_3_div_3_Template, 8, 4, \"div\", 63);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const file_r8 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"collapsed\", ctx_r2.isDateGroupCollapsed(file_r8.CChangeDate));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", file_r8.SpecialChangeFiles.length);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"collapsed\", ctx_r2.isDateGroupCollapsed(file_r8.CChangeDate));\n    i0.ɵɵproperty(\"@slideInOut\", ctx_r2.isDateGroupCollapsed(file_r8.CChangeDate) ? \"out\" : \"in\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", file_r8.SpecialChangeFiles);\n  }\n}\nfunction HistoryComponent_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"p-panel\", 47);\n    i0.ɵɵtemplate(2, HistoryComponent_div_18_ng_template_2_Template, 12, 1, \"ng-template\", 48)(3, HistoryComponent_div_18_div_3_Template, 4, 7, \"div\", 49);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"toggleable\", false);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.listSpecialChange);\n  }\n}\nfunction HistoryComponent_div_19_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 90)(1, \"div\", 91);\n    i0.ɵɵelement(2, \"i\", 92);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"\\u5F85\\u4E0A\\u50B3\\u6A94\\u6848\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 93);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 94);\n    i0.ɵɵtext(8, \" \\u8ACB\\u78BA\\u8A8D\\u4EE5\\u4E0B\\u6A94\\u6848\\u5F8C\\u9EDE\\u64CA\\u4E0A\\u50B3 \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r2.uploadedFiles.length);\n  }\n}\nfunction HistoryComponent_div_19_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 95)(1, \"div\", 96);\n    i0.ɵɵelement(2, \"i\", 97);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 74)(4, \"div\", 98);\n    i0.ɵɵelement(5, \"img\", 99);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 100)(7, \"span\", 101);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 102)(10, \"span\", 103);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\", 104);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(14, \"div\", 105)(15, \"button\", 106);\n    i0.ɵɵlistener(\"click\", function HistoryComponent_div_19_div_5_Template_button_click_15_listener() {\n      const i_r14 = i0.ɵɵrestoreView(_r13).index;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.removeFile(i_r14));\n    });\n    i0.ɵɵelement(16, \"i\", 89);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const file_r15 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"src\", ctx_r2.getFileIcon(file_r15.name), i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"title\", file_r15.name);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(file_r15.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r2.formatFileSize(file_r15.size));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.getFileType(file_r15.name));\n  }\n}\nfunction HistoryComponent_div_19_span_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u78BA\\u8A8D\\u4E0A\\u50B3\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HistoryComponent_div_19_span_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u4E0A\\u50B3\\u4E2D...\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HistoryComponent_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 80)(1, \"p-panel\", 81);\n    i0.ɵɵtemplate(2, HistoryComponent_div_19_ng_template_2_Template, 9, 1, \"ng-template\", 48);\n    i0.ɵɵelementStart(3, \"div\", 82)(4, \"div\", 83);\n    i0.ɵɵtemplate(5, HistoryComponent_div_19_div_5_Template, 17, 5, \"div\", 84);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 85)(7, \"button\", 86);\n    i0.ɵɵlistener(\"click\", function HistoryComponent_div_19_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.uploadFiles());\n    });\n    i0.ɵɵelement(8, \"i\", 87);\n    i0.ɵɵtemplate(9, HistoryComponent_div_19_span_9_Template, 2, 0, \"span\", 10)(10, HistoryComponent_div_19_span_10_Template, 2, 0, \"span\", 10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"button\", 88);\n    i0.ɵɵlistener(\"click\", function HistoryComponent_div_19_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.clearFiles());\n    });\n    i0.ɵɵelement(12, \"i\", 89);\n    i0.ɵɵtext(13, \" \\u53D6\\u6D88 \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"toggleable\", false);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.uploadedFiles);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.isUploading);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isUploading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isUploading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r2.isUploading);\n  }\n}\nfunction HistoryComponent_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 107)(1, \"div\", 108);\n    i0.ɵɵelement(2, \"i\", 109);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.uploadError, \" \");\n  }\n}\nexport class HistoryComponent {\n  constructor(_houseService, _specialChangeService, _utilityService, _toastService, _fileService) {\n    this._houseService = _houseService;\n    this._specialChangeService = _specialChangeService;\n    this._utilityService = _utilityService;\n    this._toastService = _toastService;\n    this._fileService = _fileService;\n    this.items = [];\n    this.houseHoldDetaiPicture = {\n      CFile: '',\n      CName: ''\n    };\n    this.specialChangeFiles = [];\n    this.dataUser = LocalStorageService.GetLocalStorage(STORAGE_KEY.SAVE_LOGIN);\n    // 檔案上傳相關屬性\n    this.uploadedFiles = [];\n    this.isUploading = false;\n    this.uploadError = '';\n    this.maxFileSize = 10 * 1024 * 1024; // 10MB\n    this.allowedFileTypes = ['.pdf', '.dwg', '.dxf', '.dwf'];\n    this.allowedMimeTypes = ['application/pdf', 'image/vnd.dwg', 'application/acad', 'application/x-acad'];\n    this.isDragOver = false;\n    this.showUploadArea = false; // 控制上傳區域顯示\n    // 收合狀態管理\n    this.collapsedStates = {};\n  }\n  ngOnInit() {\n    this.getHouseHoldDetaiPic();\n    this.buildCaseId = this.dataUser.buildCaseId;\n    this.holdDetailId = this.dataUser.holdDetailId;\n    this.getListSpecialChange();\n  }\n  getListSpecialChange() {\n    this._specialChangeService.apiSpecialChangeGetSpecialChangeFilePost$Json({\n      body: this.buildCaseId\n    }).subscribe(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.listSpecialChange = res.Entries ?? [];\n        // 初始化收合狀態：預設展開前三個日期群組\n        this.initializeCollapsedStates();\n      }\n    });\n  }\n  // 初始化收合狀態\n  initializeCollapsedStates() {\n    if (this.listSpecialChange && Array.isArray(this.listSpecialChange)) {\n      this.listSpecialChange.forEach((group, index) => {\n        // 前三個群組預設展開（false = 展開），其餘收合（true = 收合）\n        this.collapsedStates[group.CChangeDate] = index >= 3;\n      });\n    }\n  }\n  getHouseHoldDetaiPic() {\n    this._houseService.apiHouseGetHouseRegularPicturePost$Json({}).subscribe(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.houseHoldDetaiPicture = {\n          CFile: res.Entries.CFileURL ? res.Entries.CFileURL : '',\n          CName: res.Entries.CFileName\n        };\n      }\n    });\n  }\n  getFileNameFromUrl(url) {\n    const parts = url.split('/');\n    const fileName = parts.pop();\n    return fileName;\n  }\n  downloadFileWithoutRedirect(files) {\n    if (files.CFile && files.CName) {\n      this._fileService.getFile(files.CFile, files.CName).subscribe({\n        next: response => {\n          if (response.body) {\n            // 創建下載鏈接\n            const url = window.URL.createObjectURL(response.body);\n            const link = document.createElement('a');\n            link.href = url;\n            link.download = files.CName || 'download';\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n            window.URL.revokeObjectURL(url);\n          }\n        },\n        error: error => {\n          console.error('檔案下載失敗:', error);\n          this._toastService.showErrorMSG('檔案下載失敗');\n          // 如果 FileService 失敗，回退到原來的方法\n          window.open(files.CFile, '_blank');\n        }\n      });\n    }\n  }\n  // 檔案選擇事件處理\n  onFileSelected(event) {\n    const input = event.target;\n    if (input.files) {\n      this.handleFiles(Array.from(input.files));\n    }\n  }\n  // 拖拽事件處理\n  onDragOver(event) {\n    event.preventDefault();\n    event.stopPropagation();\n    this.isDragOver = true;\n  }\n  onDragLeave(event) {\n    event.preventDefault();\n    event.stopPropagation();\n    this.isDragOver = false;\n  }\n  onDragDrop(event) {\n    event.preventDefault();\n    event.stopPropagation();\n    this.isDragOver = false;\n    const files = event.dataTransfer?.files;\n    if (files) {\n      this.handleFiles(Array.from(files));\n    }\n  }\n  // 處理檔案\n  handleFiles(files) {\n    this.uploadError = '';\n    for (const file of files) {\n      if (!this.validateFile(file)) {\n        continue;\n      }\n      // 檢查是否已存在相同檔案\n      if (!this.uploadedFiles.some(f => f.name === file.name)) {\n        this.uploadedFiles.push(file);\n      }\n    }\n  }\n  // 檔案驗證\n  validateFile(file) {\n    // 檢查檔案大小\n    if (file.size > this.maxFileSize) {\n      this.uploadError = `檔案 \"${file.name}\" 超過大小限制 (10MB)`;\n      return false;\n    }\n    // 檢查檔案類型\n    const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();\n    if (!this.allowedFileTypes.includes(fileExtension)) {\n      this.uploadError = `檔案 \"${file.name}\" 格式不支援，只支援 PDF、DWG、DXF、DWF 格式`;\n      return false;\n    }\n    return true;\n  }\n  // 移除檔案\n  removeFile(index) {\n    this.uploadedFiles.splice(index, 1);\n    this.uploadError = '';\n  }\n  // 格式化檔案大小\n  formatFileSize(bytes) {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  }\n  // 上傳檔案\n  uploadFiles() {\n    if (this.uploadedFiles.length === 0) {\n      this.uploadError = '請選擇要上傳的檔案';\n      return;\n    }\n    this.isUploading = true;\n    this.uploadError = '';\n    // 將所有檔案轉換為 data URL 格式（包含 MIME 類型）\n    const filePromises = this.uploadedFiles.map(file => this.convertFileToBase64(file));\n    Promise.all(filePromises).then(dataUrls => {\n      // 組成 SpecialChangeFile 陣列\n      const specialChangeFiles = dataUrls.map((dataUrl, index) => ({\n        CFileBlood: dataUrl,\n        CFileName: this.uploadedFiles[index].name,\n        CFileType: this.getFileTypeFromExtension(this.uploadedFiles[index].name)\n      }));\n      // 調用 API\n      this._specialChangeService.apiSpecialChangeUploadSpecialChangePost$Json({\n        body: specialChangeFiles\n      }).subscribe({\n        next: response => {\n          this.isUploading = false;\n          this.uploadedFiles = [];\n          this.showUploadArea = false; // 上傳成功後隱藏上傳區域\n          this.getListSpecialChange(); // 重新載入檔案列表\n          // 顯示成功訊息\n          this._toastService.showSucessMSG('檔案上傳成功');\n        },\n        error: error => {\n          this.isUploading = false;\n          this.uploadError = '上傳失敗，請稍後再試';\n          console.error('Upload error:', error);\n          this._toastService.showErrorMSG('檔案上傳失敗：' + (error.error?.Message || '未知錯誤'));\n        }\n      });\n    }).catch(error => {\n      this.isUploading = false;\n      this.uploadError = '檔案處理失敗';\n      console.error('File processing error:', error);\n      this._toastService.showErrorMSG('檔案處理失敗');\n    });\n  }\n  // 將檔案轉換為 base64 純字串（不含 data URL 前綴）\n  convertFileToBase64(file) {\n    return new Promise((resolve, reject) => {\n      const reader = new FileReader();\n      reader.readAsDataURL(file);\n      reader.onload = () => {\n        const result = reader.result;\n        // 只取 base64 部分\n        const base64 = result.split(',')[1];\n        resolve(base64);\n      };\n      reader.onerror = error => reject(error);\n    });\n  }\n  // 根據檔案副檔名判斷檔案類型\n  getFileTypeFromExtension(fileName) {\n    const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));\n    switch (extension) {\n      case '.pdf':\n        return 1;\n      // 假設 1 代表 PDF\n      case '.dwg':\n        return 2;\n      // 假設 2 代表 DWG\n      case '.dxf':\n        return 3;\n      // 假設 3 代表 DXF\n      case '.dwf':\n        return 4;\n      // 假設 4 代表 DWF\n      default:\n        return 0;\n      // 未知類型\n    }\n  }\n  // 清除所有待上傳檔案\n  clearFiles() {\n    this.uploadedFiles = [];\n    this.uploadError = '';\n    this.showUploadArea = false; // 清除檔案時隱藏上傳區域\n  }\n  // 根據檔案名稱獲取對應圖標\n  getFileIcon(fileName) {\n    const extension = fileName.split('.').pop()?.toLowerCase();\n    switch (extension) {\n      case 'pdf':\n        return 'assets/PDF.svg';\n      case 'dwg':\n      case 'dxf':\n      case 'dwf':\n        return 'assets/designFile.svg';\n      case 'jpg':\n      case 'jpeg':\n      case 'png':\n      case 'gif':\n      case 'bmp':\n      case 'webp':\n        return 'assets/imageFile.svg';\n      default:\n        return 'assets/PDF.svg';\n    }\n  }\n  // 根據檔案名稱獲取檔案類型顯示文字\n  getFileType(fileName) {\n    const extension = fileName.split('.').pop()?.toLowerCase();\n    switch (extension) {\n      case 'pdf':\n        return 'PDF檔案';\n      case 'dwg':\n        return 'AutoCAD圖檔';\n      case 'dxf':\n        return 'CAD交換檔';\n      case 'dwf':\n        return 'CAD檢視檔';\n      case 'jpg':\n      case 'jpeg':\n        return 'JPEG圖片';\n      case 'png':\n        return 'PNG圖片';\n      case 'gif':\n        return 'GIF圖片';\n      case 'bmp':\n        return 'BMP圖片';\n      case 'webp':\n        return 'WebP圖片';\n      default:\n        return '未知格式';\n    }\n  }\n  // 根據檔案名稱獲取檔案類型簡短文字（用於下載按鈕）\n  getFileTypeText(fileName) {\n    const extension = fileName.split('.').pop()?.toLowerCase();\n    switch (extension) {\n      case 'pdf':\n        return 'PDF';\n      case 'dwg':\n      case 'dxf':\n      case 'dwf':\n        return 'CAD';\n      case 'jpg':\n      case 'jpeg':\n      case 'png':\n      case 'gif':\n      case 'bmp':\n      case 'webp':\n        return '圖片';\n      default:\n        return '檔案';\n    }\n  }\n  // 根據檔案名稱獲取下載圖示\n  getDownloadIcon(fileName) {\n    const extension = fileName.split('.').pop()?.toLowerCase();\n    switch (extension) {\n      case 'pdf':\n        return 'assets/PDF.svg';\n      case 'dwg':\n      case 'dxf':\n      case 'dwf':\n        return 'assets/designFile.svg';\n      case 'jpg':\n      case 'jpeg':\n      case 'png':\n      case 'gif':\n      case 'bmp':\n      case 'webp':\n        return 'assets/imageFile.svg';\n      default:\n        return 'assets/PDF.svg';\n    }\n  }\n  // 根據檔案名稱獲取下載提示文字\n  getDownloadTitle(fileName) {\n    const extension = fileName.split('.').pop()?.toLowerCase();\n    switch (extension) {\n      case 'pdf':\n        return '下載 PDF 檔案';\n      case 'dwg':\n        return '下載 AutoCAD 圖檔';\n      case 'dxf':\n        return '下載 CAD 交換檔';\n      case 'dwf':\n        return '下載 CAD 檢視檔';\n      case 'jpg':\n      case 'jpeg':\n        return '下載 JPEG 圖片';\n      case 'png':\n        return '下載 PNG 圖片';\n      case 'gif':\n        return '下載 GIF 圖片';\n      case 'bmp':\n        return '下載 BMP 圖片';\n      case 'webp':\n        return '下載 WebP 圖片';\n      default:\n        return '下載檔案';\n    }\n  }\n  // 切換上傳區域顯示\n  toggleUploadArea() {\n    this.showUploadArea = !this.showUploadArea;\n    if (!this.showUploadArea) {\n      // 隱藏上傳區域時清除檔案和錯誤訊息\n      this.clearFiles();\n    }\n  }\n  // 計算總檔案數量\n  getTotalFilesCount() {\n    if (!this.listSpecialChange || !Array.isArray(this.listSpecialChange)) {\n      return 0;\n    }\n    return this.listSpecialChange.reduce((total, group) => {\n      return total + (group.SpecialChangeFiles ? group.SpecialChangeFiles.length : 0);\n    }, 0);\n  }\n  // 切換日期群組的收合狀態\n  toggleDateGroup(changeDate) {\n    this.collapsedStates[changeDate] = !this.collapsedStates[changeDate];\n  }\n  // 檢查日期群組是否已收合\n  isDateGroupCollapsed(changeDate) {\n    return this.collapsedStates[changeDate] || false;\n  }\n  // 展開所有日期群組\n  expandAllGroups() {\n    Object.keys(this.collapsedStates).forEach(key => {\n      this.collapsedStates[key] = false;\n    });\n  }\n  // 收合所有日期群組\n  collapseAllGroups() {\n    Object.keys(this.collapsedStates).forEach(key => {\n      this.collapsedStates[key] = true;\n    });\n  }\n  // 根據來源代碼獲取來源名稱\n  getSourceName(source) {\n    switch (source) {\n      case 1:\n        return '系統';\n      case 2:\n        return '住戶';\n      case 3:\n        return '洽談';\n      default:\n        return '';\n    }\n  }\n  // 根據來源代碼獲取來源樣式類別\n  getSourceClass(source) {\n    switch (source) {\n      case 1:\n        return 'source-system';\n      case 2:\n        return 'source-resident';\n      case 3:\n        return 'source-negotiation';\n      default:\n        return 'source-unknown';\n    }\n  }\n  // 根據來源代碼獲取來源圖標\n  getSourceIcon(source) {\n    switch (source) {\n      case 1:\n        return 'pi-cog';\n      // 系統齒輪圖標\n      case 2:\n        return 'pi-home';\n      // 住戶房屋圖標\n      case 3:\n        return 'pi-comments';\n      // 洽談對話圖標\n      default:\n        return 'pi-question';\n    }\n  }\n  // 根據來源代碼獲取來源描述\n  getSourceDescription(source) {\n    switch (source) {\n      case 1:\n        return '由系統自動產生';\n      case 2:\n        return '住戶上傳提供';\n      case 3:\n        return '洽談過程中產生';\n      default:\n        return '來源不明';\n    }\n  }\n  // 獲取日期群組中各來源的檔案統計\n  getSourceStats(files) {\n    const stats = {};\n    files.forEach(file => {\n      const source = file.CSource || 0;\n      stats[source] = (stats[source] || 0) + 1;\n    });\n    return stats;\n  }\n  // 檢查是否有多種來源\n  hasMultipleSources(files) {\n    const sources = new Set(files.map(file => file.CSource).filter(source => source));\n    return sources.size > 1;\n  }\n  static #_ = this.ɵfac = function HistoryComponent_Factory(t) {\n    return new (t || HistoryComponent)(i0.ɵɵdirectiveInject(i1.HouseService), i0.ɵɵdirectiveInject(i1.SpecialChangeService), i0.ɵɵdirectiveInject(i2.UtilityService), i0.ɵɵdirectiveInject(i3.ToastMessage), i0.ɵɵdirectiveInject(i4.FileService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: HistoryComponent,\n    selectors: [[\"app-history\"]],\n    standalone: true,\n    features: [i0.ɵɵProvidersFeature([MessageService, ToastMessage]), i0.ɵɵStandaloneFeature],\n    decls: 26,\n    vars: 9,\n    consts: [[\"fileInput\", \"\"], [1, \"wrapper\"], [1, \"content\"], [1, \"flex\", \"justify-center\"], [1, \"history\"], [1, \"history-header\"], [1, \"title\"], [1, \"upload-button-container\"], [1, \"button2\", \"upload-button\", 3, \"click\", \"disabled\"], [1, \"pi\", \"pi-upload\"], [4, \"ngIf\"], [\"class\", \"upload-instructions\", 4, \"ngIf\"], [\"class\", \"drag-drop-zone\", 3, \"drag-over\", \"drop\", \"dragover\", \"dragleave\", \"click\", 4, \"ngIf\"], [\"type\", \"file\", \"accept\", \".pdf,.dwg,.dxf,.dwf\", \"multiple\", \"\", 1, \"file-input-hidden\", 2, \"display\", \"none\", 3, \"change\"], [\"class\", \"card flex justify-content-center mb-3\", 4, \"ngIf\"], [\"class\", \"card flex justify-content-center w-full\", 4, \"ngIf\"], [\"class\", \"card flex justify-content-center w-full mb-3\", 4, \"ngIf\"], [\"class\", \"error-message-container\", 4, \"ngIf\"], [1, \"my-4\", \"h-32\"], [1, \"button1\", \"!w-48\"], [\"routerLink\", \"/\"], [1, \"upload-instructions\"], [1, \"upload-hint-text-header\"], [1, \"pi\", \"pi-info-circle\"], [1, \"upload-features\"], [1, \"feature-item\"], [1, \"pi\", \"pi-check-circle\"], [1, \"drag-drop-zone\", 3, \"drop\", \"dragover\", \"dragleave\", \"click\"], [1, \"drag-drop-content\"], [1, \"pi\", \"pi-cloud-upload\", \"drag-icon\"], [1, \"drag-text-primary\"], [1, \"drag-text-secondary\"], [1, \"supported-formats\"], [1, \"format-badge\"], [1, \"card\", \"flex\", \"justify-content-center\", \"mb-3\"], [\"header\", \"\\u6A19\\u6E96\\u5716\\u9762\", 1, \"w-full\", 3, \"toggleable\"], [\"pTemplate\", \"headericons\"], [1, \"pafbox\"], [1, \"file-name\"], [1, \"download-actions\"], [1, \"download-btn\", 3, \"click\", \"title\"], [1, \"btn-content\"], [1, \"file-type-icon\", 3, \"src\", \"alt\"], [1, \"download-text\"], [1, \"pi\", \"pi-download\", \"download-icon\"], [\"src\", \"/assets/Vector.svg\", 1, \"black-arrow\"], [1, \"card\", \"flex\", \"justify-content-center\", \"w-full\"], [1, \"w-full\", \"files-panel\", 3, \"toggleable\"], [\"pTemplate\", \"header\"], [\"class\", \"date-group\", 3, \"collapsed\", 4, \"ngFor\", \"ngForOf\"], [1, \"panel-header-content\"], [1, \"header-left\"], [1, \"pi\", \"pi-folder-open\"], [1, \"panel-title\"], [\"class\", \"file-total-badge\", 4, \"ngIf\"], [1, \"header-right\"], [1, \"control-buttons\"], [1, \"expand-btn\", 3, \"click\"], [1, \"collapse-btn\", 3, \"click\"], [1, \"file-total-badge\"], [1, \"date-group\"], [\"class\", \"date-header clickable\", 3, \"click\", 4, \"ngIf\"], [1, \"files-container\"], [\"class\", \"file-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"date-header\", \"clickable\", 3, \"click\"], [1, \"date-badge\"], [1, \"pi\", \"pi-calendar\"], [1, \"date-text\"], [1, \"file-count-indicator\"], [1, \"count\"], [1, \"label\"], [1, \"collapse-indicator\"], [1, \"pi\"], [1, \"file-item\"], [1, \"file-info\"], [1, \"file-index\"], [\"class\", \"file-source\", 4, \"ngIf\"], [\"class\", \"download-actions\", 4, \"ngIf\"], [1, \"file-source\"], [1, \"source-badge\", 3, \"title\"], [1, \"card\", \"flex\", \"justify-content-center\", \"w-full\", \"mb-3\"], [1, \"w-full\", \"upload-panel\", 3, \"toggleable\"], [1, \"uploaded-files-container\"], [1, \"uploaded-files\"], [\"class\", \"uploaded-file-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"upload-actions\"], [1, \"button1\", \"confirm-upload-btn\", 3, \"click\", \"disabled\"], [1, \"pi\", \"pi-check\"], [1, \"button2\", \"cancel-btn\", 3, \"click\", \"disabled\"], [1, \"pi\", \"pi-times\"], [1, \"upload-panel-header\"], [1, \"upload-panel-title\"], [1, \"pi\", \"pi-cloud-upload\"], [1, \"file-count-badge\"], [1, \"upload-panel-subtitle\"], [1, \"uploaded-file-item\"], [1, \"file-status-indicator\"], [\"title\", \"\\u7B49\\u5F85\\u4E0A\\u50B3\", 1, \"pi\", \"pi-clock\"], [1, \"file-icon-wrapper\"], [\"alt\", \"File\", 1, \"file-icon\", 3, \"src\"], [1, \"file-details\"], [1, \"file-name\", 3, \"title\"], [1, \"file-meta\"], [1, \"file-size\"], [1, \"file-type\"], [1, \"file-actions\"], [\"title\", \"\\u79FB\\u9664\\u6A94\\u6848\", 1, \"delete-btn\", 3, \"click\"], [1, \"error-message-container\"], [1, \"error-message\"], [1, \"pi\", \"pi-exclamation-triangle\"]],\n    template: function HistoryComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵelementStart(0, \"div\", 1);\n        i0.ɵɵelement(1, \"p-toast\");\n        i0.ɵɵelementStart(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6);\n        i0.ɵɵtext(7, \"\\u6D3D\\u8AC7\\u7D00\\u9304/\\u6A94\\u6848\\u4E0A\\u50B3\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(8, \"div\", 7)(9, \"button\", 8);\n        i0.ɵɵlistener(\"click\", function HistoryComponent_Template_button_click_9_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.toggleUploadArea());\n        });\n        i0.ɵɵelement(10, \"i\", 9);\n        i0.ɵɵtemplate(11, HistoryComponent_span_11_Template, 2, 0, \"span\", 10)(12, HistoryComponent_span_12_Template, 2, 0, \"span\", 10);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵtemplate(13, HistoryComponent_div_13_Template, 13, 0, \"div\", 11)(14, HistoryComponent_div_14_Template, 16, 2, \"div\", 12);\n        i0.ɵɵelementStart(15, \"input\", 13, 0);\n        i0.ɵɵlistener(\"change\", function HistoryComponent_Template_input_change_15_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onFileSelected($event));\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(17, HistoryComponent_div_17_Template, 13, 6, \"div\", 14)(18, HistoryComponent_div_18_Template, 4, 2, \"div\", 15)(19, HistoryComponent_div_19_Template, 14, 6, \"div\", 16)(20, HistoryComponent_div_20_Template, 4, 1, \"div\", 17);\n        i0.ɵɵelementStart(21, \"div\", 3)(22, \"div\", 18)(23, \"button\", 19)(24, \"a\", 20);\n        i0.ɵɵtext(25, \"\\u8FD4\\u56DE\\u4E3B\\u9078\\u55AE\");\n        i0.ɵɵelementEnd()()()()()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(9);\n        i0.ɵɵproperty(\"disabled\", ctx.isUploading);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", !ctx.isUploading);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.isUploading);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.showUploadArea && !ctx.uploadedFiles.length);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.showUploadArea && !ctx.uploadedFiles.length);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", ctx.houseHoldDetaiPicture.CFile && ctx.houseHoldDetaiPicture.CName);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.listSpecialChange && ctx.listSpecialChange.length > 0);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.showUploadArea && ctx.uploadedFiles.length > 0);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.uploadError);\n      }\n    },\n    dependencies: [RouterModule, i5.RouterLink, PanelModule, i6.Panel, i7.PrimeTemplate, PanelMenuModule, CommonModule, i8.NgForOf, i8.NgIf, DateFormatPipe, ToastModule, i9.Toast],\n    styles: [\"@charset \\\"UTF-8\\\";@keyframes _ngcontent-%COMP%_radioAppear{0%{transform:translate(-50%,-50%) scale(0);opacity:0}50%{transform:translate(-50%,-50%) scale(1.3);opacity:.8}to{transform:translate(-50%,-50%) scale(1);opacity:1}}@keyframes _ngcontent-%COMP%_radioRipple{0%{width:0;height:0;opacity:.6}to{width:44px;height:44px;opacity:0}}@keyframes _ngcontent-%COMP%_buttonPress{0%{transform:scale(1)}50%{transform:scale(.98)}to{transform:scale(1)}}@keyframes _ngcontent-%COMP%_fadeInUp{0%{opacity:0;transform:translateY(20px)}to{opacity:1;transform:translateY(0)}}@keyframes _ngcontent-%COMP%_slideInRight{0%{opacity:0;transform:translate(30px)}to{opacity:1;transform:translate(0)}}@keyframes _ngcontent-%COMP%_shimmer{0%{background-position:-200px 0}to{background-position:200px 0}}@keyframes _ngcontent-%COMP%_pulse{0%,to{transform:scale(1);opacity:1}50%{transform:scale(1.05);opacity:.8}}.animate-fade-in-up[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fadeInUp .6s ease-out}.animate-slide-in-right[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_slideInRight .4s ease-out}.animate-pulse[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_pulse 2s infinite}.animate-shimmer[_ngcontent-%COMP%]{background:linear-gradient(90deg,transparent,rgba(255,255,255,.4),transparent);background-size:200px 100%;animation:_ngcontent-%COMP%_shimmer 1.5s infinite}.hover-lift[_ngcontent-%COMP%]{transition:transform .3s ease,box-shadow .3s ease}.hover-lift[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 4px 12px #00000026}.hover-scale[_ngcontent-%COMP%]{transition:transform .3s ease}.hover-scale[_ngcontent-%COMP%]:hover{transform:scale(1.02)}.hover-glow[_ngcontent-%COMP%]{transition:box-shadow .3s ease}.hover-glow[_ngcontent-%COMP%]:hover{box-shadow:0 0 20px #b8a6764d}html[_ngcontent-%COMP%]{line-height:initial;-webkit-text-size-adjust:100%;width:100%;height:100%}body[_ngcontent-%COMP%]{margin:0;font-size:1rem;color:#23181599;font-family:Noto Sans TC!important;box-sizing:border-box;width:100%;height:auto;overflow:auto;overscroll-behavior:none;letter-spacing:.32px}html[_ngcontent-%COMP%], body[_ngcontent-%COMP%]{-webkit-text-size-adjust:none;text-size-adjust:none}main[_ngcontent-%COMP%]{display:block}h1[_ngcontent-%COMP%]{font-size:1.75rem;font-weight:400}hr[_ngcontent-%COMP%]{box-sizing:content-box;height:0;overflow:visible;margin:10px 0;border:none;border-top:1px solid #000}pre[_ngcontent-%COMP%]{font-family:monospace,monospace;font-size:1rem}a[_ngcontent-%COMP%]{background-color:transparent;text-decoration:none;-webkit-tap-highlight-color:transparent}abbr[title][_ngcontent-%COMP%]{border-bottom:none;text-decoration:underline;-webkit-text-decoration:underline dotted;text-decoration:underline dotted}b[_ngcontent-%COMP%]{font-family:Noto Sans TC;font-weight:500}strong[_ngcontent-%COMP%]{font-family:Noto Sans TC;font-weight:600}code[_ngcontent-%COMP%], kbd[_ngcontent-%COMP%], samp[_ngcontent-%COMP%]{font-family:monospace,monospace;font-size:1em}small[_ngcontent-%COMP%]{font-size:80%}sub[_ngcontent-%COMP%], sup[_ngcontent-%COMP%]{font-size:75%;line-height:0;position:relative;vertical-align:baseline}sub[_ngcontent-%COMP%]{bottom:-.25em}sup[_ngcontent-%COMP%]{top:-.5em}p[_ngcontent-%COMP%]{font-size:1rem}img[_ngcontent-%COMP%]{border-style:none;max-width:100%}button[_ngcontent-%COMP%], input[_ngcontent-%COMP%], optgroup[_ngcontent-%COMP%], select[_ngcontent-%COMP%], textarea[_ngcontent-%COMP%]{font-family:Noto Sans TC,\\\\5fae\\\\8edf\\\\6b63\\\\9ed1\\\\9ad4,Arial,sans-serif;font-size:1rem;line-height:1.15;margin:0;color:#2f2f2f;-webkit-tap-highlight-color:transparent;letter-spacing:2px}select[_ngcontent-%COMP%]:focus, input[_ngcontent-%COMP%]:focus, button[_ngcontent-%COMP%]:focus{outline:none}button[_ngcontent-%COMP%]{padding:0;background-color:transparent;border:none;cursor:pointer}button[_ngcontent-%COMP%], input[_ngcontent-%COMP%]{overflow:visible}button[_ngcontent-%COMP%], select[_ngcontent-%COMP%]{text-transform:none}button[_ngcontent-%COMP%], [type=button][_ngcontent-%COMP%], [type=reset][_ngcontent-%COMP%], [type=submit][_ngcontent-%COMP%]{-webkit-appearance:button}button[_ngcontent-%COMP%]::-moz-focus-inner, [type=button][_ngcontent-%COMP%]::-moz-focus-inner, [type=reset][_ngcontent-%COMP%]::-moz-focus-inner, [type=submit][_ngcontent-%COMP%]::-moz-focus-inner{border-style:none;padding:0}button[_ngcontent-%COMP%]:-moz-focusring, [type=button][_ngcontent-%COMP%]:-moz-focusring, [type=reset][_ngcontent-%COMP%]:-moz-focusring, [type=submit][_ngcontent-%COMP%]:-moz-focusring{outline:1px dotted ButtonText}fieldset[_ngcontent-%COMP%]{padding:.35em .75em .625em}div[role=tab][_ngcontent-%COMP%]{-webkit-tap-highlight-color:transparent}legend[_ngcontent-%COMP%]{box-sizing:border-box;color:inherit;display:table;max-width:100%;padding:0;white-space:normal}progress[_ngcontent-%COMP%]{vertical-align:baseline}textarea[_ngcontent-%COMP%]{overflow:auto}[type=checkbox][_ngcontent-%COMP%], [type=radio][_ngcontent-%COMP%]{box-sizing:border-box;padding:0}[type=number][_ngcontent-%COMP%]::-webkit-inner-spin-button, [type=number][_ngcontent-%COMP%]::-webkit-outer-spin-button{height:auto}[type=search][_ngcontent-%COMP%]{-webkit-appearance:textfield;outline-offset:-2px}[type=search][_ngcontent-%COMP%]::-webkit-search-decoration{-webkit-appearance:none}[_ngcontent-%COMP%]::-webkit-file-upload-button{-webkit-appearance:button;font:inherit}details[_ngcontent-%COMP%]{display:block}summary[_ngcontent-%COMP%]{display:list-item}template[_ngcontent-%COMP%]{display:none}[hidden][_ngcontent-%COMP%]{display:none}table[_ngcontent-%COMP%]{border-spacing:0px}ol[_ngcontent-%COMP%], ul[_ngcontent-%COMP%]{padding-left:0;margin-top:0}.form-check-input[_ngcontent-%COMP%]:focus{outline:none;box-shadow:none}.container[_ngcontent-%COMP%]{width:100%}@media (min-width: 640px){.container[_ngcontent-%COMP%]{max-width:640px}}@media (min-width: 768px){.container[_ngcontent-%COMP%]{max-width:768px}}@media (min-width: 1024px){.container[_ngcontent-%COMP%]{max-width:1024px}}@media (min-width: 1280px){.container[_ngcontent-%COMP%]{max-width:1280px}}@media (min-width: 1536px){.container[_ngcontent-%COMP%]{max-width:1536px}}.visible[_ngcontent-%COMP%]{visibility:visible}.collapse[_ngcontent-%COMP%]{visibility:collapse}.static[_ngcontent-%COMP%]{position:static}.fixed[_ngcontent-%COMP%]{position:fixed}.absolute[_ngcontent-%COMP%]{position:absolute}.relative[_ngcontent-%COMP%]{position:relative}.inset-0[_ngcontent-%COMP%]{inset:0}.inset-y-0[_ngcontent-%COMP%]{top:0;bottom:0}.bottom-0[_ngcontent-%COMP%]{bottom:0}.bottom-1[_ngcontent-%COMP%]{bottom:.25rem}.bottom-2[_ngcontent-%COMP%]{bottom:.5rem}.bottom-3[_ngcontent-%COMP%]{bottom:.75rem}.left-0[_ngcontent-%COMP%]{left:0}.left-1\\\\/2[_ngcontent-%COMP%]{left:50%}.left-2[_ngcontent-%COMP%]{left:.5rem}.left-3[_ngcontent-%COMP%]{left:.75rem}.right-0[_ngcontent-%COMP%]{right:0}.right-1[_ngcontent-%COMP%]{right:.25rem}.right-2[_ngcontent-%COMP%]{right:.5rem}.right-3[_ngcontent-%COMP%]{right:.75rem}.top-1[_ngcontent-%COMP%]{top:.25rem}.top-1\\\\/2[_ngcontent-%COMP%]{top:50%}.top-2[_ngcontent-%COMP%]{top:.5rem}.top-3[_ngcontent-%COMP%]{top:.75rem}.z-10[_ngcontent-%COMP%]{z-index:10}.z-50[_ngcontent-%COMP%]{z-index:50}.m-0[_ngcontent-%COMP%]{margin:0}.m-2[_ngcontent-%COMP%]{margin:.5rem}.m-auto[_ngcontent-%COMP%]{margin:auto}.mx-3[_ngcontent-%COMP%]{margin-left:.75rem;margin-right:.75rem}.mx-4[_ngcontent-%COMP%]{margin-left:1rem;margin-right:1rem}.mx-5[_ngcontent-%COMP%]{margin-left:1.25rem;margin-right:1.25rem}.mx-\\\\__ph-0__[_ngcontent-%COMP%]{margin-left:20%;margin-right:20%}.my-12[_ngcontent-%COMP%]{margin-top:3rem;margin-bottom:3rem}.my-2[_ngcontent-%COMP%]{margin-top:.5rem;margin-bottom:.5rem}.my-4[_ngcontent-%COMP%]{margin-top:1rem;margin-bottom:1rem}.my-6[_ngcontent-%COMP%]{margin-top:1.5rem;margin-bottom:1.5rem}.\\\\!mt-0[_ngcontent-%COMP%]{margin-top:0!important}.mb-2[_ngcontent-%COMP%]{margin-bottom:.5rem}.mb-3[_ngcontent-%COMP%]{margin-bottom:.75rem}.mb-4[_ngcontent-%COMP%]{margin-bottom:1rem}.mb-6[_ngcontent-%COMP%]{margin-bottom:1.5rem}.ml-1[_ngcontent-%COMP%]{margin-left:.25rem}.ml-2[_ngcontent-%COMP%]{margin-left:.5rem}.mr-1[_ngcontent-%COMP%]{margin-right:.25rem}.mr-2[_ngcontent-%COMP%]{margin-right:.5rem}.mr-3[_ngcontent-%COMP%]{margin-right:.75rem}.mr-4[_ngcontent-%COMP%]{margin-right:1rem}.mr-5[_ngcontent-%COMP%]{margin-right:1.25rem}.mr-8[_ngcontent-%COMP%]{margin-right:2rem}.mt-1[_ngcontent-%COMP%]{margin-top:.25rem}.mt-10[_ngcontent-%COMP%]{margin-top:2.5rem}.mt-14[_ngcontent-%COMP%]{margin-top:3.5rem}.mt-2[_ngcontent-%COMP%]{margin-top:.5rem}.mt-3[_ngcontent-%COMP%]{margin-top:.75rem}.mt-4[_ngcontent-%COMP%]{margin-top:1rem}.mt-5[_ngcontent-%COMP%]{margin-top:1.25rem}.mt-6[_ngcontent-%COMP%]{margin-top:1.5rem}.mt-7[_ngcontent-%COMP%]{margin-top:1.75rem}.mt-8[_ngcontent-%COMP%]{margin-top:2rem}.mt-\\\\__ph-0__[_ngcontent-%COMP%]{margin-top:16px}.block[_ngcontent-%COMP%]{display:block}.flex[_ngcontent-%COMP%]{display:flex}.table[_ngcontent-%COMP%]{display:table}.table-row[_ngcontent-%COMP%]{display:table-row}.grid[_ngcontent-%COMP%]{display:grid}.hidden[_ngcontent-%COMP%]{display:none}.\\\\!h-\\\\__ph-0__[_ngcontent-%COMP%]{height:107px!important}.\\\\!h-full[_ngcontent-%COMP%]{height:100%!important}.h-10[_ngcontent-%COMP%]{height:2.5rem}.h-16[_ngcontent-%COMP%]{height:4rem}.h-28[_ngcontent-%COMP%]{height:7rem}.h-32[_ngcontent-%COMP%]{height:8rem}.h-6[_ngcontent-%COMP%]{height:1.5rem}.h-8[_ngcontent-%COMP%]{height:2rem}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:100px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:145px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:280px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:40vh}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:47px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:480px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:4px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:50px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:55px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:665px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:80px}.h-auto[_ngcontent-%COMP%]{height:auto}.h-fit[_ngcontent-%COMP%]{height:-moz-fit-content;height:fit-content}.max-h-\\\\__ph-0__[_ngcontent-%COMP%]{max-height:200px}.max-h-\\\\__ph-0__[_ngcontent-%COMP%]{max-height:500px}.max-h-full[_ngcontent-%COMP%]{max-height:100%}.max-h-screen[_ngcontent-%COMP%]{max-height:100vh}.\\\\!min-h-\\\\__ph-0__[_ngcontent-%COMP%]{min-height:60vh!important}.\\\\!w-40[_ngcontent-%COMP%]{width:10rem!important}.\\\\!w-48[_ngcontent-%COMP%]{width:12rem!important}.\\\\!w-\\\\__ph-0__[_ngcontent-%COMP%]{width:107px!important}.\\\\!w-\\\\__ph-0__[_ngcontent-%COMP%]{width:120px!important}.\\\\!w-full[_ngcontent-%COMP%]{width:100%!important}.w-1\\\\/3[_ngcontent-%COMP%]{width:33.333333%}.w-10[_ngcontent-%COMP%]{width:2.5rem}.w-16[_ngcontent-%COMP%]{width:4rem}.w-2\\\\/3[_ngcontent-%COMP%]{width:66.666667%}.w-28[_ngcontent-%COMP%]{width:7rem}.w-6[_ngcontent-%COMP%]{width:1.5rem}.w-7\\\\/12[_ngcontent-%COMP%]{width:58.333333%}.w-8[_ngcontent-%COMP%]{width:2rem}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:100%}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:150px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:160px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:180px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:30rem}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:400px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:45%}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:485px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:50%}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:550px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:660px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:760px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:80%}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:80px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:968px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:97%}.w-full[_ngcontent-%COMP%]{width:100%}.\\\\!max-w-full[_ngcontent-%COMP%]{max-width:100%!important}.max-w-4xl[_ngcontent-%COMP%]{max-width:56rem}.max-w-\\\\__ph-0__[_ngcontent-%COMP%]{max-width:1216px}.max-w-full[_ngcontent-%COMP%]{max-width:100%}.flex-1[_ngcontent-%COMP%]{flex:1 1 0%}.flex-shrink[_ngcontent-%COMP%]{flex-shrink:1}.flex-shrink-0[_ngcontent-%COMP%]{flex-shrink:0}.grow[_ngcontent-%COMP%]{flex-grow:1}.border-collapse[_ngcontent-%COMP%]{border-collapse:collapse}.-translate-x-1\\\\/2[_ngcontent-%COMP%]{--tw-translate-x: -50%;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.-translate-y-1\\\\/2[_ngcontent-%COMP%]{--tw-translate-y: -50%;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.transform[_ngcontent-%COMP%]{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.grid-cols-1[_ngcontent-%COMP%]{grid-template-columns:repeat(1,minmax(0,1fr))}.grid-cols-10[_ngcontent-%COMP%]{grid-template-columns:repeat(10,minmax(0,1fr))}.grid-cols-2[_ngcontent-%COMP%]{grid-template-columns:repeat(2,minmax(0,1fr))}.grid-cols-3[_ngcontent-%COMP%]{grid-template-columns:repeat(3,minmax(0,1fr))}.grid-cols-4[_ngcontent-%COMP%]{grid-template-columns:repeat(4,minmax(0,1fr))}.flex-row[_ngcontent-%COMP%]{flex-direction:row}.flex-col[_ngcontent-%COMP%]{flex-direction:column}.flex-wrap[_ngcontent-%COMP%]{flex-wrap:wrap}.items-start[_ngcontent-%COMP%]{align-items:flex-start}.items-center[_ngcontent-%COMP%]{align-items:center}.justify-start[_ngcontent-%COMP%]{justify-content:flex-start}.justify-end[_ngcontent-%COMP%]{justify-content:flex-end}.justify-center[_ngcontent-%COMP%]{justify-content:center}.justify-between[_ngcontent-%COMP%]{justify-content:space-between}.gap-2[_ngcontent-%COMP%]{gap:.5rem}.gap-3[_ngcontent-%COMP%]{gap:.75rem}.gap-4[_ngcontent-%COMP%]{gap:1rem}.gap-x-1[_ngcontent-%COMP%]{column-gap:.25rem}.self-center[_ngcontent-%COMP%]{align-self:center}.overflow-hidden[_ngcontent-%COMP%]{overflow:hidden}.overflow-y-scroll[_ngcontent-%COMP%]{overflow-y:scroll}.break-words[_ngcontent-%COMP%]{overflow-wrap:break-word}.rounded[_ngcontent-%COMP%]{border-radius:.25rem}.rounded-3xl[_ngcontent-%COMP%]{border-radius:1.5rem}.rounded-full[_ngcontent-%COMP%]{border-radius:9999px}.rounded-lg[_ngcontent-%COMP%]{border-radius:.5rem}.rounded-md[_ngcontent-%COMP%]{border-radius:.375rem}.rounded-b[_ngcontent-%COMP%]{border-bottom-right-radius:.25rem;border-bottom-left-radius:.25rem}.border[_ngcontent-%COMP%]{border-width:1px}.border-2[_ngcontent-%COMP%]{border-width:2px}.border-solid[_ngcontent-%COMP%]{border-style:solid}.border-blue-400[_ngcontent-%COMP%]{--tw-border-opacity: 1;border-color:rgb(96 165 250 / var(--tw-border-opacity))}.border-blue-500[_ngcontent-%COMP%]{--tw-border-opacity: 1;border-color:rgb(59 130 246 / var(--tw-border-opacity))}.border-gray-300[_ngcontent-%COMP%]{--tw-border-opacity: 1;border-color:rgb(209 213 219 / var(--tw-border-opacity))}.bg-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-bg-opacity: 1;background-color:rgb(0 143 199 / var(--tw-bg-opacity))}.bg-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-bg-opacity: 1;background-color:rgb(184 166 118 / var(--tw-bg-opacity))}.bg-\\\\__ph-0__[_ngcontent-%COMP%]{background-color:#f3f1ea80}.bg-\\\\__ph-0__[_ngcontent-%COMP%]{background-color:#f3f1ea99}.bg-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-bg-opacity: 1;background-color:rgb(217 217 217 / var(--tw-bg-opacity))}.bg-black[_ngcontent-%COMP%]{--tw-bg-opacity: 1;background-color:rgb(0 0 0 / var(--tw-bg-opacity))}.bg-gray-500[_ngcontent-%COMP%]{--tw-bg-opacity: 1;background-color:rgb(107 114 128 / var(--tw-bg-opacity))}.bg-white[_ngcontent-%COMP%]{--tw-bg-opacity: 1;background-color:rgb(255 255 255 / var(--tw-bg-opacity))}.bg-opacity-50[_ngcontent-%COMP%]{--tw-bg-opacity: .5}.bg-opacity-70[_ngcontent-%COMP%]{--tw-bg-opacity: .7}.bg-opacity-75[_ngcontent-%COMP%]{--tw-bg-opacity: .75}.bg-opacity-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-bg-opacity: .04}.object-contain[_ngcontent-%COMP%]{object-fit:contain}.object-cover[_ngcontent-%COMP%]{object-fit:cover}.p-2[_ngcontent-%COMP%]{padding:.5rem}.p-4[_ngcontent-%COMP%]{padding:1rem}.p-\\\\__ph-0__[_ngcontent-%COMP%]{padding:16px}.px-1[_ngcontent-%COMP%]{padding-left:.25rem;padding-right:.25rem}.px-2[_ngcontent-%COMP%]{padding-left:.5rem;padding-right:.5rem}.px-3[_ngcontent-%COMP%]{padding-left:.75rem;padding-right:.75rem}.px-4[_ngcontent-%COMP%]{padding-left:1rem;padding-right:1rem}.px-6[_ngcontent-%COMP%]{padding-left:1.5rem;padding-right:1.5rem}.py-0[_ngcontent-%COMP%]{padding-top:0;padding-bottom:0}.py-0\\\\.5[_ngcontent-%COMP%]{padding-top:.125rem;padding-bottom:.125rem}.py-1[_ngcontent-%COMP%]{padding-top:.25rem;padding-bottom:.25rem}.py-2[_ngcontent-%COMP%]{padding-top:.5rem;padding-bottom:.5rem}.py-3[_ngcontent-%COMP%]{padding-top:.75rem;padding-bottom:.75rem}.py-4[_ngcontent-%COMP%]{padding-top:1rem;padding-bottom:1rem}.pb-4[_ngcontent-%COMP%]{padding-bottom:1rem}.pb-5[_ngcontent-%COMP%]{padding-bottom:1.25rem}.pb-8[_ngcontent-%COMP%]{padding-bottom:2rem}.pl-4[_ngcontent-%COMP%]{padding-left:1rem}.pl-6[_ngcontent-%COMP%]{padding-left:1.5rem}.pr-1[_ngcontent-%COMP%]{padding-right:.25rem}.pr-3[_ngcontent-%COMP%]{padding-right:.75rem}.pr-4[_ngcontent-%COMP%]{padding-right:1rem}.text-left[_ngcontent-%COMP%]{text-align:left}.\\\\!text-center[_ngcontent-%COMP%]{text-align:center!important}.text-center[_ngcontent-%COMP%]{text-align:center}.text-right[_ngcontent-%COMP%]{text-align:right}.text-2xl[_ngcontent-%COMP%]{font-size:1.5rem;line-height:2rem}.text-3xl[_ngcontent-%COMP%]{font-size:1.875rem;line-height:2.25rem}.text-base[_ngcontent-%COMP%]{font-size:1rem;line-height:1.5rem}.text-lg[_ngcontent-%COMP%]{font-size:1.125rem;line-height:1.75rem}.text-sm[_ngcontent-%COMP%]{font-size:.875rem;line-height:1.25rem}.text-xl[_ngcontent-%COMP%]{font-size:1.25rem;line-height:1.75rem}.text-xs[_ngcontent-%COMP%]{font-size:.75rem;line-height:1rem}.font-bold[_ngcontent-%COMP%]{font-weight:700}.font-light[_ngcontent-%COMP%]{font-weight:300}.font-medium[_ngcontent-%COMP%]{font-weight:500}.font-normal[_ngcontent-%COMP%]{font-weight:400}.font-semibold[_ngcontent-%COMP%]{font-weight:600}.leading-relaxed[_ngcontent-%COMP%]{line-height:1.625}.tracking-wider[_ngcontent-%COMP%]{letter-spacing:.05em}.text-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(0 143 199 / var(--tw-text-opacity))}.text-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(35 24 21 / var(--tw-text-opacity))}.text-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(51 51 51 / var(--tw-text-opacity))}.text-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(182 152 103 / var(--tw-text-opacity))}.text-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(184 166 118 / var(--tw-text-opacity))}.text-black[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(0 0 0 / var(--tw-text-opacity))}.text-blue-400[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(96 165 250 / var(--tw-text-opacity))}.text-gray-500[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(107 114 128 / var(--tw-text-opacity))}.text-gray-600[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(75 85 99 / var(--tw-text-opacity))}.text-stone-600[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(87 83 78 / var(--tw-text-opacity))}.text-stone-900[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(28 25 23 / var(--tw-text-opacity))}.text-white[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(255 255 255 / var(--tw-text-opacity))}.opacity-50[_ngcontent-%COMP%]{opacity:.5}.shadow[_ngcontent-%COMP%]{--tw-shadow: 0 1px 3px 0 rgb(0 0 0 / .1), 0 1px 2px -1px rgb(0 0 0 / .1);--tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.grayscale[_ngcontent-%COMP%]{--tw-grayscale: grayscale(100%);filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)}.filter[_ngcontent-%COMP%]{filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)}.transition[_ngcontent-%COMP%]{transition-property:color,background-color,border-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,-webkit-backdrop-filter;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,backdrop-filter;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,backdrop-filter,-webkit-backdrop-filter;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.15s}.transition-all[_ngcontent-%COMP%]{transition-property:all;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.15s}.transition-shadow[_ngcontent-%COMP%]{transition-property:box-shadow;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.15s}.duration-200[_ngcontent-%COMP%]{transition-duration:.2s}.ease-in-out[_ngcontent-%COMP%]{transition-timing-function:cubic-bezier(.4,0,.2,1)}.content[_ngcontent-%COMP%]{width:100%}@media screen and (max-width: 1024px){.content[_ngcontent-%COMP%]{width:100%;padding:0 16px 50px}}.pc[_ngcontent-%COMP%], .inpc[_ngcontent-%COMP%]{display:initial}@media screen and (max-width: 1024px){.inpc[_ngcontent-%COMP%]{display:none}}.flat[_ngcontent-%COMP%]{display:none}@media screen and (max-width: 1024px){.flat[_ngcontent-%COMP%]{display:initial}}.mb[_ngcontent-%COMP%]{display:none}@media screen and (max-width: 912px){.mb[_ngcontent-%COMP%]{display:initial}}.cursor-pointer[_ngcontent-%COMP%]{cursor:pointer}.wrapper[_ngcontent-%COMP%]{display:flex;justify-content:center}.wrapper[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]{position:relative}.wrapper[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]:after{content:\\\"\\\";position:absolute;background-image:url(/assets/background/background2.png);width:180px;height:150px;background-size:unset;background-position:left;z-index:-1;right:0;bottom:20px}.button1[_ngcontent-%COMP%]{width:200px;height:47px;border:1px solid #CDCDCD;color:#3a4246;border-radius:28px;background-origin:border-box;background-clip:content-box,border-box;transition:all .3s ease}.button1[_ngcontent-%COMP%]:hover{border-color:#b8a676;color:#ae9b66;background-color:#b8a6760d}.button1[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#b8a676;transition:color .3s ease}.button1[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{color:#ae9b66}@media screen and (max-width: 912px){.button1[_ngcontent-%COMP%]{width:100%}}.button2[_ngcontent-%COMP%]{width:200px;padding:12px 24px;height:47px;color:#fff;background:linear-gradient(90deg,#ae9b66,#b8a676);border-radius:24px;box-shadow:0 2px 8px #ae9b6633;transition:all .3s ease}.button2[_ngcontent-%COMP%]:hover{background:linear-gradient(90deg,#9b8a5a,#ae9b66);transform:translateY(-1px);box-shadow:0 4px 12px #ae9b664d}.button2[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#fff}@media screen and (max-width: 912px){.button2[_ngcontent-%COMP%]{width:100%}}.button3[_ngcontent-%COMP%]{width:180px;padding:12px 24px;height:47px;color:#fff;background:linear-gradient(90deg,#ae9b66,#b8a676);border-radius:24px;box-shadow:0 2px 8px #ae9b6633;transition:all .3s ease}.button3[_ngcontent-%COMP%]:hover{background:linear-gradient(90deg,#9b8a5a,#ae9b66);transform:translateY(-1px);box-shadow:0 4px 12px #ae9b664d}.button3[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#fff}@media screen and (max-width: 912px){.button3[_ngcontent-%COMP%]{width:100%}}.button4[_ngcontent-%COMP%]{width:180px;padding:12px 24px;height:47px;color:#fff;background:#979797;border-radius:24px;transition:all .3s ease}.button4[_ngcontent-%COMP%]:hover{background:#7e7e7e}.button4[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#fff}@media screen and (max-width: 912px){.button4[_ngcontent-%COMP%]{width:100%}}.checkbox-zone[_ngcontent-%COMP%]{padding:2px;border-radius:2px;position:relative;background:linear-gradient(90deg,#ae9b66,#b8a676);box-shadow:0 1px 4px #ae9b664d}.avoid-resize-img[_ngcontent-%COMP%]{max-width:none}.fit-size[_ngcontent-%COMP%]{height:inherit;width:inherit;object-fit:cover;object-position:center}.p-calendar[_ngcontent-%COMP%]{width:100%}.p-calendar[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{border-bottom:1px solid rgba(144,150,157,.4);border-radius:0}.p-calendar[_ngcontent-%COMP%]   .p-inputtext[_ngcontent-%COMP%]{border:none;border-radius:0;border-bottom:1px solid rgba(144,150,157,.4);padding:12.5px 0;color:#23181599}@media screen and (max-width: 912px){.p-calendar[_ngcontent-%COMP%]   .p-inputtext[_ngcontent-%COMP%]{background:transparent}}.p-dropdown[_ngcontent-%COMP%]{border:none;width:100%}.p-dropdown[_ngcontent-%COMP%]   .p-inputtext[_ngcontent-%COMP%]{border:none;border-radius:0;border-bottom:1px solid rgba(144,150,157,.4);padding:12.5px 0;color:#231815}@media screen and (max-width: 912px){.p-dropdown[_ngcontent-%COMP%]   .p-inputtext[_ngcontent-%COMP%]{background:transparent}}.p-dropdown[_ngcontent-%COMP%]   .p-dropdown-trigger[_ngcontent-%COMP%]{border-radius:0;border-bottom:1px solid rgba(144,150,157,.4)}.p-dropdown[_ngcontent-%COMP%]:not(.p-disabled).p-focus{box-shadow:none}.p-radiobutton[_ngcontent-%COMP%]{position:relative;display:inline-flex;align-items:center}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box[_ngcontent-%COMP%]{width:22px;height:22px;border:2px solid #CDCDCD;background-color:#fff;border-radius:50%;transition:all .3s cubic-bezier(.25,.46,.45,.94);position:relative;box-shadow:0 1px 3px #ae9b661a;cursor:pointer}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box[_ngcontent-%COMP%]:hover{border-color:#b8a676;background:radial-gradient(circle at center,#b8a67614,#b8a67605);transform:scale(1.05);box-shadow:0 2px 8px #ae9b6626}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box[_ngcontent-%COMP%]:focus{outline:none;box-shadow:0 0 0 3px #b8a67640,0 2px 8px #ae9b6633;border-color:#a39460}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box[_ngcontent-%COMP%]:active{transform:scale(.98)}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box.p-highlight[_ngcontent-%COMP%]{background:linear-gradient(90deg,#ae9b66,#b8a676);border-color:#ae9b66;box-shadow:0 2px 12px #ae9b6640,inset 0 1px 2px #fff3}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box.p-highlight[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:50%;left:50%;width:8px;height:8px;background:radial-gradient(circle,#fff,#ffffffe6);border-radius:50%;transform:translate(-50%,-50%) scale(0);box-shadow:0 1px 2px #0003;animation:_ngcontent-%COMP%_radioAppear .2s ease forwards}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box.p-highlight[_ngcontent-%COMP%]:hover{background:linear-gradient(90deg,#9b8a5a,#ae9b66);border-color:#9b8a5a;transform:scale(1.05);box-shadow:0 3px 15px #ae9b6659,inset 0 1px 2px #ffffff40}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box.p-highlight[_ngcontent-%COMP%]:focus{box-shadow:0 0 0 3px #b8a6764d,0 3px 15px #ae9b6659}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-icon[_ngcontent-%COMP%]{display:none}@keyframes _ngcontent-%COMP%_radioAppear{0%{transform:translate(-50%,-50%) scale(0);opacity:0}50%{transform:translate(-50%,-50%) scale(1.2);opacity:.8}to{transform:translate(-50%,-50%) scale(1);opacity:1}}@keyframes _ngcontent-%COMP%_radioRipple{0%{transform:translate(-50%,-50%) scale(0);opacity:.6}to{transform:translate(-50%,-50%) scale(4);opacity:0}}.p-panel[_ngcontent-%COMP%]   .p-panel-header[_ngcontent-%COMP%]{background-color:#fff;color:#846a52;font-size:16px;border:1px solid #E6F0F3;border-radius:6px;padding:16px 32px}.p-panel-expanded[_ngcontent-%COMP%]   .p-panel-header[_ngcontent-%COMP%]{padding:16px 32px 8px;border-bottom:none!important;border-bottom-right-radius:0;border-bottom-left-radius:0}@media screen and (max-width: 912px){.p-panel-expanded[_ngcontent-%COMP%]   .p-panel-header[_ngcontent-%COMP%]{padding:16px 16px 8px}}.p-panel[_ngcontent-%COMP%]   .p-panel-header[_ngcontent-%COMP%]   .p-panel-header-icon[_ngcontent-%COMP%]{transform:rotate(-180deg)!important;transition:transform .3s linear}.p-panel-expanded[_ngcontent-%COMP%]   .p-panel-header[_ngcontent-%COMP%]   .p-panel-header-icon[_ngcontent-%COMP%]{transform:rotate(0)!important;transition:transform .3s linear}.p-panel[_ngcontent-%COMP%]   .p-panel-header[_ngcontent-%COMP%]   .p-panel-header-icon[_ngcontent-%COMP%]:focus{box-shadow:none}.p-panel[_ngcontent-%COMP%]   .p-panel-content[_ngcontent-%COMP%]{padding:0 32px 16px}@media screen and (max-width: 912px){.p-panel[_ngcontent-%COMP%]   .p-panel-content[_ngcontent-%COMP%]{padding:0 16px 16px}}.p-panel[_ngcontent-%COMP%]   .p-panel-header[_ngcontent-%COMP%]   .p-panel-header-icon[_ngcontent-%COMP%]{width:24px;height:24px}.p-dialog-mask[_ngcontent-%COMP%]{width:100%;height:100%;background-color:#0006;top:0;z-index:20;pointer-events:all!important}.p-dialog[_ngcontent-%COMP%]   .p-dialog-header[_ngcontent-%COMP%]{padding:32px 24px 16px;color:#231815;font-size:24px;text-align:center;display:flex;justify-content:center}.p-dialog[_ngcontent-%COMP%]   .p-dialog-content[_ngcontent-%COMP%]{padding:32px 24px 0;background:linear-gradient(180deg,#f3f1ea,#fff)}.p-dialog[_ngcontent-%COMP%]   .p-dialog-header[_ngcontent-%COMP%]   .p-dialog-header-icon[_ngcontent-%COMP%]{display:none}.p-dialog[_ngcontent-%COMP%]   .p-dialog-footer[_ngcontent-%COMP%]{padding:24px 24px 40px;display:flex;justify-content:center}.p-checkbox[_ngcontent-%COMP%]{width:18px;height:18px;display:inline-flex;align-items:center}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box[_ngcontent-%COMP%]{width:18px;height:18px;border:2px solid #CDCDCD;border-radius:3px;transition:.3s ease;background:#fff;position:relative;display:flex;align-items:center;justify-content:center}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box[_ngcontent-%COMP%]:hover{border-color:#b8a676}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box[_ngcontent-%COMP%]:focus{outline:none;border-color:#b8a676;box-shadow:0 0 0 2px #b8a67633}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box.p-highlight[_ngcontent-%COMP%]{background:#b8a676!important;border-color:#b8a676!important}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box[_ngcontent-%COMP%]   .p-checkbox-icon[_ngcontent-%COMP%]{color:#fff!important;font-size:12px;width:12px;height:12px;display:flex;align-items:center;justify-content:center}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box.p-highlight[_ngcontent-%COMP%]   .p-checkbox-icon[_ngcontent-%COMP%]{transform:scale(1);opacity:1}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box[_ngcontent-%COMP%]:not(.p-highlight)   .p-checkbox-icon[_ngcontent-%COMP%]{transform:scale(0);opacity:0}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box.p-highlight[_ngcontent-%COMP%]   .p-checkbox-icon[_ngcontent-%COMP%]:before{content:\\\"\\\\e915\\\";font-family:primeicons;font-weight:400;font-style:normal;font-size:12px;display:inline-block}.p-checkbox-label[_ngcontent-%COMP%]{margin-left:8px;cursor:pointer;-webkit-user-select:none;user-select:none}.input[_ngcontent-%COMP%]{width:100%;border:none;padding:12.5px 0;border-bottom:1px solid rgba(144,150,157,.4);border-radius:0}[_ngcontent-%COMP%]::-webkit-scrollbar{width:4px;height:4px}[_ngcontent-%COMP%]::-webkit-scrollbar-button{background:transparent;border-radius:2px}[_ngcontent-%COMP%]::-webkit-scrollbar-track-piece{background:transparent}[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{border-radius:4px;background-color:#90969d33}[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:transparent}.p-radiobutton-label[_ngcontent-%COMP%]{color:#231815!important;font-weight:400;cursor:pointer;transition:all .3s ease}.p-radiobutton-label[_ngcontent-%COMP%]:hover{color:#ae9b66!important}label[for*=requirement_][_ngcontent-%COMP%]{color:#231815!important;font-weight:400}label[for*=requirement_][_ngcontent-%COMP%]:hover{color:#ae9b66!important}input[type=radio][_ngcontent-%COMP%]:checked + label[_ngcontent-%COMP%], .p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box.p-highlight[_ngcontent-%COMP%] + label[_ngcontent-%COMP%]{color:#ae9b66!important;font-weight:600}.history[_ngcontent-%COMP%]{margin-top:20px;width:1216px;z-index:3;min-height:550px;height:550px}.history[_ngcontent-%COMP%]   .history-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:flex-start;margin-bottom:8px}.history[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{color:#222;font-weight:700;font-size:2.2rem;letter-spacing:.1em}.history[_ngcontent-%COMP%]   .upload-hint-text-header[_ngcontent-%COMP%]{font-size:12px;color:#9ca3af;text-align:right;margin-bottom:16px;line-height:1.4}.history[_ngcontent-%COMP%]   .files-panel[_ngcontent-%COMP%]   .panel-header-content[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;width:100%}.history[_ngcontent-%COMP%]   .files-panel[_ngcontent-%COMP%]   .panel-header-content[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px}.history[_ngcontent-%COMP%]   .files-panel[_ngcontent-%COMP%]   .panel-header-content[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#bfa76a;font-size:18px}.history[_ngcontent-%COMP%]   .files-panel[_ngcontent-%COMP%]   .panel-header-content[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .panel-title[_ngcontent-%COMP%]{font-size:16px;font-weight:600;color:#1f2937}.history[_ngcontent-%COMP%]   .files-panel[_ngcontent-%COMP%]   .panel-header-content[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .file-total-badge[_ngcontent-%COMP%]{background:linear-gradient(135deg,#bfa76a,#a68c4a);color:#fff;padding:4px 12px;border-radius:12px;font-size:12px;font-weight:600}.history[_ngcontent-%COMP%]   .files-panel[_ngcontent-%COMP%]   .panel-header-content[_ngcontent-%COMP%]   .header-right[_ngcontent-%COMP%]   .control-buttons[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px}.history[_ngcontent-%COMP%]   .files-panel[_ngcontent-%COMP%]   .panel-header-content[_ngcontent-%COMP%]   .header-right[_ngcontent-%COMP%]   .control-buttons[_ngcontent-%COMP%]   .expand-btn[_ngcontent-%COMP%], .history[_ngcontent-%COMP%]   .files-panel[_ngcontent-%COMP%]   .panel-header-content[_ngcontent-%COMP%]   .header-right[_ngcontent-%COMP%]   .control-buttons[_ngcontent-%COMP%]   .collapse-btn[_ngcontent-%COMP%]{background:#fff;border:1px solid #bfa76a;color:#bfa76a;font-size:13px;font-weight:600;cursor:pointer;padding:6px 14px;border-radius:6px;transition:all .2s ease;letter-spacing:.02em}.history[_ngcontent-%COMP%]   .files-panel[_ngcontent-%COMP%]   .panel-header-content[_ngcontent-%COMP%]   .header-right[_ngcontent-%COMP%]   .control-buttons[_ngcontent-%COMP%]   .expand-btn[_ngcontent-%COMP%]:hover, .history[_ngcontent-%COMP%]   .files-panel[_ngcontent-%COMP%]   .panel-header-content[_ngcontent-%COMP%]   .header-right[_ngcontent-%COMP%]   .control-buttons[_ngcontent-%COMP%]   .collapse-btn[_ngcontent-%COMP%]:hover{background:#bfa76a;color:#fff;transform:translateY(-1px);box-shadow:0 2px 8px #bfa76a40}.history[_ngcontent-%COMP%]   .files-panel[_ngcontent-%COMP%]   .panel-header-content[_ngcontent-%COMP%]   .header-right[_ngcontent-%COMP%]   .control-buttons[_ngcontent-%COMP%]   .expand-btn[_ngcontent-%COMP%]:active, .history[_ngcontent-%COMP%]   .files-panel[_ngcontent-%COMP%]   .panel-header-content[_ngcontent-%COMP%]   .header-right[_ngcontent-%COMP%]   .control-buttons[_ngcontent-%COMP%]   .collapse-btn[_ngcontent-%COMP%]:active{transform:translateY(0)}.history[_ngcontent-%COMP%]   .files-panel[_ngcontent-%COMP%]   .panel-header-content[_ngcontent-%COMP%]   .header-right[_ngcontent-%COMP%]   .control-buttons[_ngcontent-%COMP%]   .expand-btn[_ngcontent-%COMP%]:hover{background:#10b981;border-color:#10b981}.history[_ngcontent-%COMP%]   .files-panel[_ngcontent-%COMP%]   .panel-header-content[_ngcontent-%COMP%]   .header-right[_ngcontent-%COMP%]   .control-buttons[_ngcontent-%COMP%]   .collapse-btn[_ngcontent-%COMP%]:hover{background:#ef4444;border-color:#ef4444}.history[_ngcontent-%COMP%]   .date-group[_ngcontent-%COMP%]{margin-bottom:24px;border-radius:12px;background:linear-gradient(135deg,#fafafa,#f5f5f5);border:1px solid #e5e5e5;overflow:hidden;box-shadow:0 2px 8px #0000000a;transition:all .3s ease}.history[_ngcontent-%COMP%]   .date-group[_ngcontent-%COMP%]:last-child{margin-bottom:0}.history[_ngcontent-%COMP%]   .date-group.collapsed[_ngcontent-%COMP%]{margin-bottom:12px;box-shadow:0 1px 4px #00000005}.history[_ngcontent-%COMP%]   .date-group.collapsed[_ngcontent-%COMP%]   .date-header[_ngcontent-%COMP%]{border-radius:12px}.history[_ngcontent-%COMP%]   .date-group.collapsed[_ngcontent-%COMP%]   .date-header[_ngcontent-%COMP%]:after{display:none}.history[_ngcontent-%COMP%]   .date-group[_ngcontent-%COMP%]   .date-header[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;padding:16px 20px;background:linear-gradient(135deg,#bfa76a,#a68c4a);color:#fff;position:relative;transition:all .3s ease}.history[_ngcontent-%COMP%]   .date-group[_ngcontent-%COMP%]   .date-header.clickable[_ngcontent-%COMP%]{cursor:pointer;-webkit-user-select:none;user-select:none}.history[_ngcontent-%COMP%]   .date-group[_ngcontent-%COMP%]   .date-header.clickable[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,#a68c4a,#8f7a3e);transform:translateY(-1px);box-shadow:0 4px 12px #bfa76a4d}.history[_ngcontent-%COMP%]   .date-group[_ngcontent-%COMP%]   .date-header.clickable[_ngcontent-%COMP%]:active{transform:translateY(0)}.history[_ngcontent-%COMP%]   .date-group[_ngcontent-%COMP%]   .date-header[_ngcontent-%COMP%]:after{content:\\\"\\\";position:absolute;bottom:0;left:0;right:0;height:1px;background:linear-gradient(90deg,transparent,rgba(255,255,255,.3),transparent)}.history[_ngcontent-%COMP%]   .date-group[_ngcontent-%COMP%]   .date-header[_ngcontent-%COMP%]   .date-badge[_ngcontent-%COMP%]{display:flex;align-items:center;gap:10px;font-weight:600;font-size:15px}.history[_ngcontent-%COMP%]   .date-group[_ngcontent-%COMP%]   .date-header[_ngcontent-%COMP%]   .date-badge[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:16px;opacity:.9}.history[_ngcontent-%COMP%]   .date-group[_ngcontent-%COMP%]   .date-header[_ngcontent-%COMP%]   .date-badge[_ngcontent-%COMP%]   .date-text[_ngcontent-%COMP%]{letter-spacing:.02em}.history[_ngcontent-%COMP%]   .date-group[_ngcontent-%COMP%]   .date-header[_ngcontent-%COMP%]   .header-right[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px}.history[_ngcontent-%COMP%]   .date-group[_ngcontent-%COMP%]   .date-header[_ngcontent-%COMP%]   .header-right[_ngcontent-%COMP%]   .file-count-indicator[_ngcontent-%COMP%]{display:flex;align-items:center;gap:4px;background:#fff3;padding:6px 12px;border-radius:20px;font-size:13px;font-weight:600}.history[_ngcontent-%COMP%]   .date-group[_ngcontent-%COMP%]   .date-header[_ngcontent-%COMP%]   .header-right[_ngcontent-%COMP%]   .file-count-indicator[_ngcontent-%COMP%]   .count[_ngcontent-%COMP%]{font-size:14px;font-weight:700}.history[_ngcontent-%COMP%]   .date-group[_ngcontent-%COMP%]   .date-header[_ngcontent-%COMP%]   .header-right[_ngcontent-%COMP%]   .file-count-indicator[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%]{opacity:.9}.history[_ngcontent-%COMP%]   .date-group[_ngcontent-%COMP%]   .date-header[_ngcontent-%COMP%]   .header-right[_ngcontent-%COMP%]   .collapse-indicator[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;width:24px;height:24px;background:#fff3;border-radius:50%;transition:all .3s ease}.history[_ngcontent-%COMP%]   .date-group[_ngcontent-%COMP%]   .date-header[_ngcontent-%COMP%]   .header-right[_ngcontent-%COMP%]   .collapse-indicator[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:12px;transition:transform .3s ease}.history[_ngcontent-%COMP%]   .date-group[_ngcontent-%COMP%]   .date-header[_ngcontent-%COMP%]   .header-right[_ngcontent-%COMP%]   .collapse-indicator[_ngcontent-%COMP%]:hover{background:#ffffff4d}.history[_ngcontent-%COMP%]   .date-group[_ngcontent-%COMP%]   .files-container[_ngcontent-%COMP%]{padding:8px;transition:all .3s ease;overflow:hidden}.history[_ngcontent-%COMP%]   .date-group[_ngcontent-%COMP%]   .files-container.collapsed[_ngcontent-%COMP%]{height:0!important;padding:0 8px;opacity:0}.history[_ngcontent-%COMP%]   .date-group[_ngcontent-%COMP%]   .files-container[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;padding:16px 20px;margin-bottom:8px;background:#fff;border-radius:12px;border:1px solid #f0f0f0;transition:all .2s ease;position:relative;min-height:60px}.history[_ngcontent-%COMP%]   .date-group[_ngcontent-%COMP%]   .files-container[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]:last-child{margin-bottom:0}.history[_ngcontent-%COMP%]   .date-group[_ngcontent-%COMP%]   .files-container[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]:hover{border-color:#bfa76a;box-shadow:0 2px 12px #bfa76a26;transform:translateY(-1px)}.history[_ngcontent-%COMP%]   .date-group[_ngcontent-%COMP%]   .files-container[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]   .file-info[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px;flex:1;min-width:0}.history[_ngcontent-%COMP%]   .date-group[_ngcontent-%COMP%]   .files-container[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]   .file-info[_ngcontent-%COMP%]   .file-index[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;width:24px;height:24px;background:linear-gradient(135deg,#bfa76a,#a68c4a);color:#fff;border-radius:50%;font-size:12px;font-weight:600;flex-shrink:0}.history[_ngcontent-%COMP%]   .date-group[_ngcontent-%COMP%]   .files-container[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]   .file-info[_ngcontent-%COMP%]   .file-name[_ngcontent-%COMP%]{font-weight:500;color:#1f2937;font-size:14px;line-height:1.4;flex:1;min-width:0;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.history[_ngcontent-%COMP%]   .date-group[_ngcontent-%COMP%]   .files-container[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]   .file-info[_ngcontent-%COMP%]   .file-source[_ngcontent-%COMP%]{flex-shrink:0;margin-left:auto;margin-right:16px}.history[_ngcontent-%COMP%]   .date-group[_ngcontent-%COMP%]   .files-container[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]   .file-info[_ngcontent-%COMP%]   .file-source[_ngcontent-%COMP%]   .source-badge[_ngcontent-%COMP%]{display:inline-flex;align-items:center;gap:6px;padding:6px 14px;border-radius:18px;font-size:11px;font-weight:600;white-space:nowrap;transition:all .25s cubic-bezier(.4,0,.2,1);cursor:help;border:1.5px solid;position:relative;overflow:hidden;min-width:70px;justify-content:center}.history[_ngcontent-%COMP%]   .date-group[_ngcontent-%COMP%]   .files-container[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]   .file-info[_ngcontent-%COMP%]   .file-source[_ngcontent-%COMP%]   .source-badge[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:0;left:-100%;width:100%;height:100%;background:linear-gradient(90deg,transparent,rgba(255,255,255,.3),transparent);transition:left .6s ease}.history[_ngcontent-%COMP%]   .date-group[_ngcontent-%COMP%]   .files-container[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]   .file-info[_ngcontent-%COMP%]   .file-source[_ngcontent-%COMP%]   .source-badge[_ngcontent-%COMP%]:hover:before{left:100%}.history[_ngcontent-%COMP%]   .date-group[_ngcontent-%COMP%]   .files-container[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]   .file-info[_ngcontent-%COMP%]   .file-source[_ngcontent-%COMP%]   .source-badge[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:11px;opacity:.9;z-index:1;position:relative}.history[_ngcontent-%COMP%]   .date-group[_ngcontent-%COMP%]   .files-container[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]   .file-info[_ngcontent-%COMP%]   .file-source[_ngcontent-%COMP%]   .source-badge.source-system[_ngcontent-%COMP%]{background:linear-gradient(135deg,#c4b382,#b8a676);color:#fff;border-color:#b8a676;box-shadow:0 2px 8px #c4b38240}.history[_ngcontent-%COMP%]   .date-group[_ngcontent-%COMP%]   .files-container[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]   .file-info[_ngcontent-%COMP%]   .file-source[_ngcontent-%COMP%]   .source-badge.source-system[_ngcontent-%COMP%]:hover{box-shadow:0 4px 16px #c4b38259;transform:translateY(-2px);background:linear-gradient(135deg,#d4c392,#c4b382)}.history[_ngcontent-%COMP%]   .date-group[_ngcontent-%COMP%]   .files-container[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]   .file-info[_ngcontent-%COMP%]   .file-source[_ngcontent-%COMP%]   .source-badge.source-resident[_ngcontent-%COMP%]{background:linear-gradient(135deg,#b8a676,#ae9b66);color:#fff;border-color:#ae9b66;box-shadow:0 2px 8px #b8a67640}.history[_ngcontent-%COMP%]   .date-group[_ngcontent-%COMP%]   .files-container[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]   .file-info[_ngcontent-%COMP%]   .file-source[_ngcontent-%COMP%]   .source-badge.source-resident[_ngcontent-%COMP%]:hover{box-shadow:0 4px 16px #b8a67659;transform:translateY(-2px);background:linear-gradient(135deg,#c4b382,#b8a676)}.history[_ngcontent-%COMP%]   .date-group[_ngcontent-%COMP%]   .files-container[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]   .file-info[_ngcontent-%COMP%]   .file-source[_ngcontent-%COMP%]   .source-badge.source-negotiation[_ngcontent-%COMP%]{background:linear-gradient(135deg,#ae9b66,#9b8a5a);color:#fff;border-color:#9b8a5a;box-shadow:0 2px 8px #ae9b6640}.history[_ngcontent-%COMP%]   .date-group[_ngcontent-%COMP%]   .files-container[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]   .file-info[_ngcontent-%COMP%]   .file-source[_ngcontent-%COMP%]   .source-badge.source-negotiation[_ngcontent-%COMP%]:hover{box-shadow:0 4px 16px #ae9b6659;transform:translateY(-2px);background:linear-gradient(135deg,#b8a676,#ae9b66)}.history[_ngcontent-%COMP%]   .date-group[_ngcontent-%COMP%]   .files-container[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]   .file-info[_ngcontent-%COMP%]   .file-source[_ngcontent-%COMP%]   .source-badge.source-unknown[_ngcontent-%COMP%]{background:linear-gradient(135deg,#9ca3af,#6b7280);color:#fff;border-color:#6b7280;box-shadow:0 2px 8px #9ca3af40}.history[_ngcontent-%COMP%]   .date-group[_ngcontent-%COMP%]   .files-container[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]   .file-info[_ngcontent-%COMP%]   .file-source[_ngcontent-%COMP%]   .source-badge.source-unknown[_ngcontent-%COMP%]:hover{box-shadow:0 4px 16px #9ca3af59;transform:translateY(-2px);background:linear-gradient(135deg,#a1a8b0,#9ca3af)}.history[_ngcontent-%COMP%]   .date-group[_ngcontent-%COMP%]   .files-container[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]   .download-actions[_ngcontent-%COMP%]{display:flex;gap:8px;align-items:center;flex-shrink:0}@media (max-width: 768px){.history[_ngcontent-%COMP%]   .date-group[_ngcontent-%COMP%]   .files-container[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start;gap:12px;padding:12px 16px}.history[_ngcontent-%COMP%]   .date-group[_ngcontent-%COMP%]   .files-container[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]   .file-info[_ngcontent-%COMP%]{width:100%;flex-wrap:wrap;gap:8px}.history[_ngcontent-%COMP%]   .date-group[_ngcontent-%COMP%]   .files-container[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]   .file-info[_ngcontent-%COMP%]   .file-source[_ngcontent-%COMP%]{margin-left:0;margin-right:0;order:3}.history[_ngcontent-%COMP%]   .date-group[_ngcontent-%COMP%]   .files-container[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]   .file-info[_ngcontent-%COMP%]   .file-source[_ngcontent-%COMP%]   .source-badge[_ngcontent-%COMP%]{font-size:10px;padding:4px 8px}.history[_ngcontent-%COMP%]   .date-group[_ngcontent-%COMP%]   .files-container[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]   .download-actions[_ngcontent-%COMP%]{width:100%;justify-content:flex-end}}.history[_ngcontent-%COMP%]   .download-actions[_ngcontent-%COMP%]   .download-btn[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;gap:8px;padding:10px 14px;background:#fff;border:1.5px solid #bfa76a;border-radius:10px;color:#bfa76a;font-size:13px;font-weight:600;cursor:pointer;transition:all .3s ease;text-decoration:none;box-shadow:0 2px 8px #00000014;min-width:100px;position:relative;overflow:hidden}.history[_ngcontent-%COMP%]   .download-actions[_ngcontent-%COMP%]   .download-btn[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:0;left:-100%;width:100%;height:100%;background:linear-gradient(90deg,transparent,rgba(191,167,106,.1),transparent);transition:left .5s ease}.history[_ngcontent-%COMP%]   .download-actions[_ngcontent-%COMP%]   .download-btn[_ngcontent-%COMP%]   .btn-content[_ngcontent-%COMP%]{display:flex;align-items:center;gap:6px;flex:1}.history[_ngcontent-%COMP%]   .download-actions[_ngcontent-%COMP%]   .download-btn[_ngcontent-%COMP%]   .btn-content[_ngcontent-%COMP%]   .file-type-icon[_ngcontent-%COMP%]{width:18px;height:18px}.history[_ngcontent-%COMP%]   .download-actions[_ngcontent-%COMP%]   .download-btn[_ngcontent-%COMP%]   .btn-content[_ngcontent-%COMP%]   .file-type-icon[src*=\\\"PDF.svg\\\"][_ngcontent-%COMP%], .history[_ngcontent-%COMP%]   .download-actions[_ngcontent-%COMP%]   .download-btn[_ngcontent-%COMP%]   .btn-content[_ngcontent-%COMP%]   .file-type-icon[src*=\\\"designFile.svg\\\"][_ngcontent-%COMP%]{filter:brightness(0) saturate(100%) invert(58%) sepia(25%) saturate(1000%) hue-rotate(25deg) brightness(95%) contrast(85%)}.history[_ngcontent-%COMP%]   .download-actions[_ngcontent-%COMP%]   .download-btn[_ngcontent-%COMP%]   .btn-content[_ngcontent-%COMP%]   .file-type-icon[src*=\\\"imageFile.svg\\\"][_ngcontent-%COMP%]{filter:none}.history[_ngcontent-%COMP%]   .download-actions[_ngcontent-%COMP%]   .download-btn[_ngcontent-%COMP%]   .btn-content[_ngcontent-%COMP%]   .download-text[_ngcontent-%COMP%]{font-size:13px;font-weight:600;letter-spacing:.02em}.history[_ngcontent-%COMP%]   .download-actions[_ngcontent-%COMP%]   .download-btn[_ngcontent-%COMP%]   .download-icon[_ngcontent-%COMP%]{font-size:16px;color:#bfa76a;transition:all .3s ease;opacity:.8}.history[_ngcontent-%COMP%]   .download-actions[_ngcontent-%COMP%]   .download-btn[_ngcontent-%COMP%]:hover{background:#bfa76a;color:#fff;border-color:#bfa76a;transform:translateY(-2px);box-shadow:0 6px 20px #bfa76a4d}.history[_ngcontent-%COMP%]   .download-actions[_ngcontent-%COMP%]   .download-btn[_ngcontent-%COMP%]:hover:before{left:100%}.history[_ngcontent-%COMP%]   .download-actions[_ngcontent-%COMP%]   .download-btn[_ngcontent-%COMP%]:hover   .btn-content[_ngcontent-%COMP%]   .file-type-icon[src*=\\\"PDF.svg\\\"][_ngcontent-%COMP%], .history[_ngcontent-%COMP%]   .download-actions[_ngcontent-%COMP%]   .download-btn[_ngcontent-%COMP%]:hover   .btn-content[_ngcontent-%COMP%]   .file-type-icon[src*=\\\"designFile.svg\\\"][_ngcontent-%COMP%]{filter:brightness(0) saturate(100%) invert(100%)}.history[_ngcontent-%COMP%]   .download-actions[_ngcontent-%COMP%]   .download-btn[_ngcontent-%COMP%]:hover   .btn-content[_ngcontent-%COMP%]   .file-type-icon[src*=\\\"imageFile.svg\\\"][_ngcontent-%COMP%]{filter:brightness(1.3) drop-shadow(0 0 3px rgba(255,255,255,.5))}.history[_ngcontent-%COMP%]   .download-actions[_ngcontent-%COMP%]   .download-btn[_ngcontent-%COMP%]:hover   .download-icon[_ngcontent-%COMP%]{color:#fff;opacity:1;transform:translateY(-1px)}.history[_ngcontent-%COMP%]   .download-actions[_ngcontent-%COMP%]   .download-btn[_ngcontent-%COMP%]:active{transform:translateY(-1px);box-shadow:0 3px 10px #bfa76a33}.upload-button-container[_ngcontent-%COMP%]{display:flex;justify-content:flex-end;align-items:center;margin-bottom:1rem}.upload-button-container[_ngcontent-%COMP%]   .file-input-hidden[_ngcontent-%COMP%]{display:none}.upload-button-container[_ngcontent-%COMP%]   .upload-button[_ngcontent-%COMP%]{display:inline-flex;align-items:center;justify-content:center;gap:8px;padding:10px 18px;font-size:14px;white-space:nowrap;transition:all .2s ease;box-shadow:0 2px 4px #0000001a;background:#bfa76a;color:#fff;border-radius:12px;font-weight:600;font-size:1.1rem;padding:.7rem 2.2rem;border:none}.upload-button-container[_ngcontent-%COMP%]   .upload-button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:14px}.upload-button-container[_ngcontent-%COMP%]   .upload-button[_ngcontent-%COMP%]:hover:not(:disabled){background:#a68c4a;transform:translateY(-1px);box-shadow:0 4px 12px #00000026}.upload-button-container[_ngcontent-%COMP%]   .upload-button[_ngcontent-%COMP%]:active:not(:disabled){transform:translateY(0)}.upload-button-container[_ngcontent-%COMP%]   .upload-button[_ngcontent-%COMP%]:disabled{opacity:.6;cursor:not-allowed;transform:none;box-shadow:0 2px 4px #0000001a}.upload-panel[_ngcontent-%COMP%]{background:#fffbe6;border-radius:12px;border:1px solid #e5e1d6}.upload-panel[_ngcontent-%COMP%]   .upload-panel-header[_ngcontent-%COMP%]   .upload-panel-title[_ngcontent-%COMP%]{display:flex;align-items:center;gap:10px;font-size:16px;font-weight:600;color:#1f2937;margin-bottom:4px}.upload-panel[_ngcontent-%COMP%]   .upload-panel-header[_ngcontent-%COMP%]   .upload-panel-title[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#3b82f6;font-size:18px}.upload-panel[_ngcontent-%COMP%]   .upload-panel-header[_ngcontent-%COMP%]   .upload-panel-title[_ngcontent-%COMP%]   .file-count-badge[_ngcontent-%COMP%]{background:#bfa76a;color:#fff;border-radius:50%;padding:.2rem .7rem;font-size:1rem;margin-left:.7rem;display:inline-block}.upload-panel[_ngcontent-%COMP%]   .upload-panel-header[_ngcontent-%COMP%]   .upload-panel-subtitle[_ngcontent-%COMP%]{font-size:13px;color:#6b7280;font-weight:500}.uploaded-files-container[_ngcontent-%COMP%]{margin-top:16px}.uploaded-files[_ngcontent-%COMP%]   .uploaded-file-item[_ngcontent-%COMP%]{display:flex;align-items:center;padding:16px;border:1px solid #e1e5e9;border-radius:12px;margin-bottom:12px;background:linear-gradient(135deg,#fff,#f8fafc);box-shadow:0 2px 8px #0000000f;transition:all .3s ease;position:relative;overflow:hidden}.uploaded-files[_ngcontent-%COMP%]   .uploaded-file-item[_ngcontent-%COMP%]:hover{border-color:#c7d2fe;box-shadow:0 4px 16px #0000001f;transform:translateY(-1px)}.uploaded-files[_ngcontent-%COMP%]   .uploaded-file-item[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;left:0;top:0;bottom:0;width:4px;background:linear-gradient(135deg,#3b82f6,#1d4ed8)}.uploaded-files[_ngcontent-%COMP%]   .uploaded-file-item[_ngcontent-%COMP%]:last-child{margin-bottom:0}.uploaded-files[_ngcontent-%COMP%]   .uploaded-file-item[_ngcontent-%COMP%]   .file-status-indicator[_ngcontent-%COMP%]{margin-right:12px;padding:8px;background:#fef3cd;border-radius:50%;display:flex;align-items:center;justify-content:center}.uploaded-files[_ngcontent-%COMP%]   .uploaded-file-item[_ngcontent-%COMP%]   .file-status-indicator[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#d97706;font-size:14px}.uploaded-files[_ngcontent-%COMP%]   .uploaded-file-item[_ngcontent-%COMP%]   .file-info[_ngcontent-%COMP%]{display:flex;align-items:center;flex:1;gap:14px}.uploaded-files[_ngcontent-%COMP%]   .uploaded-file-item[_ngcontent-%COMP%]   .file-info[_ngcontent-%COMP%]   .file-icon-wrapper[_ngcontent-%COMP%]{padding:8px;background:#f1f5f9;border-radius:8px;display:flex;align-items:center;justify-content:center;box-shadow:0 2px 4px #0000000d}.uploaded-files[_ngcontent-%COMP%]   .uploaded-file-item[_ngcontent-%COMP%]   .file-info[_ngcontent-%COMP%]   .file-icon-wrapper[_ngcontent-%COMP%]   .file-icon[_ngcontent-%COMP%]{width:32px;height:32px;filter:drop-shadow(0 1px 2px rgba(0,0,0,.1))}.uploaded-files[_ngcontent-%COMP%]   .uploaded-file-item[_ngcontent-%COMP%]   .file-info[_ngcontent-%COMP%]   .file-details[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:4px;min-width:0}.uploaded-files[_ngcontent-%COMP%]   .uploaded-file-item[_ngcontent-%COMP%]   .file-info[_ngcontent-%COMP%]   .file-details[_ngcontent-%COMP%]   .file-name[_ngcontent-%COMP%]{font-weight:600;color:#1f2937;font-size:15px;line-height:1.4;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;max-width:300px}.uploaded-files[_ngcontent-%COMP%]   .uploaded-file-item[_ngcontent-%COMP%]   .file-info[_ngcontent-%COMP%]   .file-details[_ngcontent-%COMP%]   .file-meta[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px}.uploaded-files[_ngcontent-%COMP%]   .uploaded-file-item[_ngcontent-%COMP%]   .file-info[_ngcontent-%COMP%]   .file-details[_ngcontent-%COMP%]   .file-meta[_ngcontent-%COMP%]   .file-size[_ngcontent-%COMP%]{font-size:13px;color:#6b7280;font-weight:500}.uploaded-files[_ngcontent-%COMP%]   .uploaded-file-item[_ngcontent-%COMP%]   .file-info[_ngcontent-%COMP%]   .file-details[_ngcontent-%COMP%]   .file-meta[_ngcontent-%COMP%]   .file-type[_ngcontent-%COMP%]{font-size:12px;color:#3b82f6;background:#eff6ff;padding:2px 8px;border-radius:12px;font-weight:600}.uploaded-files[_ngcontent-%COMP%]   .uploaded-file-item[_ngcontent-%COMP%]   .file-actions[_ngcontent-%COMP%]   .delete-btn[_ngcontent-%COMP%]{background:none;border:none;color:#ef4444;cursor:pointer;padding:10px;border-radius:8px;transition:all .2s ease;display:flex;align-items:center;justify-content:center}.uploaded-files[_ngcontent-%COMP%]   .uploaded-file-item[_ngcontent-%COMP%]   .file-actions[_ngcontent-%COMP%]   .delete-btn[_ngcontent-%COMP%]:hover{background-color:#fef2f2;transform:scale(1.1)}.uploaded-files[_ngcontent-%COMP%]   .uploaded-file-item[_ngcontent-%COMP%]   .file-actions[_ngcontent-%COMP%]   .delete-btn[_ngcontent-%COMP%]:active{transform:scale(.95)}.uploaded-files[_ngcontent-%COMP%]   .uploaded-file-item[_ngcontent-%COMP%]   .file-actions[_ngcontent-%COMP%]   .delete-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:16px}.upload-actions[_ngcontent-%COMP%]{margin-top:20px;padding-top:16px;border-top:1px solid #f3f4f6;display:flex;justify-content:center;gap:16px}.upload-actions[_ngcontent-%COMP%]   .confirm-upload-btn[_ngcontent-%COMP%]{display:inline-flex;align-items:center;gap:8px;padding:12px 24px;transition:all .2s ease;min-width:120px;justify-content:center;background:#2563eb;color:#fff;border-radius:8px;font-weight:600;font-size:1.1rem;padding:.6rem 2.2rem;border:none;margin-right:1rem}.upload-actions[_ngcontent-%COMP%]   .confirm-upload-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:16px}.upload-actions[_ngcontent-%COMP%]   .confirm-upload-btn[_ngcontent-%COMP%]:hover:not(:disabled){background:#1746a2;transform:translateY(-1px);box-shadow:0 4px 12px #00000026}.upload-actions[_ngcontent-%COMP%]   .confirm-upload-btn[_ngcontent-%COMP%]:active:not(:disabled){transform:translateY(0)}.upload-actions[_ngcontent-%COMP%]   .confirm-upload-btn[_ngcontent-%COMP%]:disabled{opacity:.6;cursor:not-allowed}.upload-actions[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]{display:inline-flex;align-items:center;gap:8px;padding:12px 24px;font-weight:600;border-radius:8px;transition:all .2s ease;min-width:100px;justify-content:center;background:#fff;color:#bfa76a;border:1.5px solid #bfa76a}.upload-actions[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:16px}.upload-actions[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]:hover:not(:disabled){background:#f8f7f3;color:#a68c4a;transform:translateY(-1px);box-shadow:0 4px 12px #00000026}.upload-actions[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]:active:not(:disabled){transform:translateY(0)}.upload-actions[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]:disabled{opacity:.6;cursor:not-allowed}.error-message-container[_ngcontent-%COMP%]{margin-top:1.2rem;display:flex;justify-content:center;margin-bottom:20px}.error-message-container[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%]{display:flex;align-items:center;gap:10px;padding:14px 18px;background:linear-gradient(135deg,#fef2f2,#fde8e8);border:1px solid #fecaca;border-radius:8px;color:#dc2626;font-size:14px;font-weight:500;max-width:500px;box-shadow:0 2px 8px #dc26261a}.error-message-container[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:18px;flex-shrink:0;color:#ef4444}.upload-hint-text-header[_ngcontent-%COMP%]{color:#bfa76a;font-weight:600;margin-bottom:.5rem}.upload-instructions[_ngcontent-%COMP%]{background:#f8f7f3;border-radius:10px;padding:1.2rem 1.5rem;margin-bottom:1.2rem;color:#6b5e3c;font-size:1.08rem;border:1px solid #e5e1d6}.upload-instructions[_ngcontent-%COMP%]   .upload-hint-text-header[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;font-size:14px;color:#4b5563;margin-bottom:12px;font-weight:500;text-align:left}.upload-instructions[_ngcontent-%COMP%]   .upload-hint-text-header[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#3b82f6;font-size:16px}.upload-instructions[_ngcontent-%COMP%]   .upload-features[_ngcontent-%COMP%]{display:flex;gap:24px}.upload-instructions[_ngcontent-%COMP%]   .upload-features[_ngcontent-%COMP%]   .feature-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:6px;font-size:13px;color:#6b7280;font-weight:500;margin-right:1.5rem;display:inline-block}.upload-instructions[_ngcontent-%COMP%]   .upload-features[_ngcontent-%COMP%]   .feature-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#10b981;font-size:14px}.drag-drop-zone[_ngcontent-%COMP%]{border:2px dashed #bfa76a;background:#faf9f6;border-radius:16px;padding:2.5rem 1.5rem;margin-bottom:2rem;text-align:center;transition:border-color .2s,background .2s;cursor:pointer}.drag-drop-zone[_ngcontent-%COMP%]:hover{border-color:#3b82f6;background:linear-gradient(135deg,#eff6ff,#dbeafe);transform:translateY(-2px);box-shadow:0 8px 25px #3b82f626}.drag-drop-zone.drag-over[_ngcontent-%COMP%]{border-color:#a68c4a;background:#f5f1e6;box-shadow:0 8px 25px #10b98133;transform:scale(1.02)}.drag-drop-zone.drag-over[_ngcontent-%COMP%]   .drag-icon[_ngcontent-%COMP%]{color:#10b981;animation:_ngcontent-%COMP%_bounce 1s infinite}.drag-drop-zone.drag-over[_ngcontent-%COMP%]   .drag-text-primary[_ngcontent-%COMP%]{color:#065f46}.drag-drop-zone[_ngcontent-%COMP%]   .drag-drop-content[_ngcontent-%COMP%]   .drag-icon[_ngcontent-%COMP%]{font-size:3.5rem;color:#bfa76a;margin-bottom:.7rem;transition:all .3s ease}.drag-drop-zone[_ngcontent-%COMP%]   .drag-drop-content[_ngcontent-%COMP%]   .drag-text-primary[_ngcontent-%COMP%]{font-size:1.3rem;font-weight:600;color:#222;margin-bottom:8px;transition:color .3s ease}.drag-drop-zone[_ngcontent-%COMP%]   .drag-drop-content[_ngcontent-%COMP%]   .drag-text-secondary[_ngcontent-%COMP%]{color:#bfa76a;font-size:1.05rem;margin-bottom:.5rem}.drag-drop-zone[_ngcontent-%COMP%]   .drag-drop-content[_ngcontent-%COMP%]   .supported-formats[_ngcontent-%COMP%]{display:flex;justify-content:center;gap:8px;flex-wrap:wrap}.drag-drop-zone[_ngcontent-%COMP%]   .drag-drop-content[_ngcontent-%COMP%]   .supported-formats[_ngcontent-%COMP%]   .format-badge[_ngcontent-%COMP%]{background:#bfa76a;color:#fff;border-radius:8px;padding:.2rem 1.1rem;margin:0 .2rem;font-size:1rem;font-weight:600;letter-spacing:.05em;display:inline-block;transition:background .2s,box-shadow .2s}.drag-drop-zone[_ngcontent-%COMP%]   .drag-drop-content[_ngcontent-%COMP%]   .supported-formats[_ngcontent-%COMP%]   .format-badge[_ngcontent-%COMP%]:hover{background:#e5d7b0;color:#bfa76a;box-shadow:0 2px 8px #bfa76a33}@keyframes _ngcontent-%COMP%_bounce{0%,20%,50%,80%,to{transform:translateY(0)}40%{transform:translateY(-10px)}60%{transform:translateY(-5px)}}.hover\\\\:bg-opacity-75[_ngcontent-%COMP%]:hover{--tw-bg-opacity: .75}.hover\\\\:shadow-lg[_ngcontent-%COMP%]:hover{--tw-shadow: 0 10px 15px -3px rgb(0 0 0 / .1), 0 4px 6px -4px rgb(0 0 0 / .1);--tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}@media not all and (min-width: 1280px){.max-xl\\\\:grid-cols-6[_ngcontent-%COMP%]{grid-template-columns:repeat(6,minmax(0,1fr))}}@media not all and (min-width: 1024px){.max-lg\\\\:bottom-12[_ngcontent-%COMP%]{bottom:3rem}.max-lg\\\\:w-full[_ngcontent-%COMP%]{width:100%}.max-lg\\\\:grid-cols-4[_ngcontent-%COMP%]{grid-template-columns:repeat(4,minmax(0,1fr))}}@media not all and (min-width: 768px){.max-md\\\\:bottom-14[_ngcontent-%COMP%]{bottom:3.5rem}.max-md\\\\:my-4[_ngcontent-%COMP%]{margin-top:1rem;margin-bottom:1rem}.max-md\\\\:ml-0[_ngcontent-%COMP%]{margin-left:0}.max-md\\\\:mt-10[_ngcontent-%COMP%]{margin-top:2.5rem}.max-md\\\\:mt-5[_ngcontent-%COMP%]{margin-top:1.25rem}.max-md\\\\:hidden[_ngcontent-%COMP%]{display:none}.max-md\\\\:h-\\\\__ph-0__[_ngcontent-%COMP%]{height:65%}.max-md\\\\:\\\\!w-\\\\__ph-0__[_ngcontent-%COMP%]{width:450px!important}.max-md\\\\:w-\\\\__ph-0__[_ngcontent-%COMP%]{width:500px}.max-md\\\\:w-full[_ngcontent-%COMP%]{width:100%}.max-md\\\\:max-w-full[_ngcontent-%COMP%]{max-width:100%}.max-md\\\\:flex-col[_ngcontent-%COMP%]{flex-direction:column}.max-md\\\\:flex-col-reverse[_ngcontent-%COMP%]{flex-direction:column-reverse}.max-md\\\\:flex-wrap[_ngcontent-%COMP%]{flex-wrap:wrap}.max-md\\\\:items-start[_ngcontent-%COMP%]{align-items:flex-start}}@media not all and (min-width: 640px){.max-sm\\\\:bottom-10[_ngcontent-%COMP%]{bottom:2.5rem}.max-sm\\\\:ml-8[_ngcontent-%COMP%]{margin-left:2rem}.max-sm\\\\:block[_ngcontent-%COMP%]{display:block}.max-sm\\\\:\\\\!w-\\\\__ph-0__[_ngcontent-%COMP%]{width:340px!important}.max-sm\\\\:w-\\\\__ph-0__[_ngcontent-%COMP%]{width:380px}.max-sm\\\\:\\\\!grid-cols-2[_ngcontent-%COMP%]{grid-template-columns:repeat(2,minmax(0,1fr))!important}.max-sm\\\\:\\\\!justify-between[_ngcontent-%COMP%]{justify-content:space-between!important}.max-sm\\\\:gap-0[_ngcontent-%COMP%]{gap:0px}.max-sm\\\\:px-0[_ngcontent-%COMP%]{padding-left:0;padding-right:0}.max-sm\\\\:pl-0[_ngcontent-%COMP%]{padding-left:0}.max-sm\\\\:text-base[_ngcontent-%COMP%]{font-size:1rem;line-height:1.5rem}}@media (min-width: 640px){.sm\\\\:mx-0[_ngcontent-%COMP%]{margin-left:0;margin-right:0}.sm\\\\:mb-0[_ngcontent-%COMP%]{margin-bottom:0}.sm\\\\:mt-1[_ngcontent-%COMP%]{margin-top:.25rem}.sm\\\\:flex[_ngcontent-%COMP%]{display:flex}.sm\\\\:grid-cols-2[_ngcontent-%COMP%]{grid-template-columns:repeat(2,minmax(0,1fr))}.sm\\\\:items-center[_ngcontent-%COMP%]{align-items:center}.sm\\\\:pl-3[_ngcontent-%COMP%]{padding-left:.75rem}}@media (min-width: 768px){.md\\\\:flex[_ngcontent-%COMP%]{display:flex}.md\\\\:grid-cols-3[_ngcontent-%COMP%]{grid-template-columns:repeat(3,minmax(0,1fr))}.md\\\\:pb-20[_ngcontent-%COMP%]{padding-bottom:5rem}}@media (min-width: 1024px){.lg\\\\:\\\\!mx-0[_ngcontent-%COMP%]{margin-left:0!important;margin-right:0!important}.lg\\\\:\\\\!mt-0[_ngcontent-%COMP%]{margin-top:0!important}.lg\\\\:flex[_ngcontent-%COMP%]{display:flex}.lg\\\\:\\\\!w-\\\\__ph-0__[_ngcontent-%COMP%]{width:180px!important}.lg\\\\:\\\\!w-\\\\__ph-0__[_ngcontent-%COMP%]{width:800px!important}.lg\\\\:\\\\!w-\\\\__ph-0__[_ngcontent-%COMP%]{width:900px!important}.lg\\\\:\\\\!flex-row[_ngcontent-%COMP%]{flex-direction:row!important}.lg\\\\:\\\\!px-4[_ngcontent-%COMP%]{padding-left:1rem!important;padding-right:1rem!important}.lg\\\\:\\\\!px-\\\\__ph-0__[_ngcontent-%COMP%]{padding-left:22px!important;padding-right:22px!important}.lg\\\\:\\\\!py-\\\\__ph-0__[_ngcontent-%COMP%]{padding-top:18px!important;padding-bottom:18px!important}}\"],\n    data: {\n      animation: [trigger('slideInOut', [state('in', style({\n        height: '*',\n        opacity: 1,\n        overflow: 'visible'\n      })), state('out', style({\n        height: '0px',\n        opacity: 0,\n        overflow: 'hidden'\n      })), transition('in => out', animate('300ms ease-in-out')), transition('out => in', animate('300ms ease-in-out'))])]\n    }\n  });\n}", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "MessageService", "PanelModule", "PanelMenuModule", "ToastModule", "DateFormatPipe", "LocalStorageService", "STORAGE_KEY", "ToastMessage", "trigger", "state", "style", "transition", "animate", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵlistener", "HistoryComponent_div_14_Template_div_drop_0_listener", "$event", "ɵɵrestoreView", "_r2", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "onDragDrop", "HistoryComponent_div_14_Template_div_dragover_0_listener", "onDragOver", "HistoryComponent_div_14_Template_div_dragleave_0_listener", "onDragLeave", "HistoryComponent_div_14_Template_div_click_0_listener", "fileInput_r4", "ɵɵreference", "click", "ɵɵclassProp", "isDragOver", "ɵɵtemplate", "HistoryComponent_div_17_ng_template_2_Template", "HistoryComponent_div_17_Template_button_click_7_listener", "_r5", "downloadFileWithoutRedirect", "houseHoldDetaiPicture", "ɵɵadvance", "ɵɵproperty", "ɵɵtextInterpolate", "CName", "getDownloadTitle", "getDownloadIcon", "ɵɵsanitizeUrl", "getFileTypeText", "ɵɵtextInterpolate1", "getTotalFilesCount", "HistoryComponent_div_18_ng_template_2_span_5_Template", "HistoryComponent_div_18_ng_template_2_Template_button_click_8_listener", "_r6", "expandAllGroups", "HistoryComponent_div_18_ng_template_2_Template_button_click_10_listener", "collapseAllGroups", "HistoryComponent_div_18_div_3_div_1_Template_div_click_0_listener", "_r7", "file_r8", "$implicit", "toggleDateGroup", "CChangeDate", "ɵɵpipeBind1", "SpecialChangeFiles", "length", "isDateGroupCollapsed", "ɵɵclassMap", "getSourceClass", "item_r9", "CSource", "getSourceDescription", "getSourceIcon", "getSourceName", "HistoryComponent_div_18_div_3_div_3_div_7_Template_button_click_1_listener", "_r10", "CFile", "CFileName", "HistoryComponent_div_18_div_3_div_3_div_6_Template", "HistoryComponent_div_18_div_3_div_3_div_7_Template", "i_r11", "HistoryComponent_div_18_div_3_div_1_Template", "HistoryComponent_div_18_div_3_div_3_Template", "HistoryComponent_div_18_ng_template_2_Template", "HistoryComponent_div_18_div_3_Template", "listSpecialChange", "uploadedFiles", "HistoryComponent_div_19_div_5_Template_button_click_15_listener", "i_r14", "_r13", "index", "removeFile", "getFileIcon", "file_r15", "name", "formatFileSize", "size", "getFileType", "HistoryComponent_div_19_ng_template_2_Template", "HistoryComponent_div_19_div_5_Template", "HistoryComponent_div_19_Template_button_click_7_listener", "_r12", "uploadFiles", "HistoryComponent_div_19_span_9_Template", "HistoryComponent_div_19_span_10_Template", "HistoryComponent_div_19_Template_button_click_11_listener", "clearFiles", "isUploading", "uploadError", "HistoryComponent", "constructor", "_houseService", "_specialChangeService", "_utilityService", "_toastService", "_fileService", "items", "specialChangeFiles", "dataUser", "GetLocalStorage", "SAVE_LOGIN", "maxFileSize", "allowedFileTypes", "allowedMimeTypes", "showUploadArea", "collapsedStates", "ngOnInit", "getHouseHoldDetaiPic", "buildCaseId", "holdDetailId", "getListSpecialChange", "apiSpecialChangeGetSpecialChangeFilePost$Json", "body", "subscribe", "res", "Entries", "StatusCode", "initializeCollapsedStates", "Array", "isArray", "for<PERSON>ach", "group", "apiHouseGetHouseRegularPicturePost$Json", "CFileURL", "getFileNameFromUrl", "url", "parts", "split", "fileName", "pop", "files", "getFile", "next", "response", "window", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "error", "console", "showErrorMSG", "open", "onFileSelected", "event", "input", "target", "handleFiles", "from", "preventDefault", "stopPropagation", "dataTransfer", "file", "validateFile", "some", "f", "push", "fileExtension", "toLowerCase", "includes", "splice", "bytes", "k", "sizes", "i", "Math", "floor", "log", "parseFloat", "pow", "toFixed", "filePromises", "map", "convertFileToBase64", "Promise", "all", "then", "dataUrls", "dataUrl", "CFileBlood", "CFileType", "getFileTypeFromExtension", "apiSpecialChangeUploadSpecialChangePost$Json", "showSucessMSG", "Message", "catch", "resolve", "reject", "reader", "FileReader", "readAsDataURL", "onload", "result", "base64", "onerror", "extension", "substring", "lastIndexOf", "toggleUploadArea", "reduce", "total", "changeDate", "Object", "keys", "key", "source", "getSourceStats", "stats", "hasMultipleSources", "sources", "Set", "filter", "_", "ɵɵdirectiveInject", "i1", "HouseService", "SpecialChangeService", "i2", "UtilityService", "i3", "i4", "FileService", "_2", "selectors", "standalone", "features", "ɵɵProvidersFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "HistoryComponent_Template", "rf", "ctx", "HistoryComponent_Template_button_click_9_listener", "_r1", "HistoryComponent_span_11_Template", "HistoryComponent_span_12_Template", "HistoryComponent_div_13_Template", "HistoryComponent_div_14_Template", "HistoryComponent_Template_input_change_15_listener", "HistoryComponent_div_17_Template", "HistoryComponent_div_18_Template", "HistoryComponent_div_19_Template", "HistoryComponent_div_20_Template", "i5", "RouterLink", "i6", "Panel", "i7", "PrimeTemplate", "i8", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i9", "Toast", "styles", "data", "animation", "height", "opacity", "overflow"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\SaleChangeFront\\src\\app\\pages\\history\\history.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\SaleChangeFront\\src\\app\\pages\\history\\history.component.html"], "sourcesContent": ["import { CommonModule } from '@angular/common';\r\nimport { HttpResponse } from '@angular/common/http';\r\nimport { Component } from '@angular/core';\r\nimport { RouterModule } from '@angular/router';\r\nimport { MenuItem, MessageService } from 'primeng/api';\r\nimport { PanelModule } from 'primeng/panel';\r\nimport { PanelMenuModule } from 'primeng/panelmenu';\r\nimport { ToastModule } from 'primeng/toast';\r\nimport { HouseService, SpecialChangeService } from '../../../services/api/services';\r\nimport { SpecialChangeFileGroup, SpecialChangeFile } from '../../../services/api/models';\r\nimport { DateFormatPipe } from '../../shared/pipes/date-format.pipe';\r\nimport { LocalStorageService } from '../../shared/services/local-storage.service';\r\nimport { STORAGE_KEY } from '../../shared/constant/constant';\r\nimport { UtilityService } from '../../shared/services/utility.service';\r\nimport { ToastMessage } from '../../shared/services/message.service';\r\nimport { FileService } from '../../../services/File.service';\r\nimport { trigger, state, style, transition, animate } from '@angular/animations';\r\n@Component({\r\n  selector: 'app-history',\r\n  standalone: true,\r\n  providers: [\r\n    MessageService,\r\n    ToastMessage,\r\n  ],\r\n  imports: [RouterModule, PanelModule, PanelMenuModule, CommonModule, DateFormatPipe, ToastModule],\r\n  templateUrl: './history.component.html',\r\n  styleUrl: './history.component.scss',\r\n  animations: [\r\n    trigger('slideInOut', [\r\n      state('in', style({\r\n        height: '*',\r\n        opacity: 1,\r\n        overflow: 'visible'\r\n      })),\r\n      state('out', style({\r\n        height: '0px',\r\n        opacity: 0,\r\n        overflow: 'hidden'\r\n      })),\r\n      transition('in => out', animate('300ms ease-in-out')),\r\n      transition('out => in', animate('300ms ease-in-out'))\r\n    ])\r\n  ]\r\n})\r\nexport class HistoryComponent {\r\n  items: MenuItem[] = [];\r\n  houseHoldDetaiPicture: { CFile: string | any; CName: string | any } = { CFile: '', CName: '' }\r\n  specialChangeFiles: SpecialChangeFileGroup[] = []\r\n  dataUser = LocalStorageService.GetLocalStorage(STORAGE_KEY.SAVE_LOGIN) as any;\r\n  buildCaseId: any\r\n  holdDetailId: any\r\n\r\n  // 檔案上傳相關屬性\r\n  uploadedFiles: File[] = [];\r\n  isUploading: boolean = false;\r\n  uploadError: string = '';\r\n  maxFileSize: number = 10 * 1024 * 1024; // 10MB\r\n  allowedFileTypes: string[] = ['.pdf', '.dwg', '.dxf', '.dwf'];\r\n  allowedMimeTypes: string[] = ['application/pdf', 'image/vnd.dwg', 'application/acad', 'application/x-acad'];\r\n  isDragOver: boolean = false;\r\n  showUploadArea: boolean = false; // 控制上傳區域顯示\r\n\r\n  constructor(\r\n    private _houseService: HouseService,\r\n    private _specialChangeService: SpecialChangeService,\r\n    private _utilityService: UtilityService,\r\n    private _toastService: ToastMessage,\r\n    private _fileService: FileService\r\n  ) {\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.getHouseHoldDetaiPic()\r\n    this.buildCaseId = this.dataUser.buildCaseId\r\n    this.holdDetailId = this.dataUser.holdDetailId\r\n    this.getListSpecialChange()\r\n  }\r\n\r\n  listSpecialChange: SpecialChangeFileGroup[] | any\r\n  // 收合狀態管理\r\n  collapsedStates: { [key: string]: boolean } = {};\r\n\r\n  getListSpecialChange() {\r\n    this._specialChangeService.apiSpecialChangeGetSpecialChangeFilePost$Json({ body: this.buildCaseId }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        this.listSpecialChange = res.Entries! ?? []\r\n        // 初始化收合狀態：預設展開前三個日期群組\r\n        this.initializeCollapsedStates();\r\n      }\r\n    })\r\n  }\r\n\r\n  // 初始化收合狀態\r\n  private initializeCollapsedStates() {\r\n    if (this.listSpecialChange && Array.isArray(this.listSpecialChange)) {\r\n      this.listSpecialChange.forEach((group, index) => {\r\n        // 前三個群組預設展開（false = 展開），其餘收合（true = 收合）\r\n        this.collapsedStates[group.CChangeDate] = index >= 3;\r\n      });\r\n    }\r\n  }\r\n\r\n  getHouseHoldDetaiPic() {\r\n    this._houseService.apiHouseGetHouseRegularPicturePost$Json({}).subscribe(res => {\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        this.houseHoldDetaiPicture = {\r\n          CFile: res.Entries.CFileURL ? res.Entries.CFileURL : '',\r\n          CName: res.Entries.CFileName,\r\n        }\r\n      }\r\n    })\r\n  }\r\n\r\n  getFileNameFromUrl(url: string) {\r\n    const parts = url.split('/');\r\n    const fileName = parts.pop();\r\n    return fileName;\r\n  }\r\n\r\n  downloadFileWithoutRedirect(files: { CFile: string | any; CName: string | any }) {\r\n    if (files.CFile && files.CName) {\r\n      this._fileService.getFile(files.CFile, files.CName).subscribe({\r\n        next: (response: HttpResponse<Blob>) => {\r\n          if (response.body) {\r\n            // 創建下載鏈接\r\n            const url = window.URL.createObjectURL(response.body);\r\n            const link = document.createElement('a');\r\n            link.href = url;\r\n            link.download = files.CName || 'download';\r\n            document.body.appendChild(link);\r\n            link.click();\r\n            document.body.removeChild(link);\r\n            window.URL.revokeObjectURL(url);\r\n          }\r\n        },\r\n        error: (error) => {\r\n          console.error('檔案下載失敗:', error);\r\n          this._toastService.showErrorMSG('檔案下載失敗');\r\n          // 如果 FileService 失敗，回退到原來的方法\r\n          window.open(files.CFile, '_blank');\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  // 檔案選擇事件處理\r\n  onFileSelected(event: Event) {\r\n    const input = event.target as HTMLInputElement;\r\n    if (input.files) {\r\n      this.handleFiles(Array.from(input.files));\r\n    }\r\n  }\r\n\r\n  // 拖拽事件處理\r\n  onDragOver(event: DragEvent) {\r\n    event.preventDefault();\r\n    event.stopPropagation();\r\n    this.isDragOver = true;\r\n  }\r\n\r\n  onDragLeave(event: DragEvent) {\r\n    event.preventDefault();\r\n    event.stopPropagation();\r\n    this.isDragOver = false;\r\n  }\r\n\r\n  onDragDrop(event: DragEvent) {\r\n    event.preventDefault();\r\n    event.stopPropagation();\r\n    this.isDragOver = false;\r\n\r\n    const files = event.dataTransfer?.files;\r\n    if (files) {\r\n      this.handleFiles(Array.from(files));\r\n    }\r\n  }\r\n\r\n  // 處理檔案\r\n  private handleFiles(files: File[]) {\r\n    this.uploadError = '';\r\n\r\n    for (const file of files) {\r\n      if (!this.validateFile(file)) {\r\n        continue;\r\n      }\r\n\r\n      // 檢查是否已存在相同檔案\r\n      if (!this.uploadedFiles.some(f => f.name === file.name)) {\r\n        this.uploadedFiles.push(file);\r\n      }\r\n    }\r\n  }\r\n\r\n  // 檔案驗證\r\n  private validateFile(file: File): boolean {\r\n    // 檢查檔案大小\r\n    if (file.size > this.maxFileSize) {\r\n      this.uploadError = `檔案 \"${file.name}\" 超過大小限制 (10MB)`;\r\n      return false;\r\n    }\r\n\r\n    // 檢查檔案類型\r\n    const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();\r\n    if (!this.allowedFileTypes.includes(fileExtension)) {\r\n      this.uploadError = `檔案 \"${file.name}\" 格式不支援，只支援 PDF、DWG、DXF、DWF 格式`;\r\n      return false;\r\n    }\r\n\r\n    return true;\r\n  }\r\n\r\n  // 移除檔案\r\n  removeFile(index: number) {\r\n    this.uploadedFiles.splice(index, 1);\r\n    this.uploadError = '';\r\n  }\r\n\r\n  // 格式化檔案大小\r\n  formatFileSize(bytes: number): string {\r\n    if (bytes === 0) return '0 Bytes';\r\n\r\n    const k = 1024;\r\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\r\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\r\n\r\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\r\n  }\r\n\r\n  // 上傳檔案\r\n  uploadFiles() {\r\n    if (this.uploadedFiles.length === 0) {\r\n      this.uploadError = '請選擇要上傳的檔案';\r\n      return;\r\n    }\r\n\r\n    this.isUploading = true;\r\n    this.uploadError = '';\r\n\r\n    // 將所有檔案轉換為 data URL 格式（包含 MIME 類型）\r\n    const filePromises = this.uploadedFiles.map(file => this.convertFileToBase64(file));\r\n\r\n    Promise.all(filePromises).then(dataUrls => {\r\n      // 組成 SpecialChangeFile 陣列\r\n      const specialChangeFiles: SpecialChangeFile[] = dataUrls.map((dataUrl, index) => ({\r\n        CFileBlood: dataUrl, // 完整的 data URL 格式，包含 MIME 類型\r\n        CFileName: this.uploadedFiles[index].name,\r\n        CFileType: this.getFileTypeFromExtension(this.uploadedFiles[index].name)\r\n      }));\r\n\r\n      // 調用 API\r\n      this._specialChangeService.apiSpecialChangeUploadSpecialChangePost$Json({ body: specialChangeFiles }).subscribe({\r\n        next: (response) => {\r\n          this.isUploading = false;\r\n          this.uploadedFiles = [];\r\n          this.showUploadArea = false; // 上傳成功後隱藏上傳區域\r\n          this.getListSpecialChange(); // 重新載入檔案列表\r\n\r\n          // 顯示成功訊息\r\n          this._toastService.showSucessMSG('檔案上傳成功');\r\n        },\r\n        error: (error) => {\r\n          this.isUploading = false;\r\n          this.uploadError = '上傳失敗，請稍後再試';\r\n          console.error('Upload error:', error);\r\n          this._toastService.showErrorMSG('檔案上傳失敗：' + (error.error?.Message || '未知錯誤'));\r\n        }\r\n      });\r\n    }).catch(error => {\r\n      this.isUploading = false;\r\n      this.uploadError = '檔案處理失敗';\r\n      console.error('File processing error:', error);\r\n      this._toastService.showErrorMSG('檔案處理失敗');\r\n    });\r\n  }\r\n\r\n  // 將檔案轉換為 base64 純字串（不含 data URL 前綴）\r\n  private convertFileToBase64(file: File): Promise<string> {\r\n    return new Promise((resolve, reject) => {\r\n      const reader = new FileReader();\r\n      reader.readAsDataURL(file);\r\n      reader.onload = () => {\r\n        const result = reader.result as string;\r\n        // 只取 base64 部分\r\n        const base64 = result.split(',')[1];\r\n        resolve(base64);\r\n      };\r\n      reader.onerror = error => reject(error);\r\n    });\r\n  }\r\n\r\n  // 根據檔案副檔名判斷檔案類型\r\n  private getFileTypeFromExtension(fileName: string): number {\r\n    const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));\r\n    switch (extension) {\r\n      case '.pdf':\r\n        return 1; // 假設 1 代表 PDF\r\n      case '.dwg':\r\n        return 2; // 假設 2 代表 DWG\r\n      case '.dxf':\r\n        return 3; // 假設 3 代表 DXF\r\n      case '.dwf':\r\n        return 4; // 假設 4 代表 DWF\r\n      default:\r\n        return 0; // 未知類型\r\n    }\r\n  }\r\n\r\n  // 清除所有待上傳檔案\r\n  clearFiles() {\r\n    this.uploadedFiles = [];\r\n    this.uploadError = '';\r\n    this.showUploadArea = false; // 清除檔案時隱藏上傳區域\r\n  }\r\n\r\n  // 根據檔案名稱獲取對應圖標\r\n  getFileIcon(fileName: string): string {\r\n    const extension = fileName.split('.').pop()?.toLowerCase();\r\n    switch (extension) {\r\n      case 'pdf':\r\n        return 'assets/PDF.svg';\r\n      case 'dwg':\r\n      case 'dxf':\r\n      case 'dwf':\r\n        return 'assets/designFile.svg';\r\n      case 'jpg':\r\n      case 'jpeg':\r\n      case 'png':\r\n      case 'gif':\r\n      case 'bmp':\r\n      case 'webp':\r\n        return 'assets/imageFile.svg';\r\n      default:\r\n        return 'assets/PDF.svg';\r\n    }\r\n  }\r\n\r\n  // 根據檔案名稱獲取檔案類型顯示文字\r\n  getFileType(fileName: string): string {\r\n    const extension = fileName.split('.').pop()?.toLowerCase();\r\n    switch (extension) {\r\n      case 'pdf':\r\n        return 'PDF檔案';\r\n      case 'dwg':\r\n        return 'AutoCAD圖檔';\r\n      case 'dxf':\r\n        return 'CAD交換檔';\r\n      case 'dwf':\r\n        return 'CAD檢視檔';\r\n      case 'jpg':\r\n      case 'jpeg':\r\n        return 'JPEG圖片';\r\n      case 'png':\r\n        return 'PNG圖片';\r\n      case 'gif':\r\n        return 'GIF圖片';\r\n      case 'bmp':\r\n        return 'BMP圖片';\r\n      case 'webp':\r\n        return 'WebP圖片';\r\n      default:\r\n        return '未知格式';\r\n    }\r\n  }\r\n\r\n  // 根據檔案名稱獲取檔案類型簡短文字（用於下載按鈕）\r\n  getFileTypeText(fileName: string): string {\r\n    const extension = fileName.split('.').pop()?.toLowerCase();\r\n    switch (extension) {\r\n      case 'pdf':\r\n        return 'PDF';\r\n      case 'dwg':\r\n      case 'dxf':\r\n      case 'dwf':\r\n        return 'CAD';\r\n      case 'jpg':\r\n      case 'jpeg':\r\n      case 'png':\r\n      case 'gif':\r\n      case 'bmp':\r\n      case 'webp':\r\n        return '圖片';\r\n      default:\r\n        return '檔案';\r\n    }\r\n  }\r\n\r\n  // 根據檔案名稱獲取下載圖示\r\n  getDownloadIcon(fileName: string): string {\r\n    const extension = fileName.split('.').pop()?.toLowerCase();\r\n    switch (extension) {\r\n      case 'pdf':\r\n        return 'assets/PDF.svg';\r\n      case 'dwg':\r\n      case 'dxf':\r\n      case 'dwf':\r\n        return 'assets/designFile.svg';\r\n      case 'jpg':\r\n      case 'jpeg':\r\n      case 'png':\r\n      case 'gif':\r\n      case 'bmp':\r\n      case 'webp':\r\n        return 'assets/imageFile.svg';\r\n      default:\r\n        return 'assets/PDF.svg';\r\n    }\r\n  }\r\n\r\n  // 根據檔案名稱獲取下載提示文字\r\n  getDownloadTitle(fileName: string): string {\r\n    const extension = fileName.split('.').pop()?.toLowerCase();\r\n    switch (extension) {\r\n      case 'pdf':\r\n        return '下載 PDF 檔案';\r\n      case 'dwg':\r\n        return '下載 AutoCAD 圖檔';\r\n      case 'dxf':\r\n        return '下載 CAD 交換檔';\r\n      case 'dwf':\r\n        return '下載 CAD 檢視檔';\r\n      case 'jpg':\r\n      case 'jpeg':\r\n        return '下載 JPEG 圖片';\r\n      case 'png':\r\n        return '下載 PNG 圖片';\r\n      case 'gif':\r\n        return '下載 GIF 圖片';\r\n      case 'bmp':\r\n        return '下載 BMP 圖片';\r\n      case 'webp':\r\n        return '下載 WebP 圖片';\r\n      default:\r\n        return '下載檔案';\r\n    }\r\n  }\r\n\r\n  // 切換上傳區域顯示\r\n  toggleUploadArea() {\r\n    this.showUploadArea = !this.showUploadArea;\r\n    if (!this.showUploadArea) {\r\n      // 隱藏上傳區域時清除檔案和錯誤訊息\r\n      this.clearFiles();\r\n    }\r\n  }\r\n\r\n  // 計算總檔案數量\r\n  getTotalFilesCount(): number {\r\n    if (!this.listSpecialChange || !Array.isArray(this.listSpecialChange)) {\r\n      return 0;\r\n    }\r\n    return this.listSpecialChange.reduce((total, group) => {\r\n      return total + (group.SpecialChangeFiles ? group.SpecialChangeFiles.length : 0);\r\n    }, 0);\r\n  }\r\n\r\n  // 切換日期群組的收合狀態\r\n  toggleDateGroup(changeDate: string) {\r\n    this.collapsedStates[changeDate] = !this.collapsedStates[changeDate];\r\n  }\r\n\r\n  // 檢查日期群組是否已收合\r\n  isDateGroupCollapsed(changeDate: string): boolean {\r\n    return this.collapsedStates[changeDate] || false;\r\n  }\r\n\r\n  // 展開所有日期群組\r\n  expandAllGroups() {\r\n    Object.keys(this.collapsedStates).forEach(key => {\r\n      this.collapsedStates[key] = false;\r\n    });\r\n  }\r\n\r\n  // 收合所有日期群組\r\n  collapseAllGroups() {\r\n    Object.keys(this.collapsedStates).forEach(key => {\r\n      this.collapsedStates[key] = true;\r\n    });\r\n  }\r\n\r\n  // 根據來源代碼獲取來源名稱\r\n  getSourceName(source?: number): string {\r\n    switch (source) {\r\n      case 1:\r\n        return '系統';\r\n      case 2:\r\n        return '住戶';\r\n      case 3:\r\n        return '洽談';\r\n      default:\r\n        return '';\r\n    }\r\n  }\r\n\r\n  // 根據來源代碼獲取來源樣式類別\r\n  getSourceClass(source?: number): string {\r\n    switch (source) {\r\n      case 1:\r\n        return 'source-system';\r\n      case 2:\r\n        return 'source-resident';\r\n      case 3:\r\n        return 'source-negotiation';\r\n      default:\r\n        return 'source-unknown';\r\n    }\r\n  }\r\n\r\n  // 根據來源代碼獲取來源圖標\r\n  getSourceIcon(source?: number): string {\r\n    switch (source) {\r\n      case 1:\r\n        return 'pi-cog'; // 系統齒輪圖標\r\n      case 2:\r\n        return 'pi-home'; // 住戶房屋圖標\r\n      case 3:\r\n        return 'pi-comments'; // 洽談對話圖標\r\n      default:\r\n        return 'pi-question';\r\n    }\r\n  }\r\n\r\n  // 根據來源代碼獲取來源描述\r\n  getSourceDescription(source?: number): string {\r\n    switch (source) {\r\n      case 1:\r\n        return '由系統自動產生';\r\n      case 2:\r\n        return '住戶上傳提供';\r\n      case 3:\r\n        return '洽談過程中產生';\r\n      default:\r\n        return '來源不明';\r\n    }\r\n  }\r\n\r\n  // 獲取日期群組中各來源的檔案統計\r\n  getSourceStats(files: any[]): { [key: number]: number } {\r\n    const stats: { [key: number]: number } = {};\r\n    files.forEach(file => {\r\n      const source = file.CSource || 0;\r\n      stats[source] = (stats[source] || 0) + 1;\r\n    });\r\n    return stats;\r\n  }\r\n\r\n  // 檢查是否有多種來源\r\n  hasMultipleSources(files: any[]): boolean {\r\n    const sources = new Set(files.map(file => file.CSource).filter(source => source));\r\n    return sources.size > 1;\r\n  }\r\n}\r\n", "<div class=\"wrapper\">\r\n  <!-- Toast 元件用於顯示通知訊息 -->\r\n  <p-toast></p-toast>\r\n\r\n  <div class=\"content\">\r\n    <div class=\"flex justify-center\">\r\n      <div class=\"history\">\r\n        <div class=\"history-header\">\r\n          <div class=\"title\">洽談紀錄/檔案上傳</div>\r\n          <!-- 檔案上傳按鈕 - 右上角 -->\r\n          <div class=\"upload-button-container\">\r\n            <button class=\"button2 upload-button\" (click)=\"toggleUploadArea()\" [disabled]=\"isUploading\">\r\n              <i class=\"pi pi-upload\"></i>\r\n              <span *ngIf=\"!isUploading\">我要上傳</span>\r\n              <span *ngIf=\"isUploading\">處理中...</span>\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 上傳說明區塊 -->\r\n        <div class=\"upload-instructions\" *ngIf=\"showUploadArea && !uploadedFiles.length\">\r\n          <div class=\"upload-hint-text-header\">\r\n            <i class=\"pi pi-info-circle\"></i>\r\n            支援 PDF、DWG、DXF、DWF 格式，檔案大小限制 10MB\r\n          </div>\r\n          <div class=\"upload-features\">\r\n            <div class=\"feature-item\">\r\n              <i class=\"pi pi-check-circle\"></i>\r\n              <span>支援多檔案同時選擇</span>\r\n            </div>\r\n            <div class=\"feature-item\">\r\n              <i class=\"pi pi-check-circle\"></i>\r\n              <span>拖拽檔案到此處上傳</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 拖拽上傳區域 -->\r\n        <div class=\"drag-drop-zone\" *ngIf=\"showUploadArea && !uploadedFiles.length\" (drop)=\"onDragDrop($event)\"\r\n          (dragover)=\"onDragOver($event)\" (dragleave)=\"onDragLeave($event)\" [class.drag-over]=\"isDragOver\"\r\n          (click)=\"fileInput.click()\">\r\n          <div class=\"drag-drop-content\">\r\n            <i class=\"pi pi-cloud-upload drag-icon\"></i>\r\n            <div class=\"drag-text-primary\">拖拽檔案到此處</div>\r\n            <div class=\"drag-text-secondary\">或點擊選擇多個檔案</div>\r\n            <div class=\"supported-formats\">\r\n              <span class=\"format-badge\">PDF</span>\r\n              <span class=\"format-badge\">DWG</span>\r\n              <span class=\"format-badge\">DXF</span>\r\n              <span class=\"format-badge\">DWF</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 隱藏的檔案輸入元素 -->\r\n        <input #fileInput type=\"file\" class=\"file-input-hidden\" accept=\".pdf,.dwg,.dxf,.dwf\" multiple\r\n          (change)=\"onFileSelected($event)\" style=\"display: none;\">\r\n        <div class=\"card flex justify-content-center mb-3\"\r\n          *ngIf=\"houseHoldDetaiPicture.CFile && houseHoldDetaiPicture.CName\">\r\n          <p-panel header=\"標準圖面\" [toggleable]=\"true\" class=\"w-full\">\r\n            <ng-template pTemplate=\"headericons\">\r\n              <img class=\"black-arrow\" src=\"/assets/Vector.svg\" />\r\n            </ng-template>\r\n            <div class=\"pafbox\">\r\n              <span class=\"file-name\">{{houseHoldDetaiPicture.CName}}</span>\r\n              <div class=\"download-actions\">\r\n                <button class=\"download-btn\" (click)=\"downloadFileWithoutRedirect(houseHoldDetaiPicture)\"\r\n                  [title]=\"getDownloadTitle(houseHoldDetaiPicture.CName)\">\r\n                  <div class=\"btn-content\">\r\n                    <img [src]=\"getDownloadIcon(houseHoldDetaiPicture.CName)\"\r\n                      [alt]=\"getFileTypeText(houseHoldDetaiPicture.CName)\" class=\"file-type-icon\">\r\n                    <span class=\"download-text\">{{getFileTypeText(houseHoldDetaiPicture.CName)}}</span>\r\n                  </div>\r\n                  <i class=\"pi pi-download download-icon\"></i>\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </p-panel>\r\n        </div>\r\n\r\n        <div class=\"card flex justify-content-center w-full\" *ngIf=\"listSpecialChange && listSpecialChange.length > 0\">\r\n          <p-panel [toggleable]=\"false\" class=\"w-full files-panel\">\r\n            <ng-template pTemplate=\"header\">\r\n              <div class=\"panel-header-content\">\r\n                <div class=\"header-left\">\r\n                  <i class=\"pi pi-folder-open\"></i>\r\n                  <span class=\"panel-title\">檔案記錄</span>\r\n                  <span class=\"file-total-badge\" *ngIf=\"getTotalFilesCount() > 0\">共 {{getTotalFilesCount()}} 個檔案</span>\r\n                </div>\r\n                <div class=\"header-right\">\r\n                  <div class=\"control-buttons\">\r\n                    <button class=\"expand-btn\" (click)=\"expandAllGroups()\">\r\n                      展開全部\r\n                    </button>\r\n                    <button class=\"collapse-btn\" (click)=\"collapseAllGroups()\">\r\n                      收合全部\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </ng-template>\r\n            <div class=\"date-group\" *ngFor=\"let file of listSpecialChange\"\r\n              [class.collapsed]=\"isDateGroupCollapsed(file.CChangeDate)\">\r\n              <!-- 日期標題區塊 - 可點擊收合 -->\r\n              <div class=\"date-header clickable\" *ngIf=\"file.SpecialChangeFiles.length\"\r\n                (click)=\"toggleDateGroup(file.CChangeDate)\">\r\n                <div class=\"date-badge\">\r\n                  <i class=\"pi pi-calendar\"></i>\r\n                  <span class=\"date-text\">{{file.CChangeDate | dateFormat}}</span>\r\n                </div>\r\n                <div class=\"header-right\">\r\n                  <div class=\"file-count-indicator\">\r\n                    <span class=\"count\">{{file.SpecialChangeFiles.length}}</span>\r\n                    <span class=\"label\">個檔案</span>\r\n                  </div>\r\n                  <div class=\"collapse-indicator\">\r\n                    <i class=\"pi\" [class.pi-chevron-down]=\"!isDateGroupCollapsed(file.CChangeDate)\"\r\n                      [class.pi-chevron-up]=\"isDateGroupCollapsed(file.CChangeDate)\"></i>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- 檔案列表區塊 - 可收合 -->\r\n              <div class=\"files-container\" [class.collapsed]=\"isDateGroupCollapsed(file.CChangeDate)\"\r\n                [@slideInOut]=\"isDateGroupCollapsed(file.CChangeDate) ? 'out' : 'in'\">\r\n                <div class=\"file-item\" *ngFor=\"let item of file.SpecialChangeFiles; let i = index\">\r\n                  <div class=\"file-info\">\r\n                    <div class=\"file-index\">{{i + 1}}</div>\r\n                    <span class=\"file-name\">{{item.CFileName}}</span>\r\n                    <!-- 來源標籤 -->\r\n                    <div class=\"file-source\" *ngIf=\"getSourceName(item.CSource)\">\r\n                      <span class=\"source-badge\" [class]=\"getSourceClass(item.CSource)\"\r\n                        [title]=\"getSourceDescription(item.CSource)\">\r\n                        <i class=\"pi\" [class]=\"getSourceIcon(item.CSource)\"></i>\r\n                        {{getSourceName(item.CSource)}}\r\n                      </span>\r\n                    </div>\r\n                  </div>\r\n                  <div class=\"download-actions\" *ngIf=\"item.CFile\">\r\n                    <button class=\"download-btn\" (click)=\"downloadFileWithoutRedirect({\r\n                              CFile: item.CFile || '',\r\n                              CName: item.CFileName || ''\r\n                            })\" [title]=\"getDownloadTitle(item.CFileName)\">\r\n                      <div class=\"btn-content\">\r\n                        <img [src]=\"getDownloadIcon(item.CFileName)\" [alt]=\"getFileTypeText(item.CFileName)\"\r\n                          class=\"file-type-icon\">\r\n                        <span class=\"download-text\">{{getFileTypeText(item.CFileName)}}</span>\r\n                      </div>\r\n                      <i class=\"pi pi-download download-icon\"></i>\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </p-panel>\r\n        </div>\r\n\r\n        <!-- 上傳檔案列表 (當有檔案待上傳時顯示) -->\r\n        <div class=\"card flex justify-content-center w-full mb-3\" *ngIf=\"showUploadArea && uploadedFiles.length > 0\">\r\n          <p-panel [toggleable]=\"false\" class=\"w-full upload-panel\">\r\n            <ng-template pTemplate=\"header\">\r\n              <div class=\"upload-panel-header\">\r\n                <div class=\"upload-panel-title\">\r\n                  <i class=\"pi pi-cloud-upload\"></i>\r\n                  <span>待上傳檔案</span>\r\n                  <div class=\"file-count-badge\">{{uploadedFiles.length}}</div>\r\n                </div>\r\n                <div class=\"upload-panel-subtitle\">\r\n                  請確認以下檔案後點擊上傳\r\n                </div>\r\n              </div>\r\n            </ng-template>\r\n\r\n            <div class=\"uploaded-files-container\">\r\n              <div class=\"uploaded-files\">\r\n                <div class=\"uploaded-file-item\" *ngFor=\"let file of uploadedFiles; let i = index\">\r\n                  <div class=\"file-status-indicator\">\r\n                    <i class=\"pi pi-clock\" title=\"等待上傳\"></i>\r\n                  </div>\r\n                  <div class=\"file-info\">\r\n                    <div class=\"file-icon-wrapper\">\r\n                      <img [src]=\"getFileIcon(file.name)\" alt=\"File\" class=\"file-icon\">\r\n                    </div>\r\n                    <div class=\"file-details\">\r\n                      <span class=\"file-name\" [title]=\"file.name\">{{file.name}}</span>\r\n                      <div class=\"file-meta\">\r\n                        <span class=\"file-size\">{{formatFileSize(file.size)}}</span>\r\n                        <span class=\"file-type\">{{getFileType(file.name)}}</span>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                  <div class=\"file-actions\">\r\n                    <button class=\"delete-btn\" (click)=\"removeFile(i)\" title=\"移除檔案\">\r\n                      <i class=\"pi pi-times\"></i>\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 確認上傳按鈕 -->\r\n            <div class=\"upload-actions\">\r\n              <button class=\"button1 confirm-upload-btn\" [disabled]=\"isUploading\" (click)=\"uploadFiles()\">\r\n                <i class=\"pi pi-check\"></i>\r\n                <span *ngIf=\"!isUploading\">確認上傳</span>\r\n                <span *ngIf=\"isUploading\">上傳中...</span>\r\n              </button>\r\n              <button class=\"button2 cancel-btn\" (click)=\"clearFiles()\" [disabled]=\"isUploading\">\r\n                <i class=\"pi pi-times\"></i>\r\n                取消\r\n              </button>\r\n            </div>\r\n          </p-panel>\r\n        </div>\r\n\r\n        <!-- 錯誤訊息 -->\r\n        <div class=\"error-message-container\" *ngIf=\"uploadError\">\r\n          <div class=\"error-message\">\r\n            <i class=\"pi pi-exclamation-triangle\"></i>\r\n            {{uploadError}}\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"flex justify-center\">\r\n          <div class=\"my-4 h-32\"> <button class=\"button1 !w-48\"><a routerLink=\"/\">返回主選單</a></button></div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAG9C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAAmBC,cAAc,QAAQ,aAAa;AACtD,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,WAAW,QAAQ,eAAe;AAG3C,SAASC,cAAc,QAAQ,qCAAqC;AACpE,SAASC,mBAAmB,QAAQ,6CAA6C;AACjF,SAASC,WAAW,QAAQ,gCAAgC;AAE5D,SAASC,YAAY,QAAQ,uCAAuC;AAEpE,SAASC,OAAO,EAAEC,KAAK,EAAEC,KAAK,EAAEC,UAAU,EAAEC,OAAO,QAAQ,qBAAqB;;;;;;;;;;;;;ICHlEC,EAAA,CAAAC,cAAA,WAA2B;IAAAD,EAAA,CAAAE,MAAA,+BAAI;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACtCH,EAAA,CAAAC,cAAA,WAA0B;IAAAD,EAAA,CAAAE,MAAA,4BAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAO3CH,EADF,CAAAC,cAAA,cAAiF,cAC1C;IACnCD,EAAA,CAAAI,SAAA,YAAiC;IACjCJ,EAAA,CAAAE,MAAA,gHACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,cAA6B,cACD;IACxBD,EAAA,CAAAI,SAAA,YAAkC;IAClCJ,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,6DAAS;IACjBF,EADiB,CAAAG,YAAA,EAAO,EAClB;IACNH,EAAA,CAAAC,cAAA,cAA0B;IACxBD,EAAA,CAAAI,SAAA,aAAkC;IAClCJ,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,8DAAS;IAGrBF,EAHqB,CAAAG,YAAA,EAAO,EAClB,EACF,EACF;;;;;;IAGNH,EAAA,CAAAC,cAAA,cAE8B;IAA5BD,EAF0E,CAAAK,UAAA,kBAAAC,qDAAAC,MAAA;MAAAP,EAAA,CAAAQ,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAAQF,MAAA,CAAAG,UAAA,CAAAN,MAAA,CAAkB;IAAA,EAAC,sBAAAO,yDAAAP,MAAA;MAAAP,EAAA,CAAAQ,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CACzFF,MAAA,CAAAK,UAAA,CAAAR,MAAA,CAAkB;IAAA,EAAC,uBAAAS,0DAAAT,MAAA;MAAAP,EAAA,CAAAQ,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAAcF,MAAA,CAAAO,WAAA,CAAAV,MAAA,CAAmB;IAAA,EAAC,mBAAAW,sDAAA;MAAAlB,EAAA,CAAAQ,aAAA,CAAAC,GAAA;MAAAT,EAAA,CAAAW,aAAA;MAAA,MAAAQ,YAAA,GAAAnB,EAAA,CAAAoB,WAAA;MAAA,OAAApB,EAAA,CAAAY,WAAA,CACxDO,YAAA,CAAAE,KAAA,EAAiB;IAAA,EAAC;IAC3BrB,EAAA,CAAAC,cAAA,cAA+B;IAC7BD,EAAA,CAAAI,SAAA,YAA4C;IAC5CJ,EAAA,CAAAC,cAAA,cAA+B;IAAAD,EAAA,CAAAE,MAAA,iDAAO;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC5CH,EAAA,CAAAC,cAAA,cAAiC;IAAAD,EAAA,CAAAE,MAAA,6DAAS;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAE9CH,EADF,CAAAC,cAAA,cAA+B,eACF;IAAAD,EAAA,CAAAE,MAAA,UAAG;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACrCH,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,WAAG;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACrCH,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,WAAG;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACrCH,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,WAAG;IAGpCF,EAHoC,CAAAG,YAAA,EAAO,EACjC,EACF,EACF;;;;IAb8DH,EAAA,CAAAsB,WAAA,cAAAZ,MAAA,CAAAa,UAAA,CAA8B;;;;;IAsB5FvB,EAAA,CAAAI,SAAA,cAAoD;;;;;;IAFxDJ,EAFF,CAAAC,cAAA,cACqE,kBACT;IACxDD,EAAA,CAAAwB,UAAA,IAAAC,8CAAA,0BAAqC;IAInCzB,EADF,CAAAC,cAAA,cAAoB,eACM;IAAAD,EAAA,CAAAE,MAAA,GAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAE5DH,EADF,CAAAC,cAAA,cAA8B,iBAE8B;IAD7BD,EAAA,CAAAK,UAAA,mBAAAqB,yDAAA;MAAA1B,EAAA,CAAAQ,aAAA,CAAAmB,GAAA;MAAA,MAAAjB,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAkB,2BAAA,CAAAlB,MAAA,CAAAmB,qBAAA,CAAkD;IAAA,EAAC;IAEvF7B,EAAA,CAAAC,cAAA,cAAyB;IACvBD,EAAA,CAAAI,SAAA,cAC8E;IAC9EJ,EAAA,CAAAC,cAAA,gBAA4B;IAAAD,EAAA,CAAAE,MAAA,IAAgD;IAC9EF,EAD8E,CAAAG,YAAA,EAAO,EAC/E;IACNH,EAAA,CAAAI,SAAA,aAA4C;IAKtDJ,EAJQ,CAAAG,YAAA,EAAS,EACL,EACF,EACE,EACN;;;;IAnBmBH,EAAA,CAAA8B,SAAA,EAAmB;IAAnB9B,EAAA,CAAA+B,UAAA,oBAAmB;IAKd/B,EAAA,CAAA8B,SAAA,GAA+B;IAA/B9B,EAAA,CAAAgC,iBAAA,CAAAtB,MAAA,CAAAmB,qBAAA,CAAAI,KAAA,CAA+B;IAGnDjC,EAAA,CAAA8B,SAAA,GAAuD;IAAvD9B,EAAA,CAAA+B,UAAA,UAAArB,MAAA,CAAAwB,gBAAA,CAAAxB,MAAA,CAAAmB,qBAAA,CAAAI,KAAA,EAAuD;IAEhDjC,EAAA,CAAA8B,SAAA,GAAoD;IACvD9B,EADG,CAAA+B,UAAA,QAAArB,MAAA,CAAAyB,eAAA,CAAAzB,MAAA,CAAAmB,qBAAA,CAAAI,KAAA,GAAAjC,EAAA,CAAAoC,aAAA,CAAoD,QAAA1B,MAAA,CAAA2B,eAAA,CAAA3B,MAAA,CAAAmB,qBAAA,CAAAI,KAAA,EACH;IAC1BjC,EAAA,CAAA8B,SAAA,GAAgD;IAAhD9B,EAAA,CAAAgC,iBAAA,CAAAtB,MAAA,CAAA2B,eAAA,CAAA3B,MAAA,CAAAmB,qBAAA,CAAAI,KAAA,EAAgD;;;;;IAgB9EjC,EAAA,CAAAC,cAAA,eAAgE;IAAAD,EAAA,CAAAE,MAAA,GAA8B;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAArCH,EAAA,CAAA8B,SAAA,EAA8B;IAA9B9B,EAAA,CAAAsC,kBAAA,YAAA5B,MAAA,CAAA6B,kBAAA,0BAA8B;;;;;;IAHhGvC,EADF,CAAAC,cAAA,cAAkC,cACP;IACvBD,EAAA,CAAAI,SAAA,YAAiC;IACjCJ,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAE,MAAA,+BAAI;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACrCH,EAAA,CAAAwB,UAAA,IAAAgB,qDAAA,mBAAgE;IAClExC,EAAA,CAAAG,YAAA,EAAM;IAGFH,EAFJ,CAAAC,cAAA,cAA0B,cACK,iBAC4B;IAA5BD,EAAA,CAAAK,UAAA,mBAAAoC,uEAAA;MAAAzC,EAAA,CAAAQ,aAAA,CAAAkC,GAAA;MAAA,MAAAhC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAiC,eAAA,EAAiB;IAAA,EAAC;IACpD3C,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAA2D;IAA9BD,EAAA,CAAAK,UAAA,mBAAAuC,wEAAA;MAAA5C,EAAA,CAAAQ,aAAA,CAAAkC,GAAA;MAAA,MAAAhC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAmC,iBAAA,EAAmB;IAAA,EAAC;IACxD7C,EAAA,CAAAE,MAAA,kCACF;IAGNF,EAHM,CAAAG,YAAA,EAAS,EACL,EACF,EACF;;;;IAZ8BH,EAAA,CAAA8B,SAAA,GAA8B;IAA9B9B,EAAA,CAAA+B,UAAA,SAAArB,MAAA,CAAA6B,kBAAA,OAA8B;;;;;;IAiBlEvC,EAAA,CAAAC,cAAA,cAC8C;IAA5CD,EAAA,CAAAK,UAAA,mBAAAyC,kEAAA;MAAA9C,EAAA,CAAAQ,aAAA,CAAAuC,GAAA;MAAA,MAAAC,OAAA,GAAAhD,EAAA,CAAAW,aAAA,GAAAsC,SAAA;MAAA,MAAAvC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAwC,eAAA,CAAAF,OAAA,CAAAG,WAAA,CAAiC;IAAA,EAAC;IAC3CnD,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAAI,SAAA,YAA8B;IAC9BJ,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAE,MAAA,GAAiC;;IAC3DF,EAD2D,CAAAG,YAAA,EAAO,EAC5D;IAGFH,EAFJ,CAAAC,cAAA,cAA0B,cACU,eACZ;IAAAD,EAAA,CAAAE,MAAA,GAAkC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC7DH,EAAA,CAAAC,cAAA,gBAAoB;IAAAD,EAAA,CAAAE,MAAA,0BAAG;IACzBF,EADyB,CAAAG,YAAA,EAAO,EAC1B;IACNH,EAAA,CAAAC,cAAA,eAAgC;IAC9BD,EAAA,CAAAI,SAAA,aACqE;IAG3EJ,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;;;IAZsBH,EAAA,CAAA8B,SAAA,GAAiC;IAAjC9B,EAAA,CAAAgC,iBAAA,CAAAhC,EAAA,CAAAoD,WAAA,OAAAJ,OAAA,CAAAG,WAAA,EAAiC;IAInCnD,EAAA,CAAA8B,SAAA,GAAkC;IAAlC9B,EAAA,CAAAgC,iBAAA,CAAAgB,OAAA,CAAAK,kBAAA,CAAAC,MAAA,CAAkC;IAIxCtD,EAAA,CAAA8B,SAAA,GAAiE;IAC7E9B,EADY,CAAAsB,WAAA,qBAAAZ,MAAA,CAAA6C,oBAAA,CAAAP,OAAA,CAAAG,WAAA,EAAiE,kBAAAzC,MAAA,CAAA6C,oBAAA,CAAAP,OAAA,CAAAG,WAAA,EACf;;;;;IAc9DnD,EADF,CAAAC,cAAA,cAA6D,eAEZ;IAC7CD,EAAA,CAAAI,SAAA,YAAwD;IACxDJ,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAO,EACH;;;;;IALuBH,EAAA,CAAA8B,SAAA,EAAsC;IAAtC9B,EAAA,CAAAwD,UAAA,CAAA9C,MAAA,CAAA+C,cAAA,CAAAC,OAAA,CAAAC,OAAA,EAAsC;IAC/D3D,EAAA,CAAA+B,UAAA,UAAArB,MAAA,CAAAkD,oBAAA,CAAAF,OAAA,CAAAC,OAAA,EAA4C;IAC9B3D,EAAA,CAAA8B,SAAA,EAAqC;IAArC9B,EAAA,CAAAwD,UAAA,CAAA9C,MAAA,CAAAmD,aAAA,CAAAH,OAAA,CAAAC,OAAA,EAAqC;IACnD3D,EAAA,CAAA8B,SAAA,EACF;IADE9B,EAAA,CAAAsC,kBAAA,MAAA5B,MAAA,CAAAoD,aAAA,CAAAJ,OAAA,CAAAC,OAAA,OACF;;;;;;IAIF3D,EADF,CAAAC,cAAA,cAAiD,iBAIQ;IAH1BD,EAAA,CAAAK,UAAA,mBAAA0D,2EAAA;MAAA/D,EAAA,CAAAQ,aAAA,CAAAwD,IAAA;MAAA,MAAAN,OAAA,GAAA1D,EAAA,CAAAW,aAAA,GAAAsC,SAAA;MAAA,MAAAvC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAkB,2BAAA,CAA4B;QAAAqC,KAAA,EAAAP,OAAA,CAAAO,KAAA,IACpC,EAAE;QAAAhC,KAAA,EAAAyB,OAAA,CAAAQ,SAAA,IACC;MAAE,CAC7B,CAAC;IAAA,EAAI;IACTlE,EAAA,CAAAC,cAAA,cAAyB;IACvBD,EAAA,CAAAI,SAAA,cACyB;IACzBJ,EAAA,CAAAC,cAAA,eAA4B;IAAAD,EAAA,CAAAE,MAAA,GAAmC;IACjEF,EADiE,CAAAG,YAAA,EAAO,EAClE;IACNH,EAAA,CAAAI,SAAA,YAA4C;IAEhDJ,EADE,CAAAG,YAAA,EAAS,EACL;;;;;IARQH,EAAA,CAAA8B,SAAA,EAA0C;IAA1C9B,EAAA,CAAA+B,UAAA,UAAArB,MAAA,CAAAwB,gBAAA,CAAAwB,OAAA,CAAAQ,SAAA,EAA0C;IAE7ClE,EAAA,CAAA8B,SAAA,GAAuC;IAAC9B,EAAxC,CAAA+B,UAAA,QAAArB,MAAA,CAAAyB,eAAA,CAAAuB,OAAA,CAAAQ,SAAA,GAAAlE,EAAA,CAAAoC,aAAA,CAAuC,QAAA1B,MAAA,CAAA2B,eAAA,CAAAqB,OAAA,CAAAQ,SAAA,EAAwC;IAExDlE,EAAA,CAAA8B,SAAA,GAAmC;IAAnC9B,EAAA,CAAAgC,iBAAA,CAAAtB,MAAA,CAAA2B,eAAA,CAAAqB,OAAA,CAAAQ,SAAA,EAAmC;;;;;IAnBnElE,EAFJ,CAAAC,cAAA,cAAmF,cAC1D,cACG;IAAAD,EAAA,CAAAE,MAAA,GAAS;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACvCH,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEjDH,EAAA,CAAAwB,UAAA,IAAA2C,kDAAA,kBAA6D;IAO/DnE,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAwB,UAAA,IAAA4C,kDAAA,kBAAiD;IAanDpE,EAAA,CAAAG,YAAA,EAAM;;;;;;IAxBsBH,EAAA,CAAA8B,SAAA,GAAS;IAAT9B,EAAA,CAAAgC,iBAAA,CAAAqC,KAAA,KAAS;IACTrE,EAAA,CAAA8B,SAAA,GAAkB;IAAlB9B,EAAA,CAAAgC,iBAAA,CAAA0B,OAAA,CAAAQ,SAAA,CAAkB;IAEhBlE,EAAA,CAAA8B,SAAA,EAAiC;IAAjC9B,EAAA,CAAA+B,UAAA,SAAArB,MAAA,CAAAoD,aAAA,CAAAJ,OAAA,CAAAC,OAAA,EAAiC;IAQ9B3D,EAAA,CAAA8B,SAAA,EAAgB;IAAhB9B,EAAA,CAAA+B,UAAA,SAAA2B,OAAA,CAAAO,KAAA,CAAgB;;;;;IArCrDjE,EAAA,CAAAC,cAAA,cAC6D;IAE3DD,EAAA,CAAAwB,UAAA,IAAA8C,4CAAA,mBAC8C;IAkB9CtE,EAAA,CAAAC,cAAA,cACwE;IACtED,EAAA,CAAAwB,UAAA,IAAA+C,4CAAA,kBAAmF;IA4BvFvE,EADE,CAAAG,YAAA,EAAM,EACF;;;;;IAnDJH,EAAA,CAAAsB,WAAA,cAAAZ,MAAA,CAAA6C,oBAAA,CAAAP,OAAA,CAAAG,WAAA,EAA0D;IAEtBnD,EAAA,CAAA8B,SAAA,EAAoC;IAApC9B,EAAA,CAAA+B,UAAA,SAAAiB,OAAA,CAAAK,kBAAA,CAAAC,MAAA,CAAoC;IAmB3CtD,EAAA,CAAA8B,SAAA,EAA0D;IAA1D9B,EAAA,CAAAsB,WAAA,cAAAZ,MAAA,CAAA6C,oBAAA,CAAAP,OAAA,CAAAG,WAAA,EAA0D;IACrFnD,EAAA,CAAA+B,UAAA,gBAAArB,MAAA,CAAA6C,oBAAA,CAAAP,OAAA,CAAAG,WAAA,iBAAqE;IAC7BnD,EAAA,CAAA8B,SAAA,EAA4B;IAA5B9B,EAAA,CAAA+B,UAAA,YAAAiB,OAAA,CAAAK,kBAAA,CAA4B;;;;;IA5C1ErD,EADF,CAAAC,cAAA,cAA+G,kBACpD;IAoBvDD,EAnBA,CAAAwB,UAAA,IAAAgD,8CAAA,2BAAgC,IAAAC,sCAAA,kBAoB6B;IAqDjEzE,EADE,CAAAG,YAAA,EAAU,EACN;;;;IA1EKH,EAAA,CAAA8B,SAAA,EAAoB;IAApB9B,EAAA,CAAA+B,UAAA,qBAAoB;IAoBc/B,EAAA,CAAA8B,SAAA,GAAoB;IAApB9B,EAAA,CAAA+B,UAAA,YAAArB,MAAA,CAAAgE,iBAAA,CAAoB;;;;;IA6DzD1E,EADF,CAAAC,cAAA,cAAiC,cACC;IAC9BD,EAAA,CAAAI,SAAA,YAAkC;IAClCJ,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,qCAAK;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAClBH,EAAA,CAAAC,cAAA,cAA8B;IAAAD,EAAA,CAAAE,MAAA,GAAwB;IACxDF,EADwD,CAAAG,YAAA,EAAM,EACxD;IACNH,EAAA,CAAAC,cAAA,cAAmC;IACjCD,EAAA,CAAAE,MAAA,iFACF;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAL4BH,EAAA,CAAA8B,SAAA,GAAwB;IAAxB9B,EAAA,CAAAgC,iBAAA,CAAAtB,MAAA,CAAAiE,aAAA,CAAArB,MAAA,CAAwB;;;;;;IAWtDtD,EADF,CAAAC,cAAA,cAAkF,cAC7C;IACjCD,EAAA,CAAAI,SAAA,YAAwC;IAC1CJ,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,cAAuB,cACU;IAC7BD,EAAA,CAAAI,SAAA,cAAiE;IACnEJ,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,eAA0B,gBACoB;IAAAD,EAAA,CAAAE,MAAA,GAAa;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAE9DH,EADF,CAAAC,cAAA,eAAuB,iBACG;IAAAD,EAAA,CAAAE,MAAA,IAA6B;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC5DH,EAAA,CAAAC,cAAA,iBAAwB;IAAAD,EAAA,CAAAE,MAAA,IAA0B;IAGxDF,EAHwD,CAAAG,YAAA,EAAO,EACrD,EACF,EACF;IAEJH,EADF,CAAAC,cAAA,gBAA0B,mBACwC;IAArCD,EAAA,CAAAK,UAAA,mBAAAuE,gEAAA;MAAA,MAAAC,KAAA,GAAA7E,EAAA,CAAAQ,aAAA,CAAAsE,IAAA,EAAAC,KAAA;MAAA,MAAArE,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAsE,UAAA,CAAAH,KAAA,CAAa;IAAA,EAAC;IAChD7E,EAAA,CAAAI,SAAA,aAA2B;IAGjCJ,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;;;;;IAfKH,EAAA,CAAA8B,SAAA,GAA8B;IAA9B9B,EAAA,CAAA+B,UAAA,QAAArB,MAAA,CAAAuE,WAAA,CAAAC,QAAA,CAAAC,IAAA,GAAAnF,EAAA,CAAAoC,aAAA,CAA8B;IAGXpC,EAAA,CAAA8B,SAAA,GAAmB;IAAnB9B,EAAA,CAAA+B,UAAA,UAAAmD,QAAA,CAAAC,IAAA,CAAmB;IAACnF,EAAA,CAAA8B,SAAA,EAAa;IAAb9B,EAAA,CAAAgC,iBAAA,CAAAkD,QAAA,CAAAC,IAAA,CAAa;IAE/BnF,EAAA,CAAA8B,SAAA,GAA6B;IAA7B9B,EAAA,CAAAgC,iBAAA,CAAAtB,MAAA,CAAA0E,cAAA,CAAAF,QAAA,CAAAG,IAAA,EAA6B;IAC7BrF,EAAA,CAAA8B,SAAA,GAA0B;IAA1B9B,EAAA,CAAAgC,iBAAA,CAAAtB,MAAA,CAAA4E,WAAA,CAAAJ,QAAA,CAAAC,IAAA,EAA0B;;;;;IAiB1DnF,EAAA,CAAAC,cAAA,WAA2B;IAAAD,EAAA,CAAAE,MAAA,+BAAI;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACtCH,EAAA,CAAAC,cAAA,WAA0B;IAAAD,EAAA,CAAAE,MAAA,4BAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;IA9C7CH,EADF,CAAAC,cAAA,cAA6G,kBACjD;IACxDD,EAAA,CAAAwB,UAAA,IAAA+D,8CAAA,0BAAgC;IAc9BvF,EADF,CAAAC,cAAA,cAAsC,cACR;IAC1BD,EAAA,CAAAwB,UAAA,IAAAgE,sCAAA,mBAAkF;IAuBtFxF,EADE,CAAAG,YAAA,EAAM,EACF;IAIJH,EADF,CAAAC,cAAA,cAA4B,iBACkE;IAAxBD,EAAA,CAAAK,UAAA,mBAAAoF,yDAAA;MAAAzF,EAAA,CAAAQ,aAAA,CAAAkF,IAAA;MAAA,MAAAhF,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAiF,WAAA,EAAa;IAAA,EAAC;IACzF3F,EAAA,CAAAI,SAAA,YAA2B;IAE3BJ,EADA,CAAAwB,UAAA,IAAAoE,uCAAA,mBAA2B,KAAAC,wCAAA,mBACD;IAC5B7F,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAAmF;IAAhDD,EAAA,CAAAK,UAAA,mBAAAyF,0DAAA;MAAA9F,EAAA,CAAAQ,aAAA,CAAAkF,IAAA;MAAA,MAAAhF,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAqF,UAAA,EAAY;IAAA,EAAC;IACvD/F,EAAA,CAAAI,SAAA,aAA2B;IAC3BJ,EAAA,CAAAE,MAAA,sBACF;IAGNF,EAHM,CAAAG,YAAA,EAAS,EACL,EACE,EACN;;;;IAtDKH,EAAA,CAAA8B,SAAA,EAAoB;IAApB9B,EAAA,CAAA+B,UAAA,qBAAoB;IAgB0B/B,EAAA,CAAA8B,SAAA,GAAkB;IAAlB9B,EAAA,CAAA+B,UAAA,YAAArB,MAAA,CAAAiE,aAAA,CAAkB;IA2B1B3E,EAAA,CAAA8B,SAAA,GAAwB;IAAxB9B,EAAA,CAAA+B,UAAA,aAAArB,MAAA,CAAAsF,WAAA,CAAwB;IAE1DhG,EAAA,CAAA8B,SAAA,GAAkB;IAAlB9B,EAAA,CAAA+B,UAAA,UAAArB,MAAA,CAAAsF,WAAA,CAAkB;IAClBhG,EAAA,CAAA8B,SAAA,EAAiB;IAAjB9B,EAAA,CAAA+B,UAAA,SAAArB,MAAA,CAAAsF,WAAA,CAAiB;IAEgChG,EAAA,CAAA8B,SAAA,EAAwB;IAAxB9B,EAAA,CAAA+B,UAAA,aAAArB,MAAA,CAAAsF,WAAA,CAAwB;;;;;IAUtFhG,EADF,CAAAC,cAAA,eAAyD,eAC5B;IACzBD,EAAA,CAAAI,SAAA,aAA0C;IAC1CJ,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAFFH,EAAA,CAAA8B,SAAA,GACF;IADE9B,EAAA,CAAAsC,kBAAA,MAAA5B,MAAA,CAAAuF,WAAA,MACF;;;ADhLV,OAAM,MAAOC,gBAAgB;EAkB3BC,YACUC,aAA2B,EAC3BC,qBAA2C,EAC3CC,eAA+B,EAC/BC,aAA2B,EAC3BC,YAAyB;IAJzB,KAAAJ,aAAa,GAAbA,aAAa;IACb,KAAAC,qBAAqB,GAArBA,qBAAqB;IACrB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,YAAY,GAAZA,YAAY;IAtBtB,KAAAC,KAAK,GAAe,EAAE;IACtB,KAAA5E,qBAAqB,GAAiD;MAAEoC,KAAK,EAAE,EAAE;MAAEhC,KAAK,EAAE;IAAE,CAAE;IAC9F,KAAAyE,kBAAkB,GAA6B,EAAE;IACjD,KAAAC,QAAQ,GAAGnH,mBAAmB,CAACoH,eAAe,CAACnH,WAAW,CAACoH,UAAU,CAAQ;IAI7E;IACA,KAAAlC,aAAa,GAAW,EAAE;IAC1B,KAAAqB,WAAW,GAAY,KAAK;IAC5B,KAAAC,WAAW,GAAW,EAAE;IACxB,KAAAa,WAAW,GAAW,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;IACxC,KAAAC,gBAAgB,GAAa,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;IAC7D,KAAAC,gBAAgB,GAAa,CAAC,iBAAiB,EAAE,eAAe,EAAE,kBAAkB,EAAE,oBAAoB,CAAC;IAC3G,KAAAzF,UAAU,GAAY,KAAK;IAC3B,KAAA0F,cAAc,GAAY,KAAK,CAAC,CAAC;IAmBjC;IACA,KAAAC,eAAe,GAA+B,EAAE;EAXhD;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,WAAW,GAAG,IAAI,CAACV,QAAQ,CAACU,WAAW;IAC5C,IAAI,CAACC,YAAY,GAAG,IAAI,CAACX,QAAQ,CAACW,YAAY;IAC9C,IAAI,CAACC,oBAAoB,EAAE;EAC7B;EAMAA,oBAAoBA,CAAA;IAClB,IAAI,CAAClB,qBAAqB,CAACmB,6CAA6C,CAAC;MAAEC,IAAI,EAAE,IAAI,CAACJ;IAAW,CAAE,CAAC,CAACK,SAAS,CAACC,GAAG,IAAG;MACnH,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACnD,iBAAiB,GAAGiD,GAAG,CAACC,OAAQ,IAAI,EAAE;QAC3C;QACA,IAAI,CAACE,yBAAyB,EAAE;;IAEpC,CAAC,CAAC;EACJ;EAEA;EACQA,yBAAyBA,CAAA;IAC/B,IAAI,IAAI,CAACpD,iBAAiB,IAAIqD,KAAK,CAACC,OAAO,CAAC,IAAI,CAACtD,iBAAiB,CAAC,EAAE;MACnE,IAAI,CAACA,iBAAiB,CAACuD,OAAO,CAAC,CAACC,KAAK,EAAEnD,KAAK,KAAI;QAC9C;QACA,IAAI,CAACmC,eAAe,CAACgB,KAAK,CAAC/E,WAAW,CAAC,GAAG4B,KAAK,IAAI,CAAC;MACtD,CAAC,CAAC;;EAEN;EAEAqC,oBAAoBA,CAAA;IAClB,IAAI,CAAChB,aAAa,CAAC+B,uCAAuC,CAAC,EAAE,CAAC,CAACT,SAAS,CAACC,GAAG,IAAG;MAC7E,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAAChG,qBAAqB,GAAG;UAC3BoC,KAAK,EAAE0D,GAAG,CAACC,OAAO,CAACQ,QAAQ,GAAGT,GAAG,CAACC,OAAO,CAACQ,QAAQ,GAAG,EAAE;UACvDnG,KAAK,EAAE0F,GAAG,CAACC,OAAO,CAAC1D;SACpB;;IAEL,CAAC,CAAC;EACJ;EAEAmE,kBAAkBA,CAACC,GAAW;IAC5B,MAAMC,KAAK,GAAGD,GAAG,CAACE,KAAK,CAAC,GAAG,CAAC;IAC5B,MAAMC,QAAQ,GAAGF,KAAK,CAACG,GAAG,EAAE;IAC5B,OAAOD,QAAQ;EACjB;EAEA7G,2BAA2BA,CAAC+G,KAAmD;IAC7E,IAAIA,KAAK,CAAC1E,KAAK,IAAI0E,KAAK,CAAC1G,KAAK,EAAE;MAC9B,IAAI,CAACuE,YAAY,CAACoC,OAAO,CAACD,KAAK,CAAC1E,KAAK,EAAE0E,KAAK,CAAC1G,KAAK,CAAC,CAACyF,SAAS,CAAC;QAC5DmB,IAAI,EAAGC,QAA4B,IAAI;UACrC,IAAIA,QAAQ,CAACrB,IAAI,EAAE;YACjB;YACA,MAAMa,GAAG,GAAGS,MAAM,CAACC,GAAG,CAACC,eAAe,CAACH,QAAQ,CAACrB,IAAI,CAAC;YACrD,MAAMyB,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;YACxCF,IAAI,CAACG,IAAI,GAAGf,GAAG;YACfY,IAAI,CAACI,QAAQ,GAAGX,KAAK,CAAC1G,KAAK,IAAI,UAAU;YACzCkH,QAAQ,CAAC1B,IAAI,CAAC8B,WAAW,CAACL,IAAI,CAAC;YAC/BA,IAAI,CAAC7H,KAAK,EAAE;YACZ8H,QAAQ,CAAC1B,IAAI,CAAC+B,WAAW,CAACN,IAAI,CAAC;YAC/BH,MAAM,CAACC,GAAG,CAACS,eAAe,CAACnB,GAAG,CAAC;;QAEnC,CAAC;QACDoB,KAAK,EAAGA,KAAK,IAAI;UACfC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;UAC/B,IAAI,CAACnD,aAAa,CAACqD,YAAY,CAAC,QAAQ,CAAC;UACzC;UACAb,MAAM,CAACc,IAAI,CAAClB,KAAK,CAAC1E,KAAK,EAAE,QAAQ,CAAC;QACpC;OACD,CAAC;;EAEN;EAEA;EACA6F,cAAcA,CAACC,KAAY;IACzB,MAAMC,KAAK,GAAGD,KAAK,CAACE,MAA0B;IAC9C,IAAID,KAAK,CAACrB,KAAK,EAAE;MACf,IAAI,CAACuB,WAAW,CAACnC,KAAK,CAACoC,IAAI,CAACH,KAAK,CAACrB,KAAK,CAAC,CAAC;;EAE7C;EAEA;EACA5H,UAAUA,CAACgJ,KAAgB;IACzBA,KAAK,CAACK,cAAc,EAAE;IACtBL,KAAK,CAACM,eAAe,EAAE;IACvB,IAAI,CAAC9I,UAAU,GAAG,IAAI;EACxB;EAEAN,WAAWA,CAAC8I,KAAgB;IAC1BA,KAAK,CAACK,cAAc,EAAE;IACtBL,KAAK,CAACM,eAAe,EAAE;IACvB,IAAI,CAAC9I,UAAU,GAAG,KAAK;EACzB;EAEAV,UAAUA,CAACkJ,KAAgB;IACzBA,KAAK,CAACK,cAAc,EAAE;IACtBL,KAAK,CAACM,eAAe,EAAE;IACvB,IAAI,CAAC9I,UAAU,GAAG,KAAK;IAEvB,MAAMoH,KAAK,GAAGoB,KAAK,CAACO,YAAY,EAAE3B,KAAK;IACvC,IAAIA,KAAK,EAAE;MACT,IAAI,CAACuB,WAAW,CAACnC,KAAK,CAACoC,IAAI,CAACxB,KAAK,CAAC,CAAC;;EAEvC;EAEA;EACQuB,WAAWA,CAACvB,KAAa;IAC/B,IAAI,CAAC1C,WAAW,GAAG,EAAE;IAErB,KAAK,MAAMsE,IAAI,IAAI5B,KAAK,EAAE;MACxB,IAAI,CAAC,IAAI,CAAC6B,YAAY,CAACD,IAAI,CAAC,EAAE;QAC5B;;MAGF;MACA,IAAI,CAAC,IAAI,CAAC5F,aAAa,CAAC8F,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACvF,IAAI,KAAKoF,IAAI,CAACpF,IAAI,CAAC,EAAE;QACvD,IAAI,CAACR,aAAa,CAACgG,IAAI,CAACJ,IAAI,CAAC;;;EAGnC;EAEA;EACQC,YAAYA,CAACD,IAAU;IAC7B;IACA,IAAIA,IAAI,CAAClF,IAAI,GAAG,IAAI,CAACyB,WAAW,EAAE;MAChC,IAAI,CAACb,WAAW,GAAG,OAAOsE,IAAI,CAACpF,IAAI,iBAAiB;MACpD,OAAO,KAAK;;IAGd;IACA,MAAMyF,aAAa,GAAG,GAAG,GAAGL,IAAI,CAACpF,IAAI,CAACqD,KAAK,CAAC,GAAG,CAAC,CAACE,GAAG,EAAE,EAAEmC,WAAW,EAAE;IACrE,IAAI,CAAC,IAAI,CAAC9D,gBAAgB,CAAC+D,QAAQ,CAACF,aAAa,CAAC,EAAE;MAClD,IAAI,CAAC3E,WAAW,GAAG,OAAOsE,IAAI,CAACpF,IAAI,gCAAgC;MACnE,OAAO,KAAK;;IAGd,OAAO,IAAI;EACb;EAEA;EACAH,UAAUA,CAACD,KAAa;IACtB,IAAI,CAACJ,aAAa,CAACoG,MAAM,CAAChG,KAAK,EAAE,CAAC,CAAC;IACnC,IAAI,CAACkB,WAAW,GAAG,EAAE;EACvB;EAEA;EACAb,cAAcA,CAAC4F,KAAa;IAC1B,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,SAAS;IAEjC,MAAMC,CAAC,GAAG,IAAI;IACd,MAAMC,KAAK,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACzC,MAAMC,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,GAAG,CAACN,KAAK,CAAC,GAAGI,IAAI,CAACE,GAAG,CAACL,CAAC,CAAC,CAAC;IAEnD,OAAOM,UAAU,CAAC,CAACP,KAAK,GAAGI,IAAI,CAACI,GAAG,CAACP,CAAC,EAAEE,CAAC,CAAC,EAAEM,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGP,KAAK,CAACC,CAAC,CAAC;EACzE;EAEA;EACAxF,WAAWA,CAAA;IACT,IAAI,IAAI,CAAChB,aAAa,CAACrB,MAAM,KAAK,CAAC,EAAE;MACnC,IAAI,CAAC2C,WAAW,GAAG,WAAW;MAC9B;;IAGF,IAAI,CAACD,WAAW,GAAG,IAAI;IACvB,IAAI,CAACC,WAAW,GAAG,EAAE;IAErB;IACA,MAAMyF,YAAY,GAAG,IAAI,CAAC/G,aAAa,CAACgH,GAAG,CAACpB,IAAI,IAAI,IAAI,CAACqB,mBAAmB,CAACrB,IAAI,CAAC,CAAC;IAEnFsB,OAAO,CAACC,GAAG,CAACJ,YAAY,CAAC,CAACK,IAAI,CAACC,QAAQ,IAAG;MACxC;MACA,MAAMtF,kBAAkB,GAAwBsF,QAAQ,CAACL,GAAG,CAAC,CAACM,OAAO,EAAElH,KAAK,MAAM;QAChFmH,UAAU,EAAED,OAAO;QACnB/H,SAAS,EAAE,IAAI,CAACS,aAAa,CAACI,KAAK,CAAC,CAACI,IAAI;QACzCgH,SAAS,EAAE,IAAI,CAACC,wBAAwB,CAAC,IAAI,CAACzH,aAAa,CAACI,KAAK,CAAC,CAACI,IAAI;OACxE,CAAC,CAAC;MAEH;MACA,IAAI,CAACkB,qBAAqB,CAACgG,4CAA4C,CAAC;QAAE5E,IAAI,EAAEf;MAAkB,CAAE,CAAC,CAACgB,SAAS,CAAC;QAC9GmB,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAI,CAAC9C,WAAW,GAAG,KAAK;UACxB,IAAI,CAACrB,aAAa,GAAG,EAAE;UACvB,IAAI,CAACsC,cAAc,GAAG,KAAK,CAAC,CAAC;UAC7B,IAAI,CAACM,oBAAoB,EAAE,CAAC,CAAC;UAE7B;UACA,IAAI,CAAChB,aAAa,CAAC+F,aAAa,CAAC,QAAQ,CAAC;QAC5C,CAAC;QACD5C,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAAC1D,WAAW,GAAG,KAAK;UACxB,IAAI,CAACC,WAAW,GAAG,YAAY;UAC/B0D,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;UACrC,IAAI,CAACnD,aAAa,CAACqD,YAAY,CAAC,SAAS,IAAIF,KAAK,CAACA,KAAK,EAAE6C,OAAO,IAAI,MAAM,CAAC,CAAC;QAC/E;OACD,CAAC;IACJ,CAAC,CAAC,CAACC,KAAK,CAAC9C,KAAK,IAAG;MACf,IAAI,CAAC1D,WAAW,GAAG,KAAK;MACxB,IAAI,CAACC,WAAW,GAAG,QAAQ;MAC3B0D,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,IAAI,CAACnD,aAAa,CAACqD,YAAY,CAAC,QAAQ,CAAC;IAC3C,CAAC,CAAC;EACJ;EAEA;EACQgC,mBAAmBA,CAACrB,IAAU;IACpC,OAAO,IAAIsB,OAAO,CAAC,CAACY,OAAO,EAAEC,MAAM,KAAI;MACrC,MAAMC,MAAM,GAAG,IAAIC,UAAU,EAAE;MAC/BD,MAAM,CAACE,aAAa,CAACtC,IAAI,CAAC;MAC1BoC,MAAM,CAACG,MAAM,GAAG,MAAK;QACnB,MAAMC,MAAM,GAAGJ,MAAM,CAACI,MAAgB;QACtC;QACA,MAAMC,MAAM,GAAGD,MAAM,CAACvE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACnCiE,OAAO,CAACO,MAAM,CAAC;MACjB,CAAC;MACDL,MAAM,CAACM,OAAO,GAAGvD,KAAK,IAAIgD,MAAM,CAAChD,KAAK,CAAC;IACzC,CAAC,CAAC;EACJ;EAEA;EACQ0C,wBAAwBA,CAAC3D,QAAgB;IAC/C,MAAMyE,SAAS,GAAGzE,QAAQ,CAACoC,WAAW,EAAE,CAACsC,SAAS,CAAC1E,QAAQ,CAAC2E,WAAW,CAAC,GAAG,CAAC,CAAC;IAC7E,QAAQF,SAAS;MACf,KAAK,MAAM;QACT,OAAO,CAAC;MAAE;MACZ,KAAK,MAAM;QACT,OAAO,CAAC;MAAE;MACZ,KAAK,MAAM;QACT,OAAO,CAAC;MAAE;MACZ,KAAK,MAAM;QACT,OAAO,CAAC;MAAE;MACZ;QACE,OAAO,CAAC;MAAE;;EAEhB;EAEA;EACAnH,UAAUA,CAAA;IACR,IAAI,CAACpB,aAAa,GAAG,EAAE;IACvB,IAAI,CAACsB,WAAW,GAAG,EAAE;IACrB,IAAI,CAACgB,cAAc,GAAG,KAAK,CAAC,CAAC;EAC/B;EAEA;EACAhC,WAAWA,CAACwD,QAAgB;IAC1B,MAAMyE,SAAS,GAAGzE,QAAQ,CAACD,KAAK,CAAC,GAAG,CAAC,CAACE,GAAG,EAAE,EAAEmC,WAAW,EAAE;IAC1D,QAAQqC,SAAS;MACf,KAAK,KAAK;QACR,OAAO,gBAAgB;MACzB,KAAK,KAAK;MACV,KAAK,KAAK;MACV,KAAK,KAAK;QACR,OAAO,uBAAuB;MAChC,KAAK,KAAK;MACV,KAAK,MAAM;MACX,KAAK,KAAK;MACV,KAAK,KAAK;MACV,KAAK,KAAK;MACV,KAAK,MAAM;QACT,OAAO,sBAAsB;MAC/B;QACE,OAAO,gBAAgB;;EAE7B;EAEA;EACA5H,WAAWA,CAACmD,QAAgB;IAC1B,MAAMyE,SAAS,GAAGzE,QAAQ,CAACD,KAAK,CAAC,GAAG,CAAC,CAACE,GAAG,EAAE,EAAEmC,WAAW,EAAE;IAC1D,QAAQqC,SAAS;MACf,KAAK,KAAK;QACR,OAAO,OAAO;MAChB,KAAK,KAAK;QACR,OAAO,WAAW;MACpB,KAAK,KAAK;QACR,OAAO,QAAQ;MACjB,KAAK,KAAK;QACR,OAAO,QAAQ;MACjB,KAAK,KAAK;MACV,KAAK,MAAM;QACT,OAAO,QAAQ;MACjB,KAAK,KAAK;QACR,OAAO,OAAO;MAChB,KAAK,KAAK;QACR,OAAO,OAAO;MAChB,KAAK,KAAK;QACR,OAAO,OAAO;MAChB,KAAK,MAAM;QACT,OAAO,QAAQ;MACjB;QACE,OAAO,MAAM;;EAEnB;EAEA;EACA7K,eAAeA,CAACoG,QAAgB;IAC9B,MAAMyE,SAAS,GAAGzE,QAAQ,CAACD,KAAK,CAAC,GAAG,CAAC,CAACE,GAAG,EAAE,EAAEmC,WAAW,EAAE;IAC1D,QAAQqC,SAAS;MACf,KAAK,KAAK;QACR,OAAO,KAAK;MACd,KAAK,KAAK;MACV,KAAK,KAAK;MACV,KAAK,KAAK;QACR,OAAO,KAAK;MACd,KAAK,KAAK;MACV,KAAK,MAAM;MACX,KAAK,KAAK;MACV,KAAK,KAAK;MACV,KAAK,KAAK;MACV,KAAK,MAAM;QACT,OAAO,IAAI;MACb;QACE,OAAO,IAAI;;EAEjB;EAEA;EACA/K,eAAeA,CAACsG,QAAgB;IAC9B,MAAMyE,SAAS,GAAGzE,QAAQ,CAACD,KAAK,CAAC,GAAG,CAAC,CAACE,GAAG,EAAE,EAAEmC,WAAW,EAAE;IAC1D,QAAQqC,SAAS;MACf,KAAK,KAAK;QACR,OAAO,gBAAgB;MACzB,KAAK,KAAK;MACV,KAAK,KAAK;MACV,KAAK,KAAK;QACR,OAAO,uBAAuB;MAChC,KAAK,KAAK;MACV,KAAK,MAAM;MACX,KAAK,KAAK;MACV,KAAK,KAAK;MACV,KAAK,KAAK;MACV,KAAK,MAAM;QACT,OAAO,sBAAsB;MAC/B;QACE,OAAO,gBAAgB;;EAE7B;EAEA;EACAhL,gBAAgBA,CAACuG,QAAgB;IAC/B,MAAMyE,SAAS,GAAGzE,QAAQ,CAACD,KAAK,CAAC,GAAG,CAAC,CAACE,GAAG,EAAE,EAAEmC,WAAW,EAAE;IAC1D,QAAQqC,SAAS;MACf,KAAK,KAAK;QACR,OAAO,WAAW;MACpB,KAAK,KAAK;QACR,OAAO,eAAe;MACxB,KAAK,KAAK;QACR,OAAO,YAAY;MACrB,KAAK,KAAK;QACR,OAAO,YAAY;MACrB,KAAK,KAAK;MACV,KAAK,MAAM;QACT,OAAO,YAAY;MACrB,KAAK,KAAK;QACR,OAAO,WAAW;MACpB,KAAK,KAAK;QACR,OAAO,WAAW;MACpB,KAAK,KAAK;QACR,OAAO,WAAW;MACpB,KAAK,MAAM;QACT,OAAO,YAAY;MACrB;QACE,OAAO,MAAM;;EAEnB;EAEA;EACAG,gBAAgBA,CAAA;IACd,IAAI,CAACpG,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;IAC1C,IAAI,CAAC,IAAI,CAACA,cAAc,EAAE;MACxB;MACA,IAAI,CAAClB,UAAU,EAAE;;EAErB;EAEA;EACAxD,kBAAkBA,CAAA;IAChB,IAAI,CAAC,IAAI,CAACmC,iBAAiB,IAAI,CAACqD,KAAK,CAACC,OAAO,CAAC,IAAI,CAACtD,iBAAiB,CAAC,EAAE;MACrE,OAAO,CAAC;;IAEV,OAAO,IAAI,CAACA,iBAAiB,CAAC4I,MAAM,CAAC,CAACC,KAAK,EAAErF,KAAK,KAAI;MACpD,OAAOqF,KAAK,IAAIrF,KAAK,CAAC7E,kBAAkB,GAAG6E,KAAK,CAAC7E,kBAAkB,CAACC,MAAM,GAAG,CAAC,CAAC;IACjF,CAAC,EAAE,CAAC,CAAC;EACP;EAEA;EACAJ,eAAeA,CAACsK,UAAkB;IAChC,IAAI,CAACtG,eAAe,CAACsG,UAAU,CAAC,GAAG,CAAC,IAAI,CAACtG,eAAe,CAACsG,UAAU,CAAC;EACtE;EAEA;EACAjK,oBAAoBA,CAACiK,UAAkB;IACrC,OAAO,IAAI,CAACtG,eAAe,CAACsG,UAAU,CAAC,IAAI,KAAK;EAClD;EAEA;EACA7K,eAAeA,CAAA;IACb8K,MAAM,CAACC,IAAI,CAAC,IAAI,CAACxG,eAAe,CAAC,CAACe,OAAO,CAAC0F,GAAG,IAAG;MAC9C,IAAI,CAACzG,eAAe,CAACyG,GAAG,CAAC,GAAG,KAAK;IACnC,CAAC,CAAC;EACJ;EAEA;EACA9K,iBAAiBA,CAAA;IACf4K,MAAM,CAACC,IAAI,CAAC,IAAI,CAACxG,eAAe,CAAC,CAACe,OAAO,CAAC0F,GAAG,IAAG;MAC9C,IAAI,CAACzG,eAAe,CAACyG,GAAG,CAAC,GAAG,IAAI;IAClC,CAAC,CAAC;EACJ;EAEA;EACA7J,aAAaA,CAAC8J,MAAe;IAC3B,QAAQA,MAAM;MACZ,KAAK,CAAC;QACJ,OAAO,IAAI;MACb,KAAK,CAAC;QACJ,OAAO,IAAI;MACb,KAAK,CAAC;QACJ,OAAO,IAAI;MACb;QACE,OAAO,EAAE;;EAEf;EAEA;EACAnK,cAAcA,CAACmK,MAAe;IAC5B,QAAQA,MAAM;MACZ,KAAK,CAAC;QACJ,OAAO,eAAe;MACxB,KAAK,CAAC;QACJ,OAAO,iBAAiB;MAC1B,KAAK,CAAC;QACJ,OAAO,oBAAoB;MAC7B;QACE,OAAO,gBAAgB;;EAE7B;EAEA;EACA/J,aAAaA,CAAC+J,MAAe;IAC3B,QAAQA,MAAM;MACZ,KAAK,CAAC;QACJ,OAAO,QAAQ;MAAE;MACnB,KAAK,CAAC;QACJ,OAAO,SAAS;MAAE;MACpB,KAAK,CAAC;QACJ,OAAO,aAAa;MAAE;MACxB;QACE,OAAO,aAAa;;EAE1B;EAEA;EACAhK,oBAAoBA,CAACgK,MAAe;IAClC,QAAQA,MAAM;MACZ,KAAK,CAAC;QACJ,OAAO,SAAS;MAClB,KAAK,CAAC;QACJ,OAAO,QAAQ;MACjB,KAAK,CAAC;QACJ,OAAO,SAAS;MAClB;QACE,OAAO,MAAM;;EAEnB;EAEA;EACAC,cAAcA,CAAClF,KAAY;IACzB,MAAMmF,KAAK,GAA8B,EAAE;IAC3CnF,KAAK,CAACV,OAAO,CAACsC,IAAI,IAAG;MACnB,MAAMqD,MAAM,GAAGrD,IAAI,CAAC5G,OAAO,IAAI,CAAC;MAChCmK,KAAK,CAACF,MAAM,CAAC,GAAG,CAACE,KAAK,CAACF,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;IAC1C,CAAC,CAAC;IACF,OAAOE,KAAK;EACd;EAEA;EACAC,kBAAkBA,CAACpF,KAAY;IAC7B,MAAMqF,OAAO,GAAG,IAAIC,GAAG,CAACtF,KAAK,CAACgD,GAAG,CAACpB,IAAI,IAAIA,IAAI,CAAC5G,OAAO,CAAC,CAACuK,MAAM,CAACN,MAAM,IAAIA,MAAM,CAAC,CAAC;IACjF,OAAOI,OAAO,CAAC3I,IAAI,GAAG,CAAC;EACzB;EAAC,QAAA8I,CAAA,G;qBAzfUjI,gBAAgB,EAAAlG,EAAA,CAAAoO,iBAAA,CAAAC,EAAA,CAAAC,YAAA,GAAAtO,EAAA,CAAAoO,iBAAA,CAAAC,EAAA,CAAAE,oBAAA,GAAAvO,EAAA,CAAAoO,iBAAA,CAAAI,EAAA,CAAAC,cAAA,GAAAzO,EAAA,CAAAoO,iBAAA,CAAAM,EAAA,CAAAhP,YAAA,GAAAM,EAAA,CAAAoO,iBAAA,CAAAO,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAhB3I,gBAAgB;IAAA4I,SAAA;IAAAC,UAAA;IAAAC,QAAA,GAAAhP,EAAA,CAAAiP,kBAAA,CAxBhB,CACT9P,cAAc,EACdO,YAAY,CACb,GAAAM,EAAA,CAAAkP,mBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;QCvBHxP,EAAA,CAAAC,cAAA,aAAqB;QAEnBD,EAAA,CAAAI,SAAA,cAAmB;QAMXJ,EAJR,CAAAC,cAAA,aAAqB,aACc,aACV,aACS,aACP;QAAAD,EAAA,CAAAE,MAAA,wDAAS;QAAAF,EAAA,CAAAG,YAAA,EAAM;QAGhCH,EADF,CAAAC,cAAA,aAAqC,gBACyD;QAAtDD,EAAA,CAAAK,UAAA,mBAAAqP,kDAAA;UAAA1P,EAAA,CAAAQ,aAAA,CAAAmP,GAAA;UAAA,OAAA3P,EAAA,CAAAY,WAAA,CAAS6O,GAAA,CAAApC,gBAAA,EAAkB;QAAA,EAAC;QAChErN,EAAA,CAAAI,SAAA,YAA4B;QAE5BJ,EADA,CAAAwB,UAAA,KAAAoO,iCAAA,mBAA2B,KAAAC,iCAAA,mBACD;QAGhC7P,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;QAqBNH,EAlBA,CAAAwB,UAAA,KAAAsO,gCAAA,mBAAiF,KAAAC,gCAAA,mBAoBnD;QAe9B/P,EAAA,CAAAC,cAAA,oBAC2D;QAAzDD,EAAA,CAAAK,UAAA,oBAAA2P,mDAAAzP,MAAA;UAAAP,EAAA,CAAAQ,aAAA,CAAAmP,GAAA;UAAA,OAAA3P,EAAA,CAAAY,WAAA,CAAU6O,GAAA,CAAA3F,cAAA,CAAAvJ,MAAA,CAAsB;QAAA,EAAC;QADnCP,EAAA,CAAAG,YAAA,EAC2D;QAgK3DH,EA/JA,CAAAwB,UAAA,KAAAyO,gCAAA,mBACqE,KAAAC,gCAAA,kBAsB0C,KAAAC,gCAAA,mBA8EF,KAAAC,gCAAA,kBA0DpD;QAQDpQ,EADxD,CAAAC,cAAA,cAAiC,eACR,kBAA+B,aAAkB;QAAAD,EAAA,CAAAE,MAAA,sCAAK;QAKvFF,EALuF,CAAAG,YAAA,EAAI,EAAS,EAAM,EAC5F,EACF,EACF,EACF,EACF;;;QA1NyEH,EAAA,CAAA8B,SAAA,GAAwB;QAAxB9B,EAAA,CAAA+B,UAAA,aAAA0N,GAAA,CAAAzJ,WAAA,CAAwB;QAElFhG,EAAA,CAAA8B,SAAA,GAAkB;QAAlB9B,EAAA,CAAA+B,UAAA,UAAA0N,GAAA,CAAAzJ,WAAA,CAAkB;QAClBhG,EAAA,CAAA8B,SAAA,EAAiB;QAAjB9B,EAAA,CAAA+B,UAAA,SAAA0N,GAAA,CAAAzJ,WAAA,CAAiB;QAMIhG,EAAA,CAAA8B,SAAA,EAA6C;QAA7C9B,EAAA,CAAA+B,UAAA,SAAA0N,GAAA,CAAAxI,cAAA,KAAAwI,GAAA,CAAA9K,aAAA,CAAArB,MAAA,CAA6C;QAkBlDtD,EAAA,CAAA8B,SAAA,EAA6C;QAA7C9B,EAAA,CAAA+B,UAAA,SAAA0N,GAAA,CAAAxI,cAAA,KAAAwI,GAAA,CAAA9K,aAAA,CAAArB,MAAA,CAA6C;QAoBvEtD,EAAA,CAAA8B,SAAA,GAAgE;QAAhE9B,EAAA,CAAA+B,UAAA,SAAA0N,GAAA,CAAA5N,qBAAA,CAAAoC,KAAA,IAAAwL,GAAA,CAAA5N,qBAAA,CAAAI,KAAA,CAAgE;QAsBbjC,EAAA,CAAA8B,SAAA,EAAuD;QAAvD9B,EAAA,CAAA+B,UAAA,SAAA0N,GAAA,CAAA/K,iBAAA,IAAA+K,GAAA,CAAA/K,iBAAA,CAAApB,MAAA,KAAuD;QA8ElDtD,EAAA,CAAA8B,SAAA,EAAgD;QAAhD9B,EAAA,CAAA+B,UAAA,SAAA0N,GAAA,CAAAxI,cAAA,IAAAwI,GAAA,CAAA9K,aAAA,CAAArB,MAAA,KAAgD;QA0DrEtD,EAAA,CAAA8B,SAAA,EAAiB;QAAjB9B,EAAA,CAAA+B,UAAA,SAAA0N,GAAA,CAAAxJ,WAAA,CAAiB;;;mBDhMnD/G,YAAY,EAAAmR,EAAA,CAAAC,UAAA,EAAElR,WAAW,EAAAmR,EAAA,CAAAC,KAAA,EAAAC,EAAA,CAAAC,aAAA,EAAErR,eAAe,EAAEJ,YAAY,EAAA0R,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAEtR,cAAc,EAAED,WAAW,EAAAwR,EAAA,CAAAC,KAAA;IAAAC,MAAA;IAAAC,IAAA;MAAAC,SAAA,EAGnF,CACVvR,OAAO,CAAC,YAAY,EAAE,CACpBC,KAAK,CAAC,IAAI,EAAEC,KAAK,CAAC;QAChBsR,MAAM,EAAE,GAAG;QACXC,OAAO,EAAE,CAAC;QACVC,QAAQ,EAAE;OACX,CAAC,CAAC,EACHzR,KAAK,CAAC,KAAK,EAAEC,KAAK,CAAC;QACjBsR,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE,CAAC;QACVC,QAAQ,EAAE;OACX,CAAC,CAAC,EACHvR,UAAU,CAAC,WAAW,EAAEC,OAAO,CAAC,mBAAmB,CAAC,CAAC,EACrDD,UAAU,CAAC,WAAW,EAAEC,OAAO,CAAC,mBAAmB,CAAC,CAAC,CACtD,CAAC;IACH;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}