{"ast": null, "code": "import { environment } from '../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nexport class UtilityService {\n  constructor() {\n    this.BASE_FILE = environment.BASE_URL_API;\n    this.DownloadFile = data => {\n      if (data) {\n        const downloadedFile = new Blob([data.body], {\n          type: data.body.type\n        });\n        const a = document.createElement('a');\n        a.setAttribute('style', 'display:none;');\n        document.body.appendChild(a);\n        const fileName = this.parseFileName(data.headers.get('Content-Disposition'));\n        a.download = fileName;\n        a.href = URL.createObjectURL(downloadedFile);\n        a.target = '_blank';\n        a.click();\n        document.body.removeChild(a);\n      }\n    };\n  }\n  downloadFileFromUrl(file) {\n    const xhr = new XMLHttpRequest();\n    xhr.open('GET', `${this.BASE_FILE}${file.CFile}`, true);\n    xhr.responseType = 'blob';\n    xhr.onload = () => {\n      if (xhr.status === 200) {\n        const url = window.URL.createObjectURL(xhr.response);\n        const link = document.createElement('a');\n        link.href = url;\n        link.download = file.CName;\n        link.style.display = 'none';\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        window.URL.revokeObjectURL(url);\n      }\n    };\n    xhr.send();\n  }\n  getFileNameFromUrl(url) {\n    const parts = url.split('/');\n    const fileName = parts.pop();\n    return fileName;\n  }\n  downloadFileFullUrl(file) {\n    const xhr = new XMLHttpRequest();\n    xhr.open('GET', `${file.CFile}?date=${new Date().getTime()}`, true);\n    xhr.responseType = 'blob';\n    xhr.onload = () => {\n      if (xhr.status === 200) {\n        const url = window.URL.createObjectURL(xhr.response);\n        const link = document.createElement('a');\n        link.href = url;\n        link.download = file.CName;\n        link.style.display = 'none';\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        window.URL.revokeObjectURL(url);\n      }\n    };\n    xhr.send();\n  }\n  parseFileName(header, def) {\n    if (!header) {\n      return def;\n    }\n    const name = header.split(';')[2].trim().split('=')[1];\n    return decodeURI(name.replace(/\"/g, '')).replace('UTF-8\\'\\'', ''); // 注意中文請在服務端加入URL編碼\n  }\n  static #_ = this.ɵfac = function UtilityService_Factory(t) {\n    return new (t || UtilityService)();\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: UtilityService,\n    factory: UtilityService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["environment", "UtilityService", "constructor", "BASE_FILE", "BASE_URL_API", "DownloadFile", "data", "downloadedFile", "Blob", "body", "type", "a", "document", "createElement", "setAttribute", "append<PERSON><PERSON><PERSON>", "fileName", "parseFileName", "headers", "get", "download", "href", "URL", "createObjectURL", "target", "click", "<PERSON><PERSON><PERSON><PERSON>", "downloadFileFromUrl", "file", "xhr", "XMLHttpRequest", "open", "CFile", "responseType", "onload", "status", "url", "window", "response", "link", "CName", "style", "display", "revokeObjectURL", "send", "getFileNameFromUrl", "parts", "split", "pop", "downloadFileFullUrl", "Date", "getTime", "header", "def", "name", "trim", "decodeURI", "replace", "_", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\SaleChangeFront\\src\\app\\shared\\services\\utility.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpResponse } from '@angular/common/http';\r\nimport { environment } from '../../../environments/environment';\r\nimport { BuildCaseGetFileRespone } from '../../../services/api/models';\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class UtilityService {\r\n  readonly BASE_FILE = environment.BASE_URL_API;\r\n\r\n  constructor() { }\r\n\r\n  downloadFileFromUrl(file: BuildCaseGetFileRespone) {\r\n    const xhr = new XMLHttpRequest();\r\n    xhr.open('GET', `${this.BASE_FILE}${file.CFile}`\r\n      , true);\r\n    xhr.responseType = 'blob';\r\n    xhr.onload = () => {\r\n      if (xhr.status === 200) {\r\n        const url = window.URL.createObjectURL(xhr.response);\r\n        const link = document.createElement('a');\r\n        link.href = url;\r\n        link.download = file.CName!;\r\n        link.style.display = 'none';\r\n        document.body.appendChild(link);\r\n        link.click();\r\n        document.body.removeChild(link);\r\n        window.URL.revokeObjectURL(url);\r\n      }\r\n    };\r\n    xhr.send();\r\n  }\r\n\r\n  getFileNameFromUrl(url: string) {\r\n    const parts = url.split('/');\r\n    const fileName = parts.pop();\r\n    return fileName;\r\n  }\r\n\r\n  downloadFileFullUrl(file: { CFile: string | any; CName: string | any }) {\r\n    const xhr = new XMLHttpRequest();\r\n    xhr.open('GET', `${file.CFile}?date=${new Date().getTime()}`\r\n      , true);\r\n    xhr.responseType = 'blob';\r\n    xhr.onload = () => {\r\n      if (xhr.status === 200) {\r\n        const url = window.URL.createObjectURL(xhr.response);\r\n        const link = document.createElement('a');\r\n        link.href = url;\r\n        link.download = file.CName!;\r\n        link.style.display = 'none';\r\n        document.body.appendChild(link);\r\n        link.click();\r\n        document.body.removeChild(link);\r\n        window.URL.revokeObjectURL(url);\r\n      }\r\n    };\r\n    xhr.send();\r\n  }\r\n\r\n  DownloadFile = (data: HttpResponse<Blob>) => {\r\n    if (data) {\r\n      const downloadedFile = new Blob([data.body], { type: data.body.type });\r\n      const a = document.createElement('a');\r\n      a.setAttribute('style', 'display:none;');\r\n      document.body.appendChild(a);\r\n      const fileName = this.parseFileName(data.headers.get('Content-Disposition'));\r\n      a.download = fileName;\r\n      a.href = URL.createObjectURL(downloadedFile);\r\n      a.target = '_blank';\r\n      a.click();\r\n      document.body.removeChild(a);\r\n    }\r\n  }\r\n\r\n  private parseFileName(header: string, def?: string): string {\r\n    if (!header) {\r\n      return def;\r\n    }\r\n    const name = header.split(';')[2].trim().split('=')[1];\r\n    return decodeURI(name.replace(/\"/g, '')).replace('UTF-8\\'\\'', ''); // 注意中文請在服務端加入URL編碼\r\n  }\r\n}\r\n"], "mappings": "AAEA,SAASA,WAAW,QAAQ,mCAAmC;;AAK/D,OAAM,MAAOC,cAAc;EAGzBC,YAAA;IAFS,KAAAC,SAAS,GAAGH,WAAW,CAACI,YAAY;IAoD7C,KAAAC,YAAY,GAAIC,IAAwB,IAAI;MAC1C,IAAIA,IAAI,EAAE;QACR,MAAMC,cAAc,GAAG,IAAIC,IAAI,CAAC,CAACF,IAAI,CAACG,IAAI,CAAC,EAAE;UAAEC,IAAI,EAAEJ,IAAI,CAACG,IAAI,CAACC;QAAI,CAAE,CAAC;QACtE,MAAMC,CAAC,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;QACrCF,CAAC,CAACG,YAAY,CAAC,OAAO,EAAE,eAAe,CAAC;QACxCF,QAAQ,CAACH,IAAI,CAACM,WAAW,CAACJ,CAAC,CAAC;QAC5B,MAAMK,QAAQ,GAAG,IAAI,CAACC,aAAa,CAACX,IAAI,CAACY,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC,CAAC;QAC5ER,CAAC,CAACS,QAAQ,GAAGJ,QAAQ;QACrBL,CAAC,CAACU,IAAI,GAAGC,GAAG,CAACC,eAAe,CAAChB,cAAc,CAAC;QAC5CI,CAAC,CAACa,MAAM,GAAG,QAAQ;QACnBb,CAAC,CAACc,KAAK,EAAE;QACTb,QAAQ,CAACH,IAAI,CAACiB,WAAW,CAACf,CAAC,CAAC;;IAEhC,CAAC;EA/De;EAEhBgB,mBAAmBA,CAACC,IAA6B;IAC/C,MAAMC,GAAG,GAAG,IAAIC,cAAc,EAAE;IAChCD,GAAG,CAACE,IAAI,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC5B,SAAS,GAAGyB,IAAI,CAACI,KAAK,EAAE,EAC5C,IAAI,CAAC;IACTH,GAAG,CAACI,YAAY,GAAG,MAAM;IACzBJ,GAAG,CAACK,MAAM,GAAG,MAAK;MAChB,IAAIL,GAAG,CAACM,MAAM,KAAK,GAAG,EAAE;QACtB,MAAMC,GAAG,GAAGC,MAAM,CAACf,GAAG,CAACC,eAAe,CAACM,GAAG,CAACS,QAAQ,CAAC;QACpD,MAAMC,IAAI,GAAG3B,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;QACxC0B,IAAI,CAAClB,IAAI,GAAGe,GAAG;QACfG,IAAI,CAACnB,QAAQ,GAAGQ,IAAI,CAACY,KAAM;QAC3BD,IAAI,CAACE,KAAK,CAACC,OAAO,GAAG,MAAM;QAC3B9B,QAAQ,CAACH,IAAI,CAACM,WAAW,CAACwB,IAAI,CAAC;QAC/BA,IAAI,CAACd,KAAK,EAAE;QACZb,QAAQ,CAACH,IAAI,CAACiB,WAAW,CAACa,IAAI,CAAC;QAC/BF,MAAM,CAACf,GAAG,CAACqB,eAAe,CAACP,GAAG,CAAC;;IAEnC,CAAC;IACDP,GAAG,CAACe,IAAI,EAAE;EACZ;EAEAC,kBAAkBA,CAACT,GAAW;IAC5B,MAAMU,KAAK,GAAGV,GAAG,CAACW,KAAK,CAAC,GAAG,CAAC;IAC5B,MAAM/B,QAAQ,GAAG8B,KAAK,CAACE,GAAG,EAAE;IAC5B,OAAOhC,QAAQ;EACjB;EAEAiC,mBAAmBA,CAACrB,IAAkD;IACpE,MAAMC,GAAG,GAAG,IAAIC,cAAc,EAAE;IAChCD,GAAG,CAACE,IAAI,CAAC,KAAK,EAAE,GAAGH,IAAI,CAACI,KAAK,SAAS,IAAIkB,IAAI,EAAE,CAACC,OAAO,EAAE,EAAE,EACxD,IAAI,CAAC;IACTtB,GAAG,CAACI,YAAY,GAAG,MAAM;IACzBJ,GAAG,CAACK,MAAM,GAAG,MAAK;MAChB,IAAIL,GAAG,CAACM,MAAM,KAAK,GAAG,EAAE;QACtB,MAAMC,GAAG,GAAGC,MAAM,CAACf,GAAG,CAACC,eAAe,CAACM,GAAG,CAACS,QAAQ,CAAC;QACpD,MAAMC,IAAI,GAAG3B,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;QACxC0B,IAAI,CAAClB,IAAI,GAAGe,GAAG;QACfG,IAAI,CAACnB,QAAQ,GAAGQ,IAAI,CAACY,KAAM;QAC3BD,IAAI,CAACE,KAAK,CAACC,OAAO,GAAG,MAAM;QAC3B9B,QAAQ,CAACH,IAAI,CAACM,WAAW,CAACwB,IAAI,CAAC;QAC/BA,IAAI,CAACd,KAAK,EAAE;QACZb,QAAQ,CAACH,IAAI,CAACiB,WAAW,CAACa,IAAI,CAAC;QAC/BF,MAAM,CAACf,GAAG,CAACqB,eAAe,CAACP,GAAG,CAAC;;IAEnC,CAAC;IACDP,GAAG,CAACe,IAAI,EAAE;EACZ;EAiBQ3B,aAAaA,CAACmC,MAAc,EAAEC,GAAY;IAChD,IAAI,CAACD,MAAM,EAAE;MACX,OAAOC,GAAG;;IAEZ,MAAMC,IAAI,GAAGF,MAAM,CAACL,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACQ,IAAI,EAAE,CAACR,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACtD,OAAOS,SAAS,CAACF,IAAI,CAACG,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,CAACA,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC,CAAC;EACrE;EAAC,QAAAC,CAAA,G;qBA1EUzD,cAAc;EAAA;EAAA,QAAA0D,EAAA,G;WAAd1D,cAAc;IAAA2D,OAAA,EAAd3D,cAAc,CAAA4D,IAAA;IAAAC,UAAA,EAFb;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}