{"ast": null, "code": "export const environment = {\n  IS_PRODUCTION: false,\n  BASE_URL_API: 'https://jeansalechange-demo.webtech888.com/WebAPI',\n  OWNER_BUID: 'BU00000',\n  BASE_FILE: 'https://jeanjalechange.shindabu4.com/Files',\n  BASE_WITHOUT_FILEROOT: 'https://jeanjalechange.shindabu4.com',\n  SITE_KEY_RECAPTCHA: '6LcfevgpAAAAAM0f04QDOHKxer5TkwJtDSOyqR5B',\n  SITE_KEY_CLOUDFLARE_RECAPTCHA: '0x4AAAAAAA5AmFaCbBIAFZwg'\n};", "map": {"version": 3, "names": ["environment", "IS_PRODUCTION", "BASE_URL_API", "OWNER_BUID", "BASE_FILE", "BASE_WITHOUT_FILEROOT", "SITE_KEY_RECAPTCHA", "SITE_KEY_CLOUDFLARE_RECAPTCHA"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\SaleChangeFront\\src\\environments\\environment.dev.ts"], "sourcesContent": ["import { IEnvironment } from \"./environment.interface\";\r\n\r\nexport const environment: IEnvironment = {\r\n  IS_PRODUCTION: false,\r\n  BASE_URL_API: 'https://jeansalechange-demo.webtech888.com/WebAPI',\r\n  OWNER_BUID: 'BU00000',\r\n  BASE_FILE: 'https://jeanjalechange.shindabu4.com/Files',\r\n  BASE_WITHOUT_FILEROOT: 'https://jeanjalechange.shindabu4.com',\r\n  SITE_KEY_RECAPTCHA: '6LcfevgpAAAAAM0f04QDOHKxer5TkwJtDSOyqR5B',\r\n  SITE_KEY_CLOUDFLARE_RECAPTCHA: '0x4AAAAAAA5AmFaCbBIAFZwg',\r\n};\r\n"], "mappings": "AAEA,OAAO,MAAMA,WAAW,GAAiB;EACvCC,aAAa,EAAE,KAAK;EACpBC,YAAY,EAAE,mDAAmD;EACjEC,UAAU,EAAE,SAAS;EACrBC,SAAS,EAAE,4CAA4C;EACvDC,qBAAqB,EAAE,sCAAsC;EAC7DC,kBAAkB,EAAE,0CAA0C;EAC9DC,6BAA6B,EAAE;CAChC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}