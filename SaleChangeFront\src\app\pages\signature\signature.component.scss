@import "../../../styles.scss";

.signin {
  margin-top: 20px;
  width: 1216px;
  z-index: 3;
  min-height: 550px;
  height: 550px;

  .title {
    font-size: 32px;
    font-weight: 500;
    color: #231815;
    margin-bottom: 16px;
  }

  .pafbox {
    padding: 12.5px 16px;
    width: 100%;
    color: #000000;
    background: #F3F1EA99;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;

    @media screen and (max-width: 700px) {
      font-size: 14px;
    }

    @media screen and (max-width: $main-mobileM) {
      padding: 16px;
      font-size: 16px;
      display: block;
    }

    .box1 {

      .makesure,
      .makesures {
        padding: 6px 16px;
        border-radius: 6px;
        background: $error;
        color: $text-light;
        width: fit-content;
        font-weight: 600;
        font-size: 15px;
        border: 1px solid darken($error, 10%);
        box-shadow: 0 1px 3px rgba(241, 80, 47, 0.2);
      }

      .realsure,
      .realsures {
        background-color: $success !important;
        color: $text-light !important;
        border: 1px solid darken($success, 10%) !important;
        box-shadow: 0 1px 3px rgba(35, 164, 21, 0.2) !important;
      }
    }

    .box2 {
      color: #23181599;
    }

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.checkbox {
  border: 1px solid $mainColorB;
  padding: 12px 16px;
  border-radius: 4px;
  color: #231815CC;
  background-color: #FFF;

}

.sign {
  background-color: #E6F0F3;
  border-radius: 4px;
  height: 100px;

  @media screen and (max-width: $main-mobileM) {
    height: 200px;
  }
}

.card {
  @media screen and (max-width: $main-mobileM) {
    width: 100%;
  }

  button {
    @media screen and (max-width: $main-mobileM) {
      width: 100%;
      margin-top: 16px;
    }
  }
}

// 簡化的查看文件按鈕
.view-document-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  padding: 8px 16px;
  height: 36px;
  color: $text-light;
  background: linear-gradient(90deg, $primary-gold-dark 0%, $primary-gold-light 100%);
  border-radius: 18px;
  border: none;
  font-weight: 600;
  font-size: 14px;
  white-space: nowrap;
  transition: all 0.2s ease;
  cursor: pointer;

  &:hover {
    background: linear-gradient(90deg, $primary-gold-darker 0%, $primary-gold-dark 100%);
    transform: translateY(-1px);
  }

  span {
    color: inherit;
    font-weight: inherit;
  }

  img {
    filter: brightness(0) invert(1);
    width: 16px;
    height: 16px;
  }
}

// 繳款狀態樣式
.not-pay {
  padding: 6px 18px;
  background-color: rgba(241, 80, 47, 0.15);
  font-size: 16px;
  color: darken($error, 5%);
  border-radius: 6px;
  font-weight: 600;
  border: 1px solid rgba(241, 80, 47, 0.25);
  box-shadow: 0 1px 2px rgba(241, 80, 47, 0.1);
}

.pay-completed {
  padding: 6px 18px;
  background-color: rgba(35, 164, 21, 0.15);
  font-size: 16px;
  color: darken($success, 5%);
  border-radius: 6px;
  font-weight: 600;
  border: 1px solid rgba(35, 164, 21, 0.25);
  box-shadow: 0 1px 2px rgba(35, 164, 21, 0.1);
}

.pay-not-required {
  padding: 6px 18px;
  background-color: rgba(184, 166, 118, 0.25);
  font-size: 16px;
  color: $primary-gold-darker;
  border-radius: 6px;
  font-weight: 600;
  border: 1px solid rgba(184, 166, 118, 0.35);
  box-shadow: 0 1px 2px rgba(174, 155, 102, 0.15);
}

// 簽署狀態按鈕樣式 - 參考 button2 風格
.sign-status-btn {
  padding: 8px 20px;
  height: 36px;
  border-radius: 18px;
  font-size: 14px;
  font-weight: 600;
  border: none;
  cursor: default;
  transition: all 0.3s ease;
  box-shadow: 0 1px 4px rgba(174, 155, 102, 0.15);

  &.unsigned {
    color: $text-light;
    background: linear-gradient(90deg, $error 0%, lighten($error, 5%) 100%);
    box-shadow: 0 2px 6px rgba(241, 80, 47, 0.2);
  }

  &.signed {
    color: $text-light;
    background: linear-gradient(90deg, $success 0%, lighten($success, 5%) 100%);
    box-shadow: 0 2px 6px rgba(35, 164, 21, 0.2);
  }

  &:disabled {
    opacity: 1;
  }
}

// 優化的文件容器佈局
.document-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 20px;
  padding: 0;
  min-height: 60px;

  @media screen and (max-width: 767px) {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
    min-height: auto;
  }
}

// 左側文件資訊區域
.document-info {
  flex: 1;
  min-width: 0;

  @media screen and (max-width: 767px) {
    order: 1;
  }
}

// 文件標題
.document-title {
  font-size: 16px;
  font-weight: 600;
  color: $text-primary;
  margin-bottom: 8px;
  line-height: 1.4;

  // 處理長文件名
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;

  @media screen and (max-width: 767px) {
    white-space: normal;
    overflow: visible;
    text-overflow: unset;
  }
}

// 文件元資訊區域（日期和狀態）
.document-meta {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;

  @media screen and (max-width: 767px) {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}

// 日期資訊
.document-date {
  font-size: 14px;
  color: $text-secondary;
  line-height: 1.4;
  white-space: nowrap;
}

// 右側控制區域 - 所有按鈕靠右對齊
.document-controls {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-shrink: 0;
  justify-content: flex-end;

  @media screen and (max-width: 767px) {
    order: 2;
    justify-content: flex-end;
    flex-wrap: wrap;
    gap: 8px;
  }

  @media screen and (max-width: 480px) {
    flex-direction: column;
    align-items: flex-end;
    gap: 8px;
  }
}

// 優化的狀態徽章
.status-badge {
  padding: 6px 14px;
  border-radius: 18px;
  font-size: 13px;
  font-weight: 600;
  text-align: center;
  white-space: nowrap;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;

  &.unsigned {
    background: rgba(241, 80, 47, 0.1);
    color: $error;
    border: 1px solid rgba(241, 80, 47, 0.2);
  }

  &.signed {
    background: rgba(35, 164, 21, 0.1);
    color: $success;
    border: 1px solid rgba(35, 164, 21, 0.2);
  }
}

// 優化的查看文件按鈕
.view-document-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  padding: 6px 14px;
  height: 32px;
  color: $text-light;
  background: linear-gradient(90deg, $primary-gold-dark 0%, $primary-gold-light 100%);
  border-radius: 16px;
  border: none;
  font-weight: 600;
  font-size: 13px;
  white-space: nowrap;
  transition: all 0.2s ease;
  cursor: pointer;
  flex-shrink: 0;

  &:hover {
    background: linear-gradient(90deg, $primary-gold-darker 0%, $primary-gold-dark 100%);
    transform: translateY(-1px);
  }

  span {
    color: inherit;
    font-weight: inherit;
  }

  img {
    filter: brightness(0) invert(1);
    width: 14px;
    height: 14px;
  }
}

// 優化的簽署操作按鈕
.sign-action-btn {
  height: 32px;
  padding: 6px 16px;
  font-size: 13px;
  flex-shrink: 0;
  white-space: nowrap;

  @media screen and (max-width: 480px) {
    width: 100%;
  }
}

// 保持向後兼容性
.pay {
  padding: 6px 18px;
  background-color: rgba(35, 164, 21, 0.15);
  font-size: 16px;
  color: darken($success, 5%);
  border-radius: 6px;
  font-weight: 600;
  border: 1px solid rgba(35, 164, 21, 0.25);
  box-shadow: 0 1px 2px rgba(35, 164, 21, 0.1);
}
