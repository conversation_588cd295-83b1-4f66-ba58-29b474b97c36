@import "../../../styles.scss";

.signin {
  margin-top: 20px;
  width: 1216px;
  z-index: 3;
  min-height: 550px;
  height: 550px;

  .title {
    font-size: 32px;
    font-weight: 500;
    color: #231815;
    margin-bottom: 16px;
  }

  .pafbox {
    padding: 12.5px 16px;
    width: 100%;
    color: #000000;
    background: #F3F1EA99;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;

    @media screen and (max-width: 700px) {
      font-size: 14px;
    }

    @media screen and (max-width: $main-mobileM) {
      padding: 16px;
      font-size: 16px;
      display: block;
    }

    .box1 {

      .makesure,
      .makesures {
        padding: 6px 16px;
        border-radius: 6px;
        background: $error;
        color: $text-light;
        width: fit-content;
        font-weight: 600;
        font-size: 15px;
        border: 1px solid darken($error, 10%);
        box-shadow: 0 1px 3px rgba(241, 80, 47, 0.2);
      }

      .realsure,
      .realsures {
        background-color: $success !important;
        color: $text-light !important;
        border: 1px solid darken($success, 10%) !important;
        box-shadow: 0 1px 3px rgba(35, 164, 21, 0.2) !important;
      }
    }

    .box2 {
      color: #23181599;
    }

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.checkbox {
  border: 1px solid $mainColorB;
  padding: 12px 16px;
  border-radius: 4px;
  color: #231815CC;
  background-color: #FFF;

}

.sign {
  background-color: #E6F0F3;
  border-radius: 4px;
  height: 100px;

  @media screen and (max-width: $main-mobileM) {
    height: 200px;
  }
}

.card {
  @media screen and (max-width: $main-mobileM) {
    width: 100%;
  }

  button {
    @media screen and (max-width: $main-mobileM) {
      width: 100%;
      margin-top: 16px;
    }
  }
}

// 查看文件按鈕樣式 - 參考 button2 風格
.view-document-btn {
  width: auto !important;
  padding: 8px 20px !important;
  height: 36px !important;
  color: $text-light !important;
  background: linear-gradient(90deg, $primary-gold-dark 0%, $primary-gold-light 100%) !important;
  border-radius: 18px !important;
  border: none !important;
  box-shadow: 0 2px 6px rgba(174, 155, 102, 0.2) !important;
  transition: all 0.3s ease !important;
  font-weight: 600 !important;
  font-size: 14px !important;

  &:hover {
    background: linear-gradient(90deg, $primary-gold-darker 0%, $primary-gold-dark 100%) !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 3px 8px rgba(174, 155, 102, 0.3) !important;
  }

  span {
    color: inherit !important;
    font-weight: inherit !important;
  }

  img {
    filter: brightness(0) invert(1);
    margin-left: 6px;
  }
}

// 繳款狀態樣式
.not-pay {
  padding: 6px 18px;
  background-color: rgba(241, 80, 47, 0.15);
  font-size: 16px;
  color: darken($error, 5%);
  border-radius: 6px;
  font-weight: 600;
  border: 1px solid rgba(241, 80, 47, 0.25);
  box-shadow: 0 1px 2px rgba(241, 80, 47, 0.1);
}

.pay-completed {
  padding: 6px 18px;
  background-color: rgba(35, 164, 21, 0.15);
  font-size: 16px;
  color: darken($success, 5%);
  border-radius: 6px;
  font-weight: 600;
  border: 1px solid rgba(35, 164, 21, 0.25);
  box-shadow: 0 1px 2px rgba(35, 164, 21, 0.1);
}

.pay-not-required {
  padding: 6px 18px;
  background-color: rgba(184, 166, 118, 0.25);
  font-size: 16px;
  color: $primary-gold-darker;
  border-radius: 6px;
  font-weight: 600;
  border: 1px solid rgba(184, 166, 118, 0.35);
  box-shadow: 0 1px 2px rgba(174, 155, 102, 0.15);
}

// 簽署狀態按鈕樣式 - 參考 button2 風格
.sign-status-btn {
  padding: 8px 20px;
  height: 36px;
  border-radius: 18px;
  font-size: 14px;
  font-weight: 600;
  border: none;
  cursor: default;
  transition: all 0.3s ease;
  box-shadow: 0 1px 4px rgba(174, 155, 102, 0.15);

  &.unsigned {
    color: $text-light;
    background: linear-gradient(90deg, $error 0%, lighten($error, 5%) 100%);
    box-shadow: 0 2px 6px rgba(241, 80, 47, 0.2);
  }

  &.signed {
    color: $text-light;
    background: linear-gradient(90deg, $success 0%, lighten($success, 5%) 100%);
    box-shadow: 0 2px 6px rgba(35, 164, 21, 0.2);
  }

  &:disabled {
    opacity: 1;
  }
}

// 新的文件資訊區域佈局
.document-info-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px;
  background: rgba(248, 247, 244, 0.6);
  border-radius: 8px;
  border: 1px solid rgba(184, 166, 118, 0.15);

  @media screen and (min-width: 768px) {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    gap: 24px;
  }
}

// 日期資訊區域
.date-info {
  display: flex;
  flex-direction: column;
  gap: 4px;

  @media screen and (min-width: 768px) {
    flex-direction: row;
    align-items: center;
    gap: 8px;
  }

  .date-label {
    font-size: 14px;
    color: $text-secondary;
    font-weight: 500;
  }

  .date-value {
    font-size: 14px;
    color: $text-primary;
    font-weight: 600;
  }
}

// 操作區域
.action-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
  align-items: stretch;

  @media screen and (min-width: 768px) {
    flex-direction: row;
    align-items: center;
    gap: 16px;
  }
}

// 狀態組合
.status-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
  align-items: flex-start;

  @media screen and (min-width: 768px) {
    flex-direction: row;
    align-items: center;
    gap: 8px;
  }

  .status-label {
    font-size: 14px;
    color: $text-secondary;
    font-weight: 500;
    white-space: nowrap;
  }
}

// 狀態徽章
.status-badge {
  padding: 6px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 600;
  text-align: center;
  min-width: 80px;
  transition: all 0.3s ease;

  &.unsigned {
    background: linear-gradient(135deg, rgba(241, 80, 47, 0.15) 0%, rgba(241, 80, 47, 0.08) 100%);
    color: darken($error, 5%);
    border: 1px solid rgba(241, 80, 47, 0.25);
    box-shadow: 0 1px 3px rgba(241, 80, 47, 0.1);
  }

  &.signed {
    background: linear-gradient(135deg, rgba(35, 164, 21, 0.15) 0%, rgba(35, 164, 21, 0.08) 100%);
    color: darken($success, 5%);
    border: 1px solid rgba(35, 164, 21, 0.25);
    box-shadow: 0 1px 3px rgba(35, 164, 21, 0.1);
  }
}

// 簽署操作按鈕
.sign-action-btn {
  min-width: 120px;

  @media screen and (max-width: 767px) {
    width: 100%;
  }
}

// 保持向後兼容性
.pay {
  padding: 6px 18px;
  background-color: rgba(35, 164, 21, 0.15);
  font-size: 16px;
  color: darken($success, 5%);
  border-radius: 6px;
  font-weight: 600;
  border: 1px solid rgba(35, 164, 21, 0.25);
  box-shadow: 0 1px 2px rgba(35, 164, 21, 0.1);
}
