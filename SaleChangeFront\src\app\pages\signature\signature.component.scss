﻿@import "../../../styles.scss";

.signin {
  margin-top: 20px;
  width: 1216px;
  z-index: 3;
  min-height: 550px;
  height: 550px;

  .title {
    font-size: 32px;
    font-weight: 500;
    color: #231815;
    margin-bottom: 16px;
  }

  .pafbox {
    padding: 12.5px 16px;
    width: 100%;
    color: #000000;
    background: #F3F1EA99;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;

    @media screen and (max-width: 700px) {
      font-size: 14px;
    }

    @media screen and (max-width: $main-mobileM) {
      padding: 16px;
      font-size: 16px;
      display: block;
    }

    .box1 {

      .makesure,
      .makesures {
        padding: 4px 12px;
        border-radius: 4px;
        background: $error;
        color: $text-light;
        width: fit-content;
      }

      .realsure,
      .realsures {
        background-color: $success !important;
      }
    }

    .box2 {
      color: #23181599;
    }

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.checkbox {
  border: 1px solid $mainColorB;
  padding: 12px 16px;
  border-radius: 4px;
  color: #231815CC;
  background-color: #FFF;

}

.sign {
  background-color: #E6F0F3;
  border-radius: 4px;
  height: 100px;

  @media screen and (max-width: $main-mobileM) {
    height: 200px;
  }
}

.card {
  @media screen and (max-width: $main-mobileM) {
    width: 100%;
  }

  button {
    @media screen and (max-width: $main-mobileM) {
      width: 100%;
      margin-top: 16px;
    }
  }
}

// 查看文件按鈕樣式
.view-document-btn {
  background: rgba(184, 166, 118, 0.15) !important;
  color: $primary-gold-dark;
  transition: all 0.3s ease;

  &:hover {
    background: rgba(184, 166, 118, 0.25) !important;
    color: $primary-gold-darker;
    transform: translateY(-1px);
  }

  span {
    color: inherit;
  }
}

// 繳款狀態樣式
.not-pay {
  padding: 4px 16px;
  background-color: $error-light;
  font-size: 16px;
  color: $error;
  border-radius: 4px;
}

.pay-completed {
  padding: 4px 16px;
  background-color: $success-light;
  font-size: 16px;
  color: $success;
  border-radius: 4px;
}

.pay-not-required {
  padding: 4px 16px;
  background-color: rgba(184, 166, 118, 0.12);
  font-size: 16px;
  color: $primary-gold-dark;
  border-radius: 4px;
}

// 保持向後兼容性
.pay {
  padding: 4px 16px;
  background-color: $success-light;
  font-size: 16px;
  color: $success;
  border-radius: 4px;
}
