import { Component, OnInit, ViewChild } from '@angular/core';
import { Router, RouterModule } from '@angular/router';
import { HttpResponse } from '@angular/common/http';
import { DialogModule } from 'primeng/dialog';
import { CommonModule } from '@angular/common';
import { CheckboxModule } from 'primeng/checkbox';
import { FormsModule } from '@angular/forms';
import { SignaturePadComponent } from '../../components/signature-pad/signature-pad.component';
import { DialogPopupComponent } from '../../components/dialog-popup/dialog-popup.component';
import { ToastMessage } from '../../shared/services/message.service';
import { MessageService } from 'primeng/api';
import { UtilityService } from '../../shared/services/utility.service';
import { ToastModule } from 'primeng/toast';
import { ContentDialog } from '../../../model/choice.model';
import { GetFinalDocRes } from '../../../services/api/models';
import { DateFormatPipe } from '../../shared/pipes/date-format.pipe';
import { FinalDocumentService } from '../../../services/api/services/final-document.service';
import { EEvent, EventService } from '../../shared/services/event.service';
import { LoadingService } from '../../shared/services/loading.service';
import { FileService } from '../../../services/File.service';

@Component({
  selector: 'app-signature',
  standalone: true,
  imports: [
    RouterModule, DialogModule, CommonModule,
    CheckboxModule, CommonModule, FormsModule,
    ToastModule,
    DialogPopupComponent, SignaturePadComponent, DateFormatPipe
  ],
  providers: [
    MessageService,
    ToastMessage,
  ],
  templateUrl: './signature.component.html',
  styleUrl: './signature.component.scss'
})
export class SignatureComponent implements OnInit {
  @ViewChild('signaturePadComponent') signaturePadComponent!: SignaturePadComponent;

  signature: string = ""

  docsData: GetFinalDocRes[] = []
  agree: boolean = false;

  textData: ContentDialog = {
    title: 'sign',
    header: "確認簽署",
    content: "",
    titleButtonLeft: "返回上頁",
    titleButtonRight: "確認"
  }

  visible: boolean = false;
  currentDocId: number = -1
  selectedDoc = {} as GetFinalDocRes;
  constructor(
    private _finalDocService: FinalDocumentService,
    private _toastService: ToastMessage,
    private utilityService: UtilityService,
    private _eventService: EventService,
    private _fileService: FileService,
    private router: Router
  ) { }

  ngOnInit(): void {
    this.getFinalDocBefore()
  }

  handleSignatureData(signatureDataUrl: string): void {
    this.signature = signatureDataUrl;
  }

  getSignatureFromChild() {
    this.signature = this.signaturePadComponent.getSignatureValue();
    return this.signature
  }

  updateSign() {
    if (this.agree) {
      LoadingService.loading(true);
      this._finalDocService.apiFinalDocumentUpdateSignPost$Json({
        body: {
          CSign: this.getSignatureFromChild(),
          CFinalDocID: this.currentDocId!
        }
      }).subscribe(res => {
        if (res.StatusCode === 0) {
          LoadingService.loading(false);
          this.getFinalDocAfter()
          this._toastService.showSucessMSG('更新成功');
          setTimeout(function () {
            window.location.reload();
          }, 500)
        }
        this.visible = false;
      })
    } else {
      this._toastService.showErrorMSG("[我已閱讀聲明] 必填");
      this.visible = true;
    }
  }

  getFinalDocBefore() {
    this._finalDocService.apiFinalDocumentGetFinalDocBeforePost$Json({}).subscribe(res => {
      if (res.StatusCode == 0) {
        this.docsData = res.Entries! ?? []
      }
    })
  }

  getFinalDocAfter() {
    this._finalDocService.apiFinalDocumentGetFinalDocAfterPost$Json({}).subscribe(res => {
      if (res.StatusCode == 0) {
        this.docsData = res.Entries! ?? []
        this._eventService.push({
          action: EEvent.UPDATE_SIGN,
          payload: res.Entries!
        })
      }
    })
  }
  downloadFile(docData: GetFinalDocRes) {
    if (!docData.CLink || !docData.CDocumentName) {
      console.error('Document link or name is not available');
      return;
    }
    this._fileService.getFile(docData.CLink, docData.CDocumentName).subscribe({
      next: (response: HttpResponse<Blob>) => {
        this.utilityService.DownloadFile(response);
      },
      error: (error) => {
        console.error('下載文件失敗:', error);
        this._toastService.showErrorMSG('下載文件失敗');
        // 如果 FileService 失敗，回退到原來的方法
        window.open((docData.CLink + '?date=' + new Date().getTime()), '_blank');
      }
    });
  }

  showDialog(docData: GetFinalDocRes) {
    this.visible = true
    this.currentDocId = docData.CID!
    this.selectedDoc = docData
  }

  close() {
    this.visible = false;
    this.currentDocId = -1
    this.selectedDoc = {} as GetFinalDocRes;
  }

  actionButtonRight() {
    this.updateSign()
  }

  actionButtonLeft() {
    this.visible = false;
    this.agree = false
    this.currentDocId = -1
    this.signaturePadComponent.clear()
  }

  goback() {
    this.router.navigateByUrl("/");
  }
}
