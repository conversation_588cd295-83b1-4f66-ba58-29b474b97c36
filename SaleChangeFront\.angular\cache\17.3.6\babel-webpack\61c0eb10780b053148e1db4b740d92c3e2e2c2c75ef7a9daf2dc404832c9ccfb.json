{"ast": null, "code": "import { environment } from '../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nexport class UtilityService {\n  constructor() {\n    this.BASE_FILE = environment.BASE_URL_API;\n    /**\n     * 從 HttpResponse 下載檔案\n     * @param data HttpResponse<Blob> 物件\n     */\n    this.downloadFile = data => {\n      if (data && data.body) {\n        const downloadedFile = new Blob([data.body], {\n          type: data.body.type\n        });\n        const a = document.createElement('a');\n        a.setAttribute('style', 'display:none;');\n        document.body.appendChild(a);\n        const fileName = this.parseFileName(data.headers.get('Content-Disposition'));\n        a.download = fileName;\n        a.href = URL.createObjectURL(downloadedFile);\n        a.target = '_blank';\n        a.click();\n        document.body.removeChild(a);\n        URL.revokeObjectURL(a.href);\n      }\n    };\n  }\n  downloadFileFromUrl(file) {\n    const xhr = new XMLHttpRequest();\n    xhr.open('GET', `${this.BASE_FILE}${file.CFile}`, true);\n    xhr.responseType = 'blob';\n    xhr.onload = () => {\n      if (xhr.status === 200) {\n        const url = window.URL.createObjectURL(xhr.response);\n        const link = document.createElement('a');\n        link.href = url;\n        link.download = file.CName;\n        link.style.display = 'none';\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        window.URL.revokeObjectURL(url);\n      }\n    };\n    xhr.send();\n  }\n  getFileNameFromUrl(url) {\n    const parts = url.split('/');\n    const fileName = parts.pop();\n    return fileName;\n  }\n  downloadFileFullUrl(file) {\n    const xhr = new XMLHttpRequest();\n    xhr.open('GET', `${file.CFile}?date=${new Date().getTime()}`, true);\n    xhr.responseType = 'blob';\n    xhr.onload = () => {\n      if (xhr.status === 200) {\n        const url = window.URL.createObjectURL(xhr.response);\n        const link = document.createElement('a');\n        link.href = url;\n        link.download = file.CName;\n        link.style.display = 'none';\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        window.URL.revokeObjectURL(url);\n      }\n    };\n    xhr.send();\n  }\n  /**\n   * 解析 Content-Disposition header 中的檔名\n   * @param header Content-Disposition header 值\n   * @param def 預設檔名\n   * @returns 解析出的檔名\n   */\n  parseFileName(header, def = 'download') {\n    if (!header) {\n      return def;\n    }\n    try {\n      const parts = header.split(';');\n      const filenamePart = parts.find(part => part.trim().startsWith('filename'));\n      if (filenamePart) {\n        const name = filenamePart.split('=')[1];\n        return decodeURI(name.replace(/\"/g, '')).replace('UTF-8\\'\\'', ''); // 注意中文請在服務端加入URL編碼\n      }\n    } catch (error) {\n      console.error('解析檔名時發生錯誤:', error);\n    }\n    return def;\n  }\n  /**\n   * 將 Base64 字串下載為檔案\n   * @param base64String Base64 編碼的字串\n   * @param fileName 檔案名稱\n   */\n  downloadBase64AsFile(base64String, fileName) {\n    try {\n      // 將Base64字串轉換為Blob\n      const byteCharacters = atob(base64String);\n      const byteNumbers = new Array(byteCharacters.length);\n      for (let i = 0; i < byteCharacters.length; i++) {\n        byteNumbers[i] = byteCharacters.charCodeAt(i);\n      }\n      const byteArray = new Uint8Array(byteNumbers);\n      const blob = new Blob([byteArray], {\n        type: 'application/octet-stream'\n      });\n      // 創建URL\n      const url = URL.createObjectURL(blob);\n      // 創建a標籤並設置屬性\n      const a = document.createElement('a');\n      a.href = url;\n      a.download = fileName;\n      // 模擬點擊\n      document.body.appendChild(a);\n      a.click();\n      document.body.removeChild(a);\n      // 釋放URL資源\n      URL.revokeObjectURL(url);\n    } catch (error) {\n      console.error('Base64 下載時發生錯誤:', error);\n    }\n  }\n  /**\n   * 截斷字串並加上省略號\n   * @param str 要截斷的字串\n   * @param maxLength 最大長度\n   * @returns 截斷後的字串\n   */\n  truncateString(str, maxLength) {\n    if (!str) {\n      return str;\n    }\n    if (str.length > maxLength) {\n      return str.slice(0, maxLength) + '...';\n    } else {\n      return str;\n    }\n  }\n  /**\n   * 智能檔名處理 - 根據檔案類型添加適當的副檔名\n   * @param fileName 原始檔名\n   * @param mimeType MIME 類型\n   * @returns 處理後的檔名\n   */\n  getFileNameWithExtension(fileName, mimeType) {\n    let finalFileName = fileName || 'document';\n    // 如果檔名已有副檔名，直接返回\n    if (finalFileName.includes('.')) {\n      return finalFileName;\n    }\n    // 根據 MIME type 推斷副檔名\n    const mimeToExtension = {\n      'application/pdf': '.pdf',\n      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': '.docx',\n      'application/msword': '.doc',\n      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': '.xlsx',\n      'application/vnd.ms-excel': '.xls',\n      'image/png': '.png',\n      'image/jpeg': '.jpg',\n      'image/gif': '.gif',\n      'text/plain': '.txt',\n      'application/zip': '.zip',\n      'application/x-rar-compressed': '.rar'\n    };\n    const extension = mimeToExtension[mimeType];\n    if (extension) {\n      finalFileName += extension;\n    }\n    return finalFileName;\n  }\n  /**\n   * 處理表單輸入 - 空值轉為 0\n   * @param event 輸入事件\n   */\n  handleInput(event) {\n    if (event.target.value === '') {\n      event.target.value = 0;\n    }\n  }\n  static #_ = this.ɵfac = function UtilityService_Factory(t) {\n    return new (t || UtilityService)();\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: UtilityService,\n    factory: UtilityService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["environment", "UtilityService", "constructor", "BASE_FILE", "BASE_URL_API", "downloadFile", "data", "body", "downloadedFile", "Blob", "type", "a", "document", "createElement", "setAttribute", "append<PERSON><PERSON><PERSON>", "fileName", "parseFileName", "headers", "get", "download", "href", "URL", "createObjectURL", "target", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "downloadFileFromUrl", "file", "xhr", "XMLHttpRequest", "open", "CFile", "responseType", "onload", "status", "url", "window", "response", "link", "CName", "style", "display", "send", "getFileNameFromUrl", "parts", "split", "pop", "downloadFileFullUrl", "Date", "getTime", "header", "def", "filenamePart", "find", "part", "trim", "startsWith", "name", "decodeURI", "replace", "error", "console", "downloadBase64AsFile", "base64String", "byteCharacters", "atob", "byteNumbers", "Array", "length", "i", "charCodeAt", "byteArray", "Uint8Array", "blob", "truncateString", "str", "max<PERSON><PERSON><PERSON>", "slice", "getFileNameWithExtension", "mimeType", "finalFileName", "includes", "mimeToExtension", "extension", "handleInput", "event", "value", "_", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\SaleChangeFront\\src\\app\\shared\\services\\utility.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpResponse } from '@angular/common/http';\r\nimport { environment } from '../../../environments/environment';\r\nimport { BuildCaseGetFileRespone } from '../../../services/api/models';\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class UtilityService {\r\n  readonly BASE_FILE = environment.BASE_URL_API;\r\n\r\n  constructor() { }\r\n\r\n  downloadFileFromUrl(file: BuildCaseGetFileRespone) {\r\n    const xhr = new XMLHttpRequest();\r\n    xhr.open('GET', `${this.BASE_FILE}${file.CFile}`\r\n      , true);\r\n    xhr.responseType = 'blob';\r\n    xhr.onload = () => {\r\n      if (xhr.status === 200) {\r\n        const url = window.URL.createObjectURL(xhr.response);\r\n        const link = document.createElement('a');\r\n        link.href = url;\r\n        link.download = file.CName!;\r\n        link.style.display = 'none';\r\n        document.body.appendChild(link);\r\n        link.click();\r\n        document.body.removeChild(link);\r\n        window.URL.revokeObjectURL(url);\r\n      }\r\n    };\r\n    xhr.send();\r\n  }\r\n\r\n  getFileNameFromUrl(url: string) {\r\n    const parts = url.split('/');\r\n    const fileName = parts.pop();\r\n    return fileName;\r\n  }\r\n\r\n  downloadFileFullUrl(file: { CFile: string | any; CName: string | any }) {\r\n    const xhr = new XMLHttpRequest();\r\n    xhr.open('GET', `${file.CFile}?date=${new Date().getTime()}`\r\n      , true);\r\n    xhr.responseType = 'blob';\r\n    xhr.onload = () => {\r\n      if (xhr.status === 200) {\r\n        const url = window.URL.createObjectURL(xhr.response);\r\n        const link = document.createElement('a');\r\n        link.href = url;\r\n        link.download = file.CName!;\r\n        link.style.display = 'none';\r\n        document.body.appendChild(link);\r\n        link.click();\r\n        document.body.removeChild(link);\r\n        window.URL.revokeObjectURL(url);\r\n      }\r\n    };\r\n    xhr.send();\r\n  }\r\n\r\n  /**\r\n   * 從 HttpResponse 下載檔案\r\n   * @param data HttpResponse<Blob> 物件\r\n   */\r\n  downloadFile = (data: HttpResponse<Blob>) => {\r\n    if (data && data.body) {\r\n      const downloadedFile = new Blob([data.body], { type: data.body.type });\r\n      const a = document.createElement('a');\r\n      a.setAttribute('style', 'display:none;');\r\n      document.body.appendChild(a);\r\n      const fileName = this.parseFileName(data.headers.get('Content-Disposition'));\r\n      a.download = fileName;\r\n      a.href = URL.createObjectURL(downloadedFile);\r\n      a.target = '_blank';\r\n      a.click();\r\n      document.body.removeChild(a);\r\n      URL.revokeObjectURL(a.href);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 解析 Content-Disposition header 中的檔名\r\n   * @param header Content-Disposition header 值\r\n   * @param def 預設檔名\r\n   * @returns 解析出的檔名\r\n   */\r\n  private parseFileName(header: string | null, def: string = 'download'): string {\r\n    if (!header) {\r\n      return def;\r\n    }\r\n    try {\r\n      const parts = header.split(';');\r\n      const filenamePart = parts.find(part => part.trim().startsWith('filename'));\r\n      if (filenamePart) {\r\n        const name = filenamePart.split('=')[1];\r\n        return decodeURI(name.replace(/\"/g, '')).replace('UTF-8\\'\\'', ''); // 注意中文請在服務端加入URL編碼\r\n      }\r\n    } catch (error) {\r\n      console.error('解析檔名時發生錯誤:', error);\r\n    }\r\n    return def;\r\n  }\r\n\r\n  /**\r\n   * 將 Base64 字串下載為檔案\r\n   * @param base64String Base64 編碼的字串\r\n   * @param fileName 檔案名稱\r\n   */\r\n  downloadBase64AsFile(base64String: string, fileName: string) {\r\n    try {\r\n      // 將Base64字串轉換為Blob\r\n      const byteCharacters = atob(base64String);\r\n      const byteNumbers = new Array(byteCharacters.length);\r\n      for (let i = 0; i < byteCharacters.length; i++) {\r\n        byteNumbers[i] = byteCharacters.charCodeAt(i);\r\n      }\r\n      const byteArray = new Uint8Array(byteNumbers);\r\n      const blob = new Blob([byteArray], { type: 'application/octet-stream' });\r\n\r\n      // 創建URL\r\n      const url = URL.createObjectURL(blob);\r\n\r\n      // 創建a標籤並設置屬性\r\n      const a = document.createElement('a');\r\n      a.href = url;\r\n      a.download = fileName;\r\n\r\n      // 模擬點擊\r\n      document.body.appendChild(a);\r\n      a.click();\r\n      document.body.removeChild(a);\r\n\r\n      // 釋放URL資源\r\n      URL.revokeObjectURL(url);\r\n    } catch (error) {\r\n      console.error('Base64 下載時發生錯誤:', error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 截斷字串並加上省略號\r\n   * @param str 要截斷的字串\r\n   * @param maxLength 最大長度\r\n   * @returns 截斷後的字串\r\n   */\r\n  truncateString(str: string, maxLength: number): string {\r\n    if (!str) {\r\n      return str;\r\n    }\r\n    if (str.length > maxLength) {\r\n      return str.slice(0, maxLength) + '...';\r\n    } else {\r\n      return str;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 智能檔名處理 - 根據檔案類型添加適當的副檔名\r\n   * @param fileName 原始檔名\r\n   * @param mimeType MIME 類型\r\n   * @returns 處理後的檔名\r\n   */\r\n  getFileNameWithExtension(fileName: string, mimeType: string): string {\r\n    let finalFileName = fileName || 'document';\r\n    \r\n    // 如果檔名已有副檔名，直接返回\r\n    if (finalFileName.includes('.')) {\r\n      return finalFileName;\r\n    }\r\n    \r\n    // 根據 MIME type 推斷副檔名\r\n    const mimeToExtension: { [key: string]: string } = {\r\n      'application/pdf': '.pdf',\r\n      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': '.docx',\r\n      'application/msword': '.doc',\r\n      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': '.xlsx',\r\n      'application/vnd.ms-excel': '.xls',\r\n      'image/png': '.png',\r\n      'image/jpeg': '.jpg',\r\n      'image/gif': '.gif',\r\n      'text/plain': '.txt',\r\n      'application/zip': '.zip',\r\n      'application/x-rar-compressed': '.rar'\r\n    };\r\n    \r\n    const extension = mimeToExtension[mimeType];\r\n    if (extension) {\r\n      finalFileName += extension;\r\n    }\r\n    \r\n    return finalFileName;\r\n  }\r\n\r\n  /**\r\n   * 處理表單輸入 - 空值轉為 0\r\n   * @param event 輸入事件\r\n   */\r\n  handleInput(event: any) {\r\n    if (event.target.value === '') {\r\n      event.target.value = 0;\r\n    }\r\n  }\r\n}\r\n"], "mappings": "AAEA,SAASA,WAAW,QAAQ,mCAAmC;;AAK/D,OAAM,MAAOC,cAAc;EAGzBC,YAAA;IAFS,KAAAC,SAAS,GAAGH,WAAW,CAACI,YAAY;IAoD7C;;;;IAIA,KAAAC,YAAY,GAAIC,IAAwB,IAAI;MAC1C,IAAIA,IAAI,IAAIA,IAAI,CAACC,IAAI,EAAE;QACrB,MAAMC,cAAc,GAAG,IAAIC,IAAI,CAAC,CAACH,IAAI,CAACC,IAAI,CAAC,EAAE;UAAEG,IAAI,EAAEJ,IAAI,CAACC,IAAI,CAACG;QAAI,CAAE,CAAC;QACtE,MAAMC,CAAC,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;QACrCF,CAAC,CAACG,YAAY,CAAC,OAAO,EAAE,eAAe,CAAC;QACxCF,QAAQ,CAACL,IAAI,CAACQ,WAAW,CAACJ,CAAC,CAAC;QAC5B,MAAMK,QAAQ,GAAG,IAAI,CAACC,aAAa,CAACX,IAAI,CAACY,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC,CAAC;QAC5ER,CAAC,CAACS,QAAQ,GAAGJ,QAAQ;QACrBL,CAAC,CAACU,IAAI,GAAGC,GAAG,CAACC,eAAe,CAACf,cAAc,CAAC;QAC5CG,CAAC,CAACa,MAAM,GAAG,QAAQ;QACnBb,CAAC,CAACc,KAAK,EAAE;QACTb,QAAQ,CAACL,IAAI,CAACmB,WAAW,CAACf,CAAC,CAAC;QAC5BW,GAAG,CAACK,eAAe,CAAChB,CAAC,CAACU,IAAI,CAAC;;IAE/B,CAAC;EApEe;EAEhBO,mBAAmBA,CAACC,IAA6B;IAC/C,MAAMC,GAAG,GAAG,IAAIC,cAAc,EAAE;IAChCD,GAAG,CAACE,IAAI,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC7B,SAAS,GAAG0B,IAAI,CAACI,KAAK,EAAE,EAC5C,IAAI,CAAC;IACTH,GAAG,CAACI,YAAY,GAAG,MAAM;IACzBJ,GAAG,CAACK,MAAM,GAAG,MAAK;MAChB,IAAIL,GAAG,CAACM,MAAM,KAAK,GAAG,EAAE;QACtB,MAAMC,GAAG,GAAGC,MAAM,CAAChB,GAAG,CAACC,eAAe,CAACO,GAAG,CAACS,QAAQ,CAAC;QACpD,MAAMC,IAAI,GAAG5B,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;QACxC2B,IAAI,CAACnB,IAAI,GAAGgB,GAAG;QACfG,IAAI,CAACpB,QAAQ,GAAGS,IAAI,CAACY,KAAM;QAC3BD,IAAI,CAACE,KAAK,CAACC,OAAO,GAAG,MAAM;QAC3B/B,QAAQ,CAACL,IAAI,CAACQ,WAAW,CAACyB,IAAI,CAAC;QAC/BA,IAAI,CAACf,KAAK,EAAE;QACZb,QAAQ,CAACL,IAAI,CAACmB,WAAW,CAACc,IAAI,CAAC;QAC/BF,MAAM,CAAChB,GAAG,CAACK,eAAe,CAACU,GAAG,CAAC;;IAEnC,CAAC;IACDP,GAAG,CAACc,IAAI,EAAE;EACZ;EAEAC,kBAAkBA,CAACR,GAAW;IAC5B,MAAMS,KAAK,GAAGT,GAAG,CAACU,KAAK,CAAC,GAAG,CAAC;IAC5B,MAAM/B,QAAQ,GAAG8B,KAAK,CAACE,GAAG,EAAE;IAC5B,OAAOhC,QAAQ;EACjB;EAEAiC,mBAAmBA,CAACpB,IAAkD;IACpE,MAAMC,GAAG,GAAG,IAAIC,cAAc,EAAE;IAChCD,GAAG,CAACE,IAAI,CAAC,KAAK,EAAE,GAAGH,IAAI,CAACI,KAAK,SAAS,IAAIiB,IAAI,EAAE,CAACC,OAAO,EAAE,EAAE,EACxD,IAAI,CAAC;IACTrB,GAAG,CAACI,YAAY,GAAG,MAAM;IACzBJ,GAAG,CAACK,MAAM,GAAG,MAAK;MAChB,IAAIL,GAAG,CAACM,MAAM,KAAK,GAAG,EAAE;QACtB,MAAMC,GAAG,GAAGC,MAAM,CAAChB,GAAG,CAACC,eAAe,CAACO,GAAG,CAACS,QAAQ,CAAC;QACpD,MAAMC,IAAI,GAAG5B,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;QACxC2B,IAAI,CAACnB,IAAI,GAAGgB,GAAG;QACfG,IAAI,CAACpB,QAAQ,GAAGS,IAAI,CAACY,KAAM;QAC3BD,IAAI,CAACE,KAAK,CAACC,OAAO,GAAG,MAAM;QAC3B/B,QAAQ,CAACL,IAAI,CAACQ,WAAW,CAACyB,IAAI,CAAC;QAC/BA,IAAI,CAACf,KAAK,EAAE;QACZb,QAAQ,CAACL,IAAI,CAACmB,WAAW,CAACc,IAAI,CAAC;QAC/BF,MAAM,CAAChB,GAAG,CAACK,eAAe,CAACU,GAAG,CAAC;;IAEnC,CAAC;IACDP,GAAG,CAACc,IAAI,EAAE;EACZ;EAsBA;;;;;;EAMQ3B,aAAaA,CAACmC,MAAqB,EAAEC,GAAA,GAAc,UAAU;IACnE,IAAI,CAACD,MAAM,EAAE;MACX,OAAOC,GAAG;;IAEZ,IAAI;MACF,MAAMP,KAAK,GAAGM,MAAM,CAACL,KAAK,CAAC,GAAG,CAAC;MAC/B,MAAMO,YAAY,GAAGR,KAAK,CAACS,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACC,IAAI,EAAE,CAACC,UAAU,CAAC,UAAU,CAAC,CAAC;MAC3E,IAAIJ,YAAY,EAAE;QAChB,MAAMK,IAAI,GAAGL,YAAY,CAACP,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACvC,OAAOa,SAAS,CAACD,IAAI,CAACE,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,CAACA,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC,CAAC;;KAEtE,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;;IAEpC,OAAOT,GAAG;EACZ;EAEA;;;;;EAKAW,oBAAoBA,CAACC,YAAoB,EAAEjD,QAAgB;IACzD,IAAI;MACF;MACA,MAAMkD,cAAc,GAAGC,IAAI,CAACF,YAAY,CAAC;MACzC,MAAMG,WAAW,GAAG,IAAIC,KAAK,CAACH,cAAc,CAACI,MAAM,CAAC;MACpD,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,cAAc,CAACI,MAAM,EAAEC,CAAC,EAAE,EAAE;QAC9CH,WAAW,CAACG,CAAC,CAAC,GAAGL,cAAc,CAACM,UAAU,CAACD,CAAC,CAAC;;MAE/C,MAAME,SAAS,GAAG,IAAIC,UAAU,CAACN,WAAW,CAAC;MAC7C,MAAMO,IAAI,GAAG,IAAIlE,IAAI,CAAC,CAACgE,SAAS,CAAC,EAAE;QAAE/D,IAAI,EAAE;MAA0B,CAAE,CAAC;MAExE;MACA,MAAM2B,GAAG,GAAGf,GAAG,CAACC,eAAe,CAACoD,IAAI,CAAC;MAErC;MACA,MAAMhE,CAAC,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACrCF,CAAC,CAACU,IAAI,GAAGgB,GAAG;MACZ1B,CAAC,CAACS,QAAQ,GAAGJ,QAAQ;MAErB;MACAJ,QAAQ,CAACL,IAAI,CAACQ,WAAW,CAACJ,CAAC,CAAC;MAC5BA,CAAC,CAACc,KAAK,EAAE;MACTb,QAAQ,CAACL,IAAI,CAACmB,WAAW,CAACf,CAAC,CAAC;MAE5B;MACAW,GAAG,CAACK,eAAe,CAACU,GAAG,CAAC;KACzB,CAAC,OAAOyB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;;EAE3C;EAEA;;;;;;EAMAc,cAAcA,CAACC,GAAW,EAAEC,SAAiB;IAC3C,IAAI,CAACD,GAAG,EAAE;MACR,OAAOA,GAAG;;IAEZ,IAAIA,GAAG,CAACP,MAAM,GAAGQ,SAAS,EAAE;MAC1B,OAAOD,GAAG,CAACE,KAAK,CAAC,CAAC,EAAED,SAAS,CAAC,GAAG,KAAK;KACvC,MAAM;MACL,OAAOD,GAAG;;EAEd;EAEA;;;;;;EAMAG,wBAAwBA,CAAChE,QAAgB,EAAEiE,QAAgB;IACzD,IAAIC,aAAa,GAAGlE,QAAQ,IAAI,UAAU;IAE1C;IACA,IAAIkE,aAAa,CAACC,QAAQ,CAAC,GAAG,CAAC,EAAE;MAC/B,OAAOD,aAAa;;IAGtB;IACA,MAAME,eAAe,GAA8B;MACjD,iBAAiB,EAAE,MAAM;MACzB,yEAAyE,EAAE,OAAO;MAClF,oBAAoB,EAAE,MAAM;MAC5B,mEAAmE,EAAE,OAAO;MAC5E,0BAA0B,EAAE,MAAM;MAClC,WAAW,EAAE,MAAM;MACnB,YAAY,EAAE,MAAM;MACpB,WAAW,EAAE,MAAM;MACnB,YAAY,EAAE,MAAM;MACpB,iBAAiB,EAAE,MAAM;MACzB,8BAA8B,EAAE;KACjC;IAED,MAAMC,SAAS,GAAGD,eAAe,CAACH,QAAQ,CAAC;IAC3C,IAAII,SAAS,EAAE;MACbH,aAAa,IAAIG,SAAS;;IAG5B,OAAOH,aAAa;EACtB;EAEA;;;;EAIAI,WAAWA,CAACC,KAAU;IACpB,IAAIA,KAAK,CAAC/D,MAAM,CAACgE,KAAK,KAAK,EAAE,EAAE;MAC7BD,KAAK,CAAC/D,MAAM,CAACgE,KAAK,GAAG,CAAC;;EAE1B;EAAC,QAAAC,CAAA,G;qBAlMUxF,cAAc;EAAA;EAAA,QAAAyF,EAAA,G;WAAdzF,cAAc;IAAA0F,OAAA,EAAd1F,cAAc,CAAA2F,IAAA;IAAAC,UAAA,EAFb;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}