{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { DialogModule } from 'primeng/dialog';\nimport { CommonModule } from '@angular/common';\nimport { CheckboxModule } from 'primeng/checkbox';\nimport { FormsModule } from '@angular/forms';\nimport { SignaturePadComponent } from '../../components/signature-pad/signature-pad.component';\nimport { DialogPopupComponent } from '../../components/dialog-popup/dialog-popup.component';\nimport { ToastMessage } from '../../shared/services/message.service';\nimport { MessageService } from 'primeng/api';\nimport { ToastModule } from 'primeng/toast';\nimport { DateFormatPipe } from '../../shared/pipes/date-format.pipe';\nimport { EEvent } from '../../shared/services/event.service';\nimport { LoadingService } from '../../shared/services/loading.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../services/api/services/final-document.service\";\nimport * as i2 from \"../../shared/services/message.service\";\nimport * as i3 from \"../../shared/services/utility.service\";\nimport * as i4 from \"../../shared/services/event.service\";\nimport * as i5 from \"../../../services/File.service\";\nimport * as i6 from \"@angular/router\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"primeng/checkbox\";\nimport * as i9 from \"@angular/forms\";\nimport * as i10 from \"primeng/toast\";\nconst _c0 = [\"signaturePadComponent\"];\nconst _c1 = () => ({\n  \"width\": \"22rem\"\n});\nconst _c2 = a0 => ({\n  \"realsures\": a0\n});\nfunction SignatureComponent_Conditional_6_ng_container_0_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"dateFormat\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const docData_r3 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\\u7522\\u6A94\\u65E5\\u671F: \", docData_r3.CCreateDT ? i0.ɵɵpipeBind1(2, 1, docData_r3.CCreateDT) : \"\", \"\");\n  }\n}\nfunction SignatureComponent_Conditional_6_ng_container_0_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"dateFormat\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const docData_r3 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\\u78BA\\u8A8D\\u65E5\\u671F: \", docData_r3.CSignDate ? i0.ɵɵpipeBind1(2, 1, docData_r3.CSignDate) : \"\", \"\");\n  }\n}\nfunction SignatureComponent_Conditional_6_ng_container_0_span_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 36);\n    i0.ɵɵtext(1, \"\\u672A\\u7E73\\u6B3E\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SignatureComponent_Conditional_6_ng_container_0_span_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 37);\n    i0.ɵɵtext(1, \"\\u5DF2\\u7E73\\u6B3E\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SignatureComponent_Conditional_6_ng_container_0_span_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 37);\n    i0.ɵɵtext(1, \"\\u7121\\u9808\\u4ED8\\u6B3E\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SignatureComponent_Conditional_6_ng_container_0_button_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function SignatureComponent_Conditional_6_ng_container_0_button_22_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const docData_r3 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.showDialog(docData_r3));\n    });\n    i0.ɵɵtext(1, \"\\u524D\\u5F80\\u7C3D\\u7F72\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SignatureComponent_Conditional_6_ng_container_0_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 39);\n  }\n}\nfunction SignatureComponent_Conditional_6_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 21)(2, \"div\", 22)(3, \"div\", 23)(4, \"div\", 24)(5, \"span\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(7, \"div\", 25);\n    i0.ɵɵtemplate(8, SignatureComponent_Conditional_6_ng_container_0_div_8_Template, 3, 3, \"div\", 26)(9, SignatureComponent_Conditional_6_ng_container_0_div_9_Template, 3, 3, \"div\", 26);\n    i0.ɵɵelementStart(10, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function SignatureComponent_Conditional_6_ng_container_0_Template_button_click_10_listener() {\n      const docData_r3 = i0.ɵɵrestoreView(_r2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.downloadFile(docData_r3));\n    });\n    i0.ɵɵelementStart(11, \"span\", 12);\n    i0.ɵɵtext(12, \"\\u67E5\\u770B\\u6587\\u4EF6\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(13, \"img\", 28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 29)(15, \"span\", 30);\n    i0.ɵɵtext(16, \"\\u7E73\\u6B3E\\u72C0\\u614B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(17, SignatureComponent_Conditional_6_ng_container_0_span_17_Template, 2, 0, \"span\", 31)(18, SignatureComponent_Conditional_6_ng_container_0_span_18_Template, 2, 0, \"span\", 32)(19, SignatureComponent_Conditional_6_ng_container_0_span_19_Template, 2, 0, \"span\", 32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"div\", 33);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(22, SignatureComponent_Conditional_6_ng_container_0_button_22_Template, 2, 0, \"button\", 34)(23, SignatureComponent_Conditional_6_ng_container_0_div_23_Template, 1, 0, \"div\", 35);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const docData_r3 = ctx.$implicit;\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", docData_r3 == null ? null : docData_r3.CDocumentName, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !docData_r3.CSign);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", docData_r3.CSign);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", docData_r3.CPayStatus == 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", docData_r3.CPayStatus == 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", docData_r3.CPayStatus == 2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(10, _c2, docData_r3.CSign));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", docData_r3.CSign ? \"\\u5DF2\\u7C3D\\u7F72\" : \"\\u672A\\u7C3D\\u7F72\", \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !docData_r3.CSign);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", docData_r3.CSign);\n  }\n}\nfunction SignatureComponent_Conditional_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, SignatureComponent_Conditional_6_ng_container_0_Template, 24, 12, \"ng-container\", 20);\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.docsData);\n  }\n}\nexport class SignatureComponent {\n  constructor(_finalDocService, _toastService, utilityService, _eventService, _fileService, router) {\n    this._finalDocService = _finalDocService;\n    this._toastService = _toastService;\n    this.utilityService = utilityService;\n    this._eventService = _eventService;\n    this._fileService = _fileService;\n    this.router = router;\n    this.signature = \"\";\n    this.docsData = [];\n    this.agree = false;\n    this.textData = {\n      title: 'sign',\n      header: \"確認簽署\",\n      content: \"\",\n      titleButtonLeft: \"返回上頁\",\n      titleButtonRight: \"確認\"\n    };\n    this.visible = false;\n    this.currentDocId = -1;\n    this.selectedDoc = {};\n  }\n  ngOnInit() {\n    this.getFinalDocBefore();\n  }\n  handleSignatureData(signatureDataUrl) {\n    this.signature = signatureDataUrl;\n  }\n  getSignatureFromChild() {\n    this.signature = this.signaturePadComponent.getSignatureValue();\n    return this.signature;\n  }\n  updateSign() {\n    if (this.agree) {\n      LoadingService.loading(true);\n      this._finalDocService.apiFinalDocumentUpdateSignPost$Json({\n        body: {\n          CSign: this.getSignatureFromChild(),\n          CFinalDocID: this.currentDocId\n        }\n      }).subscribe(res => {\n        if (res.StatusCode === 0) {\n          LoadingService.loading(false);\n          this.getFinalDocAfter();\n          this._toastService.showSucessMSG('更新成功');\n          setTimeout(function () {\n            window.location.reload();\n          }, 500);\n        }\n        this.visible = false;\n      });\n    } else {\n      this._toastService.showErrorMSG(\"[我已閱讀聲明] 必填\");\n      this.visible = true;\n    }\n  }\n  getFinalDocBefore() {\n    this._finalDocService.apiFinalDocumentGetFinalDocBeforePost$Json({}).subscribe(res => {\n      if (res.StatusCode == 0) {\n        this.docsData = res.Entries ?? [];\n      }\n    });\n  }\n  getFinalDocAfter() {\n    this._finalDocService.apiFinalDocumentGetFinalDocAfterPost$Json({}).subscribe(res => {\n      if (res.StatusCode == 0) {\n        this.docsData = res.Entries ?? [];\n        this._eventService.push({\n          action: \"UPDATE_SIGN\" /* EEvent.UPDATE_SIGN */,\n          payload: res.Entries\n        });\n      }\n    });\n  }\n  downloadFile(docData) {\n    if (!docData.CLink || !docData.CDocumentName) {\n      console.error('Document link or name is not available');\n      return;\n    }\n    this._fileService.getFile(docData.CLink, docData.CDocumentName).subscribe({\n      next: response => {\n        const {\n          blob,\n          filename\n        } = response;\n        // 使用 UtilityService 的智能檔名處理\n        const baseFileName = filename || docData.CDocumentName || 'document';\n        const finalFileName = this.utilityService.getFileNameWithExtension(baseFileName, blob.type);\n        // 使用簡化的下載邏輯\n        this.utilityService.downloadBlobAsFile(blob, finalFileName);\n      },\n      error: error => {\n        console.error('下載文件失敗:', error);\n        this._toastService.showErrorMSG('下載文件失敗');\n        // 如果 FileService 失敗，回退到原來的方法\n        window.open(docData.CLink + '?date=' + new Date().getTime(), '_blank');\n      }\n    });\n  }\n  showDialog(docData) {\n    this.visible = true;\n    this.currentDocId = docData.CID;\n    this.selectedDoc = docData;\n  }\n  close() {\n    this.visible = false;\n    this.currentDocId = -1;\n    this.selectedDoc = {};\n  }\n  actionButtonRight() {\n    this.updateSign();\n  }\n  actionButtonLeft() {\n    this.visible = false;\n    this.agree = false;\n    this.currentDocId = -1;\n    this.signaturePadComponent.clear();\n  }\n  goback() {\n    this.router.navigateByUrl(\"/\");\n  }\n  static #_ = this.ɵfac = function SignatureComponent_Factory(t) {\n    return new (t || SignatureComponent)(i0.ɵɵdirectiveInject(i1.FinalDocumentService), i0.ɵɵdirectiveInject(i2.ToastMessage), i0.ɵɵdirectiveInject(i3.UtilityService), i0.ɵɵdirectiveInject(i4.EventService), i0.ɵɵdirectiveInject(i5.FileService), i0.ɵɵdirectiveInject(i6.Router));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: SignatureComponent,\n    selectors: [[\"app-signature\"]],\n    viewQuery: function SignatureComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.signaturePadComponent = _t.first);\n      }\n    },\n    standalone: true,\n    features: [i0.ɵɵProvidersFeature([MessageService, ToastMessage]), i0.ɵɵStandaloneFeature],\n    decls: 27,\n    vars: 8,\n    consts: [[\"signaturePadComponent\", \"\"], [1, \"wrapper\"], [1, \"content\"], [1, \"flex\", \"justify-center\"], [1, \"signin\"], [1, \"title\"], [1, \"w-full\", \"text-center\"], [1, \"button1\", \"my-12\", 3, \"click\"], [3, \"visibleChange\", \"actionButtonLeft\", \"actionButtonRight\", \"textData\", \"visible\"], [1, \"w-full\", \"px-4\"], [1, \"w-full\"], [1, \"text-center\", \"flex\", \"items-center\", \"gap-x-1\", \"rounded-full\", \"py-1\", \"m-auto\", 2, \"background\", \"linear-gradient(to right, rgba(0, 143, 199, 0.2) 0%, rgba(0, 143, 199, 0.2) 100%)\", \"padding\", \"0 60px\"], [1, \"font-semibold\"], [\"src\", \"assets/PDF.svg\", 1, \"cursor-pointer\", 3, \"click\"], [1, \"mt-4\", \"w-full\", \"text-stone-600\", \"checkbox-zone\"], [1, \"bg-white\", \"p-4\"], [\"label\", \"\\u6211\\u5DF2\\u78BA\\u8A8D\\u4E26\\u540C\\u610F\\u6240\\u6709\\u5BA2\\u8B8A\\u5716\\u9762\\u5167\\u5BB9\\u3002\", 3, \"ngModelChange\", \"binary\", \"ngModel\"], [1, \"h-fit\", \"max-h-[200px]\", \"text-xl\", \"font-light\", \"overflow-y-scroll\", \"mt-2\"], [1, \"mt-4\"], [\"pRipple\", \"\", \"position\", \"top-right\"], [4, \"ngFor\", \"ngForOf\"], [1, \"card\", \"flex\", \"justify-content-center\"], [1, \"pafbox\"], [1, \"block\", \"sm:flex\", \"items-center\", \"box1\"], [1, \"flex\", \"items-center\", \"sm:mb-0\", \"mb-2\"], [1, \"block\", \"sm:flex\", \"items-center\", \"box2\"], [\"class\", \"mr-4\", 4, \"ngIf\"], [1, \"flex\", \"items-center\", \"gap-x-1\", \"rounded-full\", \"px-3\", \"py-1\", \"mr-4\", 2, \"background\", \"linear-gradient(to right, rgba(0, 143, 199, 0.2) 0%, rgba(0, 143, 199, 0.2) 100%)\", 3, \"click\"], [\"src\", \"assets/PDF.svg\", 1, \"cursor-pointer\"], [1, \"mr-4\"], [1, \"pl-4\", \"pr-1\"], [\"class\", \"not-pay rounded\", 4, \"ngIf\"], [\"class\", \"pay rounded\", 4, \"ngIf\"], [1, \"makesures\", \"mr-1\", \"sm:mb-0\", \"mb-2\", 3, \"ngClass\"], [\"class\", \"button2 !w-[120px]\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"!w-[120px]\", 4, \"ngIf\"], [1, \"not-pay\", \"rounded\"], [1, \"pay\", \"rounded\"], [1, \"button2\", \"!w-[120px]\", 3, \"click\"], [1, \"!w-[120px]\"]],\n    template: function SignatureComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"div\", 3)(3, \"div\", 4)(4, \"div\", 5);\n        i0.ɵɵtext(5, \"Step3 \\u78BA\\u8A8D\\u7C3D\\u7F72\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(6, SignatureComponent_Conditional_6_Template, 1, 1, \"ng-container\");\n        i0.ɵɵelementStart(7, \"div\", 3)(8, \"a\", 6)(9, \"button\", 7);\n        i0.ɵɵlistener(\"click\", function SignatureComponent_Template_button_click_9_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.goback());\n        });\n        i0.ɵɵtext(10, \"\\u8FD4\\u56DE\\u4E3B\\u9078\\u55AE\");\n        i0.ɵɵelementEnd()()()()()()();\n        i0.ɵɵelementStart(11, \"app-dialog-popup\", 8);\n        i0.ɵɵtwoWayListener(\"visibleChange\", function SignatureComponent_Template_app_dialog_popup_visibleChange_11_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          i0.ɵɵtwoWayBindingSet(ctx.visible, $event) || (ctx.visible = $event);\n          return i0.ɵɵresetView($event);\n        });\n        i0.ɵɵlistener(\"actionButtonLeft\", function SignatureComponent_Template_app_dialog_popup_actionButtonLeft_11_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.actionButtonLeft());\n        })(\"actionButtonRight\", function SignatureComponent_Template_app_dialog_popup_actionButtonRight_11_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.actionButtonRight());\n        });\n        i0.ɵɵelementStart(12, \"div\", 9)(13, \"div\", 10)(14, \"button\", 11)(15, \"span\", 12);\n        i0.ɵɵtext(16, \"\\u67E5\\u770B\\u6587\\u4EF6\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(17, \"img\", 13);\n        i0.ɵɵlistener(\"click\", function SignatureComponent_Template_img_click_17_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.downloadFile(ctx.selectedDoc));\n        });\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(18, \"div\", 14)(19, \"div\", 15)(20, \"p-checkbox\", 16);\n        i0.ɵɵtwoWayListener(\"ngModelChange\", function SignatureComponent_Template_p_checkbox_ngModelChange_20_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          i0.ɵɵtwoWayBindingSet(ctx.agree, $event) || (ctx.agree = $event);\n          return i0.ɵɵresetView($event);\n        });\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(21, \"div\", 17);\n        i0.ɵɵtext(22, \" \\u8ACB\\u9EDE\\u9078\\u4E0B\\u65B9\\u7070\\u8272\\u5340\\u584A\\u9032\\u884C\\u7C3D\\u540D \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(23, \"div\", 18);\n        i0.ɵɵelement(24, \"app-signature-pad\", null, 0);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelement(26, \"p-toast\", 19);\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(6);\n        i0.ɵɵconditional(6, ctx.docsData.length > 0 ? 6 : -1);\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"textData\", ctx.textData);\n        i0.ɵɵtwoWayProperty(\"visible\", ctx.visible);\n        i0.ɵɵadvance(9);\n        i0.ɵɵproperty(\"binary\", true);\n        i0.ɵɵtwoWayProperty(\"ngModel\", ctx.agree);\n        i0.ɵɵadvance(6);\n        i0.ɵɵstyleMap(i0.ɵɵpureFunction0(7, _c1));\n      }\n    },\n    dependencies: [RouterModule, DialogModule, CommonModule, i7.NgClass, i7.NgForOf, i7.NgIf, CheckboxModule, i8.Checkbox, FormsModule, i9.NgControlStatus, i9.NgModel, ToastModule, i10.Toast, DialogPopupComponent, SignaturePadComponent, DateFormatPipe],\n    styles: [\"@charset \\\"UTF-8\\\";@keyframes _ngcontent-%COMP%_radioAppear{0%{transform:translate(-50%,-50%) scale(0);opacity:0}50%{transform:translate(-50%,-50%) scale(1.3);opacity:.8}to{transform:translate(-50%,-50%) scale(1);opacity:1}}@keyframes _ngcontent-%COMP%_radioRipple{0%{width:0;height:0;opacity:.6}to{width:44px;height:44px;opacity:0}}@keyframes _ngcontent-%COMP%_buttonPress{0%{transform:scale(1)}50%{transform:scale(.98)}to{transform:scale(1)}}@keyframes _ngcontent-%COMP%_fadeInUp{0%{opacity:0;transform:translateY(20px)}to{opacity:1;transform:translateY(0)}}@keyframes _ngcontent-%COMP%_slideInRight{0%{opacity:0;transform:translate(30px)}to{opacity:1;transform:translate(0)}}@keyframes _ngcontent-%COMP%_shimmer{0%{background-position:-200px 0}to{background-position:200px 0}}@keyframes _ngcontent-%COMP%_pulse{0%,to{transform:scale(1);opacity:1}50%{transform:scale(1.05);opacity:.8}}.animate-fade-in-up[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fadeInUp .6s ease-out}.animate-slide-in-right[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_slideInRight .4s ease-out}.animate-pulse[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_pulse 2s infinite}.animate-shimmer[_ngcontent-%COMP%]{background:linear-gradient(90deg,transparent,rgba(255,255,255,.4),transparent);background-size:200px 100%;animation:_ngcontent-%COMP%_shimmer 1.5s infinite}.hover-lift[_ngcontent-%COMP%]{transition:transform .3s ease,box-shadow .3s ease}.hover-lift[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 4px 12px #00000026}.hover-scale[_ngcontent-%COMP%]{transition:transform .3s ease}.hover-scale[_ngcontent-%COMP%]:hover{transform:scale(1.02)}.hover-glow[_ngcontent-%COMP%]{transition:box-shadow .3s ease}.hover-glow[_ngcontent-%COMP%]:hover{box-shadow:0 0 20px #b8a6764d}html[_ngcontent-%COMP%]{line-height:initial;-webkit-text-size-adjust:100%;width:100%;height:100%}body[_ngcontent-%COMP%]{margin:0;font-size:1rem;color:#23181599;font-family:Noto Sans TC!important;box-sizing:border-box;width:100%;height:auto;overflow:auto;overscroll-behavior:none;letter-spacing:.32px}html[_ngcontent-%COMP%], body[_ngcontent-%COMP%]{-webkit-text-size-adjust:none;text-size-adjust:none}main[_ngcontent-%COMP%]{display:block}h1[_ngcontent-%COMP%]{font-size:1.75rem;font-weight:400}hr[_ngcontent-%COMP%]{box-sizing:content-box;height:0;overflow:visible;margin:10px 0;border:none;border-top:1px solid #000}pre[_ngcontent-%COMP%]{font-family:monospace,monospace;font-size:1rem}a[_ngcontent-%COMP%]{background-color:transparent;text-decoration:none;-webkit-tap-highlight-color:transparent}abbr[title][_ngcontent-%COMP%]{border-bottom:none;text-decoration:underline;-webkit-text-decoration:underline dotted;text-decoration:underline dotted}b[_ngcontent-%COMP%]{font-family:Noto Sans TC;font-weight:500}strong[_ngcontent-%COMP%]{font-family:Noto Sans TC;font-weight:600}code[_ngcontent-%COMP%], kbd[_ngcontent-%COMP%], samp[_ngcontent-%COMP%]{font-family:monospace,monospace;font-size:1em}small[_ngcontent-%COMP%]{font-size:80%}sub[_ngcontent-%COMP%], sup[_ngcontent-%COMP%]{font-size:75%;line-height:0;position:relative;vertical-align:baseline}sub[_ngcontent-%COMP%]{bottom:-.25em}sup[_ngcontent-%COMP%]{top:-.5em}p[_ngcontent-%COMP%]{font-size:1rem}img[_ngcontent-%COMP%]{border-style:none;max-width:100%}button[_ngcontent-%COMP%], input[_ngcontent-%COMP%], optgroup[_ngcontent-%COMP%], select[_ngcontent-%COMP%], textarea[_ngcontent-%COMP%]{font-family:Noto Sans TC,\\\\5fae\\\\8edf\\\\6b63\\\\9ed1\\\\9ad4,Arial,sans-serif;font-size:1rem;line-height:1.15;margin:0;color:#2f2f2f;-webkit-tap-highlight-color:transparent;letter-spacing:2px}select[_ngcontent-%COMP%]:focus, input[_ngcontent-%COMP%]:focus, button[_ngcontent-%COMP%]:focus{outline:none}button[_ngcontent-%COMP%]{padding:0;background-color:transparent;border:none;cursor:pointer}button[_ngcontent-%COMP%], input[_ngcontent-%COMP%]{overflow:visible}button[_ngcontent-%COMP%], select[_ngcontent-%COMP%]{text-transform:none}button[_ngcontent-%COMP%], [type=button][_ngcontent-%COMP%], [type=reset][_ngcontent-%COMP%], [type=submit][_ngcontent-%COMP%]{-webkit-appearance:button}button[_ngcontent-%COMP%]::-moz-focus-inner, [type=button][_ngcontent-%COMP%]::-moz-focus-inner, [type=reset][_ngcontent-%COMP%]::-moz-focus-inner, [type=submit][_ngcontent-%COMP%]::-moz-focus-inner{border-style:none;padding:0}button[_ngcontent-%COMP%]:-moz-focusring, [type=button][_ngcontent-%COMP%]:-moz-focusring, [type=reset][_ngcontent-%COMP%]:-moz-focusring, [type=submit][_ngcontent-%COMP%]:-moz-focusring{outline:1px dotted ButtonText}fieldset[_ngcontent-%COMP%]{padding:.35em .75em .625em}div[role=tab][_ngcontent-%COMP%]{-webkit-tap-highlight-color:transparent}legend[_ngcontent-%COMP%]{box-sizing:border-box;color:inherit;display:table;max-width:100%;padding:0;white-space:normal}progress[_ngcontent-%COMP%]{vertical-align:baseline}textarea[_ngcontent-%COMP%]{overflow:auto}[type=checkbox][_ngcontent-%COMP%], [type=radio][_ngcontent-%COMP%]{box-sizing:border-box;padding:0}[type=number][_ngcontent-%COMP%]::-webkit-inner-spin-button, [type=number][_ngcontent-%COMP%]::-webkit-outer-spin-button{height:auto}[type=search][_ngcontent-%COMP%]{-webkit-appearance:textfield;outline-offset:-2px}[type=search][_ngcontent-%COMP%]::-webkit-search-decoration{-webkit-appearance:none}[_ngcontent-%COMP%]::-webkit-file-upload-button{-webkit-appearance:button;font:inherit}details[_ngcontent-%COMP%]{display:block}summary[_ngcontent-%COMP%]{display:list-item}template[_ngcontent-%COMP%]{display:none}[hidden][_ngcontent-%COMP%]{display:none}table[_ngcontent-%COMP%]{border-spacing:0px}ol[_ngcontent-%COMP%], ul[_ngcontent-%COMP%]{padding-left:0;margin-top:0}.form-check-input[_ngcontent-%COMP%]:focus{outline:none;box-shadow:none}.container[_ngcontent-%COMP%]{width:100%}@media (min-width: 640px){.container[_ngcontent-%COMP%]{max-width:640px}}@media (min-width: 768px){.container[_ngcontent-%COMP%]{max-width:768px}}@media (min-width: 1024px){.container[_ngcontent-%COMP%]{max-width:1024px}}@media (min-width: 1280px){.container[_ngcontent-%COMP%]{max-width:1280px}}@media (min-width: 1536px){.container[_ngcontent-%COMP%]{max-width:1536px}}.visible[_ngcontent-%COMP%]{visibility:visible}.collapse[_ngcontent-%COMP%]{visibility:collapse}.static[_ngcontent-%COMP%]{position:static}.fixed[_ngcontent-%COMP%]{position:fixed}.absolute[_ngcontent-%COMP%]{position:absolute}.relative[_ngcontent-%COMP%]{position:relative}.inset-0[_ngcontent-%COMP%]{inset:0}.inset-y-0[_ngcontent-%COMP%]{top:0;bottom:0}.bottom-0[_ngcontent-%COMP%]{bottom:0}.bottom-1[_ngcontent-%COMP%]{bottom:.25rem}.bottom-2[_ngcontent-%COMP%]{bottom:.5rem}.bottom-3[_ngcontent-%COMP%]{bottom:.75rem}.left-0[_ngcontent-%COMP%]{left:0}.left-1\\\\/2[_ngcontent-%COMP%]{left:50%}.left-2[_ngcontent-%COMP%]{left:.5rem}.left-3[_ngcontent-%COMP%]{left:.75rem}.right-0[_ngcontent-%COMP%]{right:0}.right-1[_ngcontent-%COMP%]{right:.25rem}.right-2[_ngcontent-%COMP%]{right:.5rem}.right-3[_ngcontent-%COMP%]{right:.75rem}.top-1[_ngcontent-%COMP%]{top:.25rem}.top-1\\\\/2[_ngcontent-%COMP%]{top:50%}.top-2[_ngcontent-%COMP%]{top:.5rem}.top-3[_ngcontent-%COMP%]{top:.75rem}.z-10[_ngcontent-%COMP%]{z-index:10}.z-50[_ngcontent-%COMP%]{z-index:50}.m-0[_ngcontent-%COMP%]{margin:0}.m-2[_ngcontent-%COMP%]{margin:.5rem}.m-auto[_ngcontent-%COMP%]{margin:auto}.mx-3[_ngcontent-%COMP%]{margin-left:.75rem;margin-right:.75rem}.mx-4[_ngcontent-%COMP%]{margin-left:1rem;margin-right:1rem}.mx-5[_ngcontent-%COMP%]{margin-left:1.25rem;margin-right:1.25rem}.mx-\\\\__ph-0__[_ngcontent-%COMP%]{margin-left:20%;margin-right:20%}.my-12[_ngcontent-%COMP%]{margin-top:3rem;margin-bottom:3rem}.my-2[_ngcontent-%COMP%]{margin-top:.5rem;margin-bottom:.5rem}.my-4[_ngcontent-%COMP%]{margin-top:1rem;margin-bottom:1rem}.my-6[_ngcontent-%COMP%]{margin-top:1.5rem;margin-bottom:1.5rem}.\\\\!mt-0[_ngcontent-%COMP%]{margin-top:0!important}.mb-2[_ngcontent-%COMP%]{margin-bottom:.5rem}.mb-3[_ngcontent-%COMP%]{margin-bottom:.75rem}.mb-4[_ngcontent-%COMP%]{margin-bottom:1rem}.mb-6[_ngcontent-%COMP%]{margin-bottom:1.5rem}.ml-1[_ngcontent-%COMP%]{margin-left:.25rem}.ml-2[_ngcontent-%COMP%]{margin-left:.5rem}.mr-1[_ngcontent-%COMP%]{margin-right:.25rem}.mr-2[_ngcontent-%COMP%]{margin-right:.5rem}.mr-3[_ngcontent-%COMP%]{margin-right:.75rem}.mr-4[_ngcontent-%COMP%]{margin-right:1rem}.mr-5[_ngcontent-%COMP%]{margin-right:1.25rem}.mr-8[_ngcontent-%COMP%]{margin-right:2rem}.mt-1[_ngcontent-%COMP%]{margin-top:.25rem}.mt-10[_ngcontent-%COMP%]{margin-top:2.5rem}.mt-14[_ngcontent-%COMP%]{margin-top:3.5rem}.mt-2[_ngcontent-%COMP%]{margin-top:.5rem}.mt-3[_ngcontent-%COMP%]{margin-top:.75rem}.mt-4[_ngcontent-%COMP%]{margin-top:1rem}.mt-5[_ngcontent-%COMP%]{margin-top:1.25rem}.mt-6[_ngcontent-%COMP%]{margin-top:1.5rem}.mt-7[_ngcontent-%COMP%]{margin-top:1.75rem}.mt-8[_ngcontent-%COMP%]{margin-top:2rem}.mt-\\\\__ph-0__[_ngcontent-%COMP%]{margin-top:16px}.block[_ngcontent-%COMP%]{display:block}.flex[_ngcontent-%COMP%]{display:flex}.table[_ngcontent-%COMP%]{display:table}.table-row[_ngcontent-%COMP%]{display:table-row}.grid[_ngcontent-%COMP%]{display:grid}.hidden[_ngcontent-%COMP%]{display:none}.\\\\!h-\\\\__ph-0__[_ngcontent-%COMP%]{height:107px!important}.\\\\!h-full[_ngcontent-%COMP%]{height:100%!important}.h-10[_ngcontent-%COMP%]{height:2.5rem}.h-16[_ngcontent-%COMP%]{height:4rem}.h-28[_ngcontent-%COMP%]{height:7rem}.h-32[_ngcontent-%COMP%]{height:8rem}.h-6[_ngcontent-%COMP%]{height:1.5rem}.h-8[_ngcontent-%COMP%]{height:2rem}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:100px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:145px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:280px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:40vh}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:47px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:480px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:4px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:50px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:55px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:665px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:80px}.h-auto[_ngcontent-%COMP%]{height:auto}.h-fit[_ngcontent-%COMP%]{height:-moz-fit-content;height:fit-content}.max-h-\\\\__ph-0__[_ngcontent-%COMP%]{max-height:200px}.max-h-\\\\__ph-0__[_ngcontent-%COMP%]{max-height:500px}.max-h-full[_ngcontent-%COMP%]{max-height:100%}.max-h-screen[_ngcontent-%COMP%]{max-height:100vh}.\\\\!min-h-\\\\__ph-0__[_ngcontent-%COMP%]{min-height:60vh!important}.\\\\!w-40[_ngcontent-%COMP%]{width:10rem!important}.\\\\!w-48[_ngcontent-%COMP%]{width:12rem!important}.\\\\!w-\\\\__ph-0__[_ngcontent-%COMP%]{width:107px!important}.\\\\!w-\\\\__ph-0__[_ngcontent-%COMP%]{width:120px!important}.\\\\!w-full[_ngcontent-%COMP%]{width:100%!important}.w-1\\\\/3[_ngcontent-%COMP%]{width:33.333333%}.w-10[_ngcontent-%COMP%]{width:2.5rem}.w-16[_ngcontent-%COMP%]{width:4rem}.w-2\\\\/3[_ngcontent-%COMP%]{width:66.666667%}.w-28[_ngcontent-%COMP%]{width:7rem}.w-6[_ngcontent-%COMP%]{width:1.5rem}.w-7\\\\/12[_ngcontent-%COMP%]{width:58.333333%}.w-8[_ngcontent-%COMP%]{width:2rem}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:100%}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:150px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:160px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:180px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:30rem}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:400px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:45%}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:485px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:50%}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:550px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:660px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:760px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:80%}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:80px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:968px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:97%}.w-full[_ngcontent-%COMP%]{width:100%}.\\\\!max-w-full[_ngcontent-%COMP%]{max-width:100%!important}.max-w-4xl[_ngcontent-%COMP%]{max-width:56rem}.max-w-\\\\__ph-0__[_ngcontent-%COMP%]{max-width:1216px}.max-w-full[_ngcontent-%COMP%]{max-width:100%}.flex-1[_ngcontent-%COMP%]{flex:1 1 0%}.flex-shrink[_ngcontent-%COMP%]{flex-shrink:1}.flex-shrink-0[_ngcontent-%COMP%]{flex-shrink:0}.grow[_ngcontent-%COMP%]{flex-grow:1}.border-collapse[_ngcontent-%COMP%]{border-collapse:collapse}.-translate-x-1\\\\/2[_ngcontent-%COMP%]{--tw-translate-x: -50%;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.-translate-y-1\\\\/2[_ngcontent-%COMP%]{--tw-translate-y: -50%;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.transform[_ngcontent-%COMP%]{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.grid-cols-1[_ngcontent-%COMP%]{grid-template-columns:repeat(1,minmax(0,1fr))}.grid-cols-10[_ngcontent-%COMP%]{grid-template-columns:repeat(10,minmax(0,1fr))}.grid-cols-2[_ngcontent-%COMP%]{grid-template-columns:repeat(2,minmax(0,1fr))}.grid-cols-3[_ngcontent-%COMP%]{grid-template-columns:repeat(3,minmax(0,1fr))}.grid-cols-4[_ngcontent-%COMP%]{grid-template-columns:repeat(4,minmax(0,1fr))}.flex-row[_ngcontent-%COMP%]{flex-direction:row}.flex-col[_ngcontent-%COMP%]{flex-direction:column}.flex-wrap[_ngcontent-%COMP%]{flex-wrap:wrap}.items-start[_ngcontent-%COMP%]{align-items:flex-start}.items-center[_ngcontent-%COMP%]{align-items:center}.justify-start[_ngcontent-%COMP%]{justify-content:flex-start}.justify-end[_ngcontent-%COMP%]{justify-content:flex-end}.justify-center[_ngcontent-%COMP%]{justify-content:center}.justify-between[_ngcontent-%COMP%]{justify-content:space-between}.gap-2[_ngcontent-%COMP%]{gap:.5rem}.gap-3[_ngcontent-%COMP%]{gap:.75rem}.gap-4[_ngcontent-%COMP%]{gap:1rem}.gap-x-1[_ngcontent-%COMP%]{column-gap:.25rem}.self-center[_ngcontent-%COMP%]{align-self:center}.overflow-hidden[_ngcontent-%COMP%]{overflow:hidden}.overflow-y-scroll[_ngcontent-%COMP%]{overflow-y:scroll}.break-words[_ngcontent-%COMP%]{overflow-wrap:break-word}.rounded[_ngcontent-%COMP%]{border-radius:.25rem}.rounded-3xl[_ngcontent-%COMP%]{border-radius:1.5rem}.rounded-full[_ngcontent-%COMP%]{border-radius:9999px}.rounded-lg[_ngcontent-%COMP%]{border-radius:.5rem}.rounded-md[_ngcontent-%COMP%]{border-radius:.375rem}.rounded-b[_ngcontent-%COMP%]{border-bottom-right-radius:.25rem;border-bottom-left-radius:.25rem}.border[_ngcontent-%COMP%]{border-width:1px}.border-2[_ngcontent-%COMP%]{border-width:2px}.border-solid[_ngcontent-%COMP%]{border-style:solid}.border-blue-400[_ngcontent-%COMP%]{--tw-border-opacity: 1;border-color:rgb(96 165 250 / var(--tw-border-opacity))}.border-blue-500[_ngcontent-%COMP%]{--tw-border-opacity: 1;border-color:rgb(59 130 246 / var(--tw-border-opacity))}.border-gray-300[_ngcontent-%COMP%]{--tw-border-opacity: 1;border-color:rgb(209 213 219 / var(--tw-border-opacity))}.bg-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-bg-opacity: 1;background-color:rgb(0 143 199 / var(--tw-bg-opacity))}.bg-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-bg-opacity: 1;background-color:rgb(184 166 118 / var(--tw-bg-opacity))}.bg-\\\\__ph-0__[_ngcontent-%COMP%]{background-color:#f3f1ea80}.bg-\\\\__ph-0__[_ngcontent-%COMP%]{background-color:#f3f1ea99}.bg-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-bg-opacity: 1;background-color:rgb(217 217 217 / var(--tw-bg-opacity))}.bg-black[_ngcontent-%COMP%]{--tw-bg-opacity: 1;background-color:rgb(0 0 0 / var(--tw-bg-opacity))}.bg-gray-500[_ngcontent-%COMP%]{--tw-bg-opacity: 1;background-color:rgb(107 114 128 / var(--tw-bg-opacity))}.bg-white[_ngcontent-%COMP%]{--tw-bg-opacity: 1;background-color:rgb(255 255 255 / var(--tw-bg-opacity))}.bg-opacity-50[_ngcontent-%COMP%]{--tw-bg-opacity: .5}.bg-opacity-70[_ngcontent-%COMP%]{--tw-bg-opacity: .7}.bg-opacity-75[_ngcontent-%COMP%]{--tw-bg-opacity: .75}.bg-opacity-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-bg-opacity: .04}.object-contain[_ngcontent-%COMP%]{object-fit:contain}.object-cover[_ngcontent-%COMP%]{object-fit:cover}.p-2[_ngcontent-%COMP%]{padding:.5rem}.p-4[_ngcontent-%COMP%]{padding:1rem}.p-\\\\__ph-0__[_ngcontent-%COMP%]{padding:16px}.px-1[_ngcontent-%COMP%]{padding-left:.25rem;padding-right:.25rem}.px-2[_ngcontent-%COMP%]{padding-left:.5rem;padding-right:.5rem}.px-3[_ngcontent-%COMP%]{padding-left:.75rem;padding-right:.75rem}.px-4[_ngcontent-%COMP%]{padding-left:1rem;padding-right:1rem}.px-6[_ngcontent-%COMP%]{padding-left:1.5rem;padding-right:1.5rem}.py-0[_ngcontent-%COMP%]{padding-top:0;padding-bottom:0}.py-0\\\\.5[_ngcontent-%COMP%]{padding-top:.125rem;padding-bottom:.125rem}.py-1[_ngcontent-%COMP%]{padding-top:.25rem;padding-bottom:.25rem}.py-2[_ngcontent-%COMP%]{padding-top:.5rem;padding-bottom:.5rem}.py-3[_ngcontent-%COMP%]{padding-top:.75rem;padding-bottom:.75rem}.py-4[_ngcontent-%COMP%]{padding-top:1rem;padding-bottom:1rem}.pb-4[_ngcontent-%COMP%]{padding-bottom:1rem}.pb-5[_ngcontent-%COMP%]{padding-bottom:1.25rem}.pb-8[_ngcontent-%COMP%]{padding-bottom:2rem}.pl-4[_ngcontent-%COMP%]{padding-left:1rem}.pl-6[_ngcontent-%COMP%]{padding-left:1.5rem}.pr-1[_ngcontent-%COMP%]{padding-right:.25rem}.pr-3[_ngcontent-%COMP%]{padding-right:.75rem}.pr-4[_ngcontent-%COMP%]{padding-right:1rem}.text-left[_ngcontent-%COMP%]{text-align:left}.\\\\!text-center[_ngcontent-%COMP%]{text-align:center!important}.text-center[_ngcontent-%COMP%]{text-align:center}.text-right[_ngcontent-%COMP%]{text-align:right}.text-2xl[_ngcontent-%COMP%]{font-size:1.5rem;line-height:2rem}.text-3xl[_ngcontent-%COMP%]{font-size:1.875rem;line-height:2.25rem}.text-base[_ngcontent-%COMP%]{font-size:1rem;line-height:1.5rem}.text-lg[_ngcontent-%COMP%]{font-size:1.125rem;line-height:1.75rem}.text-sm[_ngcontent-%COMP%]{font-size:.875rem;line-height:1.25rem}.text-xl[_ngcontent-%COMP%]{font-size:1.25rem;line-height:1.75rem}.text-xs[_ngcontent-%COMP%]{font-size:.75rem;line-height:1rem}.font-bold[_ngcontent-%COMP%]{font-weight:700}.font-light[_ngcontent-%COMP%]{font-weight:300}.font-medium[_ngcontent-%COMP%]{font-weight:500}.font-normal[_ngcontent-%COMP%]{font-weight:400}.font-semibold[_ngcontent-%COMP%]{font-weight:600}.leading-relaxed[_ngcontent-%COMP%]{line-height:1.625}.tracking-wider[_ngcontent-%COMP%]{letter-spacing:.05em}.text-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(0 143 199 / var(--tw-text-opacity))}.text-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(35 24 21 / var(--tw-text-opacity))}.text-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(51 51 51 / var(--tw-text-opacity))}.text-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(182 152 103 / var(--tw-text-opacity))}.text-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(184 166 118 / var(--tw-text-opacity))}.text-black[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(0 0 0 / var(--tw-text-opacity))}.text-blue-400[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(96 165 250 / var(--tw-text-opacity))}.text-gray-500[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(107 114 128 / var(--tw-text-opacity))}.text-gray-600[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(75 85 99 / var(--tw-text-opacity))}.text-stone-600[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(87 83 78 / var(--tw-text-opacity))}.text-stone-900[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(28 25 23 / var(--tw-text-opacity))}.text-white[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(255 255 255 / var(--tw-text-opacity))}.opacity-50[_ngcontent-%COMP%]{opacity:.5}.shadow[_ngcontent-%COMP%]{--tw-shadow: 0 1px 3px 0 rgb(0 0 0 / .1), 0 1px 2px -1px rgb(0 0 0 / .1);--tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.grayscale[_ngcontent-%COMP%]{--tw-grayscale: grayscale(100%);filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)}.filter[_ngcontent-%COMP%]{filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)}.transition[_ngcontent-%COMP%]{transition-property:color,background-color,border-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,-webkit-backdrop-filter;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,backdrop-filter;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,backdrop-filter,-webkit-backdrop-filter;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.15s}.transition-all[_ngcontent-%COMP%]{transition-property:all;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.15s}.transition-shadow[_ngcontent-%COMP%]{transition-property:box-shadow;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.15s}.duration-200[_ngcontent-%COMP%]{transition-duration:.2s}.ease-in-out[_ngcontent-%COMP%]{transition-timing-function:cubic-bezier(.4,0,.2,1)}.content[_ngcontent-%COMP%]{width:100%}@media screen and (max-width: 1024px){.content[_ngcontent-%COMP%]{width:100%;padding:0 16px 50px}}.pc[_ngcontent-%COMP%], .inpc[_ngcontent-%COMP%]{display:initial}@media screen and (max-width: 1024px){.inpc[_ngcontent-%COMP%]{display:none}}.flat[_ngcontent-%COMP%]{display:none}@media screen and (max-width: 1024px){.flat[_ngcontent-%COMP%]{display:initial}}.mb[_ngcontent-%COMP%]{display:none}@media screen and (max-width: 912px){.mb[_ngcontent-%COMP%]{display:initial}}.cursor-pointer[_ngcontent-%COMP%]{cursor:pointer}.wrapper[_ngcontent-%COMP%]{display:flex;justify-content:center}.wrapper[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]{position:relative}.wrapper[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]:after{content:\\\"\\\";position:absolute;background-image:url(/assets/background/background2.png);width:180px;height:150px;background-size:unset;background-position:left;z-index:-1;right:0;bottom:20px}.button1[_ngcontent-%COMP%]{width:200px;height:47px;border:1px solid #CDCDCD;color:#3a4246;border-radius:28px;background-origin:border-box;background-clip:content-box,border-box;transition:all .3s ease}.button1[_ngcontent-%COMP%]:hover{border-color:#b8a676;color:#ae9b66;background-color:#b8a6760d}.button1[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#b8a676;transition:color .3s ease}.button1[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{color:#ae9b66}@media screen and (max-width: 912px){.button1[_ngcontent-%COMP%]{width:100%}}.button2[_ngcontent-%COMP%]{width:200px;padding:12px 24px;height:47px;color:#fff;background:linear-gradient(90deg,#ae9b66,#b8a676);border-radius:24px;box-shadow:0 2px 8px #ae9b6633;transition:all .3s ease}.button2[_ngcontent-%COMP%]:hover{background:linear-gradient(90deg,#9b8a5a,#ae9b66);transform:translateY(-1px);box-shadow:0 4px 12px #ae9b664d}.button2[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#fff}@media screen and (max-width: 912px){.button2[_ngcontent-%COMP%]{width:100%}}.button3[_ngcontent-%COMP%]{width:180px;padding:12px 24px;height:47px;color:#fff;background:linear-gradient(90deg,#ae9b66,#b8a676);border-radius:24px;box-shadow:0 2px 8px #ae9b6633;transition:all .3s ease}.button3[_ngcontent-%COMP%]:hover{background:linear-gradient(90deg,#9b8a5a,#ae9b66);transform:translateY(-1px);box-shadow:0 4px 12px #ae9b664d}.button3[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#fff}@media screen and (max-width: 912px){.button3[_ngcontent-%COMP%]{width:100%}}.button4[_ngcontent-%COMP%]{width:180px;padding:12px 24px;height:47px;color:#fff;background:#979797;border-radius:24px;transition:all .3s ease}.button4[_ngcontent-%COMP%]:hover{background:#7e7e7e}.button4[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#fff}@media screen and (max-width: 912px){.button4[_ngcontent-%COMP%]{width:100%}}.checkbox-zone[_ngcontent-%COMP%]{padding:2px;border-radius:2px;position:relative;background:linear-gradient(90deg,#ae9b66,#b8a676);box-shadow:0 1px 4px #ae9b664d}.avoid-resize-img[_ngcontent-%COMP%]{max-width:none}.fit-size[_ngcontent-%COMP%]{height:inherit;width:inherit;object-fit:cover;object-position:center}.p-calendar[_ngcontent-%COMP%]{width:100%}.p-calendar[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{border-bottom:1px solid rgba(144,150,157,.4);border-radius:0}.p-calendar[_ngcontent-%COMP%]   .p-inputtext[_ngcontent-%COMP%]{border:none;border-radius:0;border-bottom:1px solid rgba(144,150,157,.4);padding:12.5px 0;color:#23181599}@media screen and (max-width: 912px){.p-calendar[_ngcontent-%COMP%]   .p-inputtext[_ngcontent-%COMP%]{background:transparent}}.p-dropdown[_ngcontent-%COMP%]{border:none;width:100%}.p-dropdown[_ngcontent-%COMP%]   .p-inputtext[_ngcontent-%COMP%]{border:none;border-radius:0;border-bottom:1px solid rgba(144,150,157,.4);padding:12.5px 0;color:#231815}@media screen and (max-width: 912px){.p-dropdown[_ngcontent-%COMP%]   .p-inputtext[_ngcontent-%COMP%]{background:transparent}}.p-dropdown[_ngcontent-%COMP%]   .p-dropdown-trigger[_ngcontent-%COMP%]{border-radius:0;border-bottom:1px solid rgba(144,150,157,.4)}.p-dropdown[_ngcontent-%COMP%]:not(.p-disabled).p-focus{box-shadow:none}.p-radiobutton[_ngcontent-%COMP%]{position:relative;display:inline-flex;align-items:center}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box[_ngcontent-%COMP%]{width:22px;height:22px;border:2px solid #CDCDCD;background-color:#fff;border-radius:50%;transition:all .3s cubic-bezier(.25,.46,.45,.94);position:relative;box-shadow:0 1px 3px #ae9b661a;cursor:pointer}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box[_ngcontent-%COMP%]:hover{border-color:#b8a676;background:radial-gradient(circle at center,#b8a67614,#b8a67605);transform:scale(1.05);box-shadow:0 2px 8px #ae9b6626}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box[_ngcontent-%COMP%]:focus{outline:none;box-shadow:0 0 0 3px #b8a67640,0 2px 8px #ae9b6633;border-color:#a39460}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box[_ngcontent-%COMP%]:active{transform:scale(.98)}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box.p-highlight[_ngcontent-%COMP%]{background:linear-gradient(90deg,#ae9b66,#b8a676);border-color:#ae9b66;box-shadow:0 2px 12px #ae9b6640,inset 0 1px 2px #fff3}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box.p-highlight[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:50%;left:50%;width:8px;height:8px;background:radial-gradient(circle,#fff,#ffffffe6);border-radius:50%;transform:translate(-50%,-50%) scale(0);box-shadow:0 1px 2px #0003;animation:_ngcontent-%COMP%_radioAppear .2s ease forwards}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box.p-highlight[_ngcontent-%COMP%]:hover{background:linear-gradient(90deg,#9b8a5a,#ae9b66);border-color:#9b8a5a;transform:scale(1.05);box-shadow:0 3px 15px #ae9b6659,inset 0 1px 2px #ffffff40}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box.p-highlight[_ngcontent-%COMP%]:focus{box-shadow:0 0 0 3px #b8a6764d,0 3px 15px #ae9b6659}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-icon[_ngcontent-%COMP%]{display:none}@keyframes _ngcontent-%COMP%_radioAppear{0%{transform:translate(-50%,-50%) scale(0);opacity:0}50%{transform:translate(-50%,-50%) scale(1.2);opacity:.8}to{transform:translate(-50%,-50%) scale(1);opacity:1}}@keyframes _ngcontent-%COMP%_radioRipple{0%{transform:translate(-50%,-50%) scale(0);opacity:.6}to{transform:translate(-50%,-50%) scale(4);opacity:0}}.p-panel[_ngcontent-%COMP%]   .p-panel-header[_ngcontent-%COMP%]{background-color:#fff;color:#846a52;font-size:16px;border:1px solid #E6F0F3;border-radius:6px;padding:16px 32px}.p-panel-expanded[_ngcontent-%COMP%]   .p-panel-header[_ngcontent-%COMP%]{padding:16px 32px 8px;border-bottom:none!important;border-bottom-right-radius:0;border-bottom-left-radius:0}@media screen and (max-width: 912px){.p-panel-expanded[_ngcontent-%COMP%]   .p-panel-header[_ngcontent-%COMP%]{padding:16px 16px 8px}}.p-panel[_ngcontent-%COMP%]   .p-panel-header[_ngcontent-%COMP%]   .p-panel-header-icon[_ngcontent-%COMP%]{transform:rotate(-180deg)!important;transition:transform .3s linear}.p-panel-expanded[_ngcontent-%COMP%]   .p-panel-header[_ngcontent-%COMP%]   .p-panel-header-icon[_ngcontent-%COMP%]{transform:rotate(0)!important;transition:transform .3s linear}.p-panel[_ngcontent-%COMP%]   .p-panel-header[_ngcontent-%COMP%]   .p-panel-header-icon[_ngcontent-%COMP%]:focus{box-shadow:none}.p-panel[_ngcontent-%COMP%]   .p-panel-content[_ngcontent-%COMP%]{padding:0 32px 16px}@media screen and (max-width: 912px){.p-panel[_ngcontent-%COMP%]   .p-panel-content[_ngcontent-%COMP%]{padding:0 16px 16px}}.p-panel[_ngcontent-%COMP%]   .p-panel-header[_ngcontent-%COMP%]   .p-panel-header-icon[_ngcontent-%COMP%]{width:24px;height:24px}.p-dialog-mask[_ngcontent-%COMP%]{width:100%;height:100%;background-color:#0006;top:0;z-index:20;pointer-events:all!important}.p-dialog[_ngcontent-%COMP%]   .p-dialog-header[_ngcontent-%COMP%]{padding:32px 24px 16px;color:#231815;font-size:24px;text-align:center;display:flex;justify-content:center}.p-dialog[_ngcontent-%COMP%]   .p-dialog-content[_ngcontent-%COMP%]{padding:32px 24px 0;background:linear-gradient(180deg,#f3f1ea,#fff)}.p-dialog[_ngcontent-%COMP%]   .p-dialog-header[_ngcontent-%COMP%]   .p-dialog-header-icon[_ngcontent-%COMP%]{display:none}.p-dialog[_ngcontent-%COMP%]   .p-dialog-footer[_ngcontent-%COMP%]{padding:24px 24px 40px;display:flex;justify-content:center}.p-checkbox[_ngcontent-%COMP%]{width:18px;height:18px;display:inline-flex;align-items:center}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box[_ngcontent-%COMP%]{width:18px;height:18px;border:2px solid #CDCDCD;border-radius:3px;transition:.3s ease;background:#fff;position:relative;display:flex;align-items:center;justify-content:center}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box[_ngcontent-%COMP%]:hover{border-color:#b8a676}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box[_ngcontent-%COMP%]:focus{outline:none;border-color:#b8a676;box-shadow:0 0 0 2px #b8a67633}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box.p-highlight[_ngcontent-%COMP%]{background:#b8a676!important;border-color:#b8a676!important}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box[_ngcontent-%COMP%]   .p-checkbox-icon[_ngcontent-%COMP%]{color:#fff!important;font-size:12px;width:12px;height:12px;display:flex;align-items:center;justify-content:center}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box.p-highlight[_ngcontent-%COMP%]   .p-checkbox-icon[_ngcontent-%COMP%]{transform:scale(1);opacity:1}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box[_ngcontent-%COMP%]:not(.p-highlight)   .p-checkbox-icon[_ngcontent-%COMP%]{transform:scale(0);opacity:0}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box.p-highlight[_ngcontent-%COMP%]   .p-checkbox-icon[_ngcontent-%COMP%]:before{content:\\\"\\\\e915\\\";font-family:primeicons;font-weight:400;font-style:normal;font-size:12px;display:inline-block}.p-checkbox-label[_ngcontent-%COMP%]{margin-left:8px;cursor:pointer;-webkit-user-select:none;user-select:none}.input[_ngcontent-%COMP%]{width:100%;border:none;padding:12.5px 0;border-bottom:1px solid rgba(144,150,157,.4);border-radius:0}[_ngcontent-%COMP%]::-webkit-scrollbar{width:4px;height:4px}[_ngcontent-%COMP%]::-webkit-scrollbar-button{background:transparent;border-radius:2px}[_ngcontent-%COMP%]::-webkit-scrollbar-track-piece{background:transparent}[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{border-radius:4px;background-color:#90969d33}[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:transparent}.p-radiobutton-label[_ngcontent-%COMP%]{color:#231815!important;font-weight:400;cursor:pointer;transition:all .3s ease}.p-radiobutton-label[_ngcontent-%COMP%]:hover{color:#ae9b66!important}label[for*=requirement_][_ngcontent-%COMP%]{color:#231815!important;font-weight:400}label[for*=requirement_][_ngcontent-%COMP%]:hover{color:#ae9b66!important}input[type=radio][_ngcontent-%COMP%]:checked + label[_ngcontent-%COMP%], .p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box.p-highlight[_ngcontent-%COMP%] + label[_ngcontent-%COMP%]{color:#ae9b66!important;font-weight:600}.signin[_ngcontent-%COMP%]{margin-top:20px;width:1216px;z-index:3;min-height:550px;height:550px}.signin[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-size:32px;font-weight:500;color:#231815;margin-bottom:16px}.signin[_ngcontent-%COMP%]   .pafbox[_ngcontent-%COMP%]{padding:12.5px 16px;width:100%;color:#000;background:#f3f1ea99;display:flex;align-items:center;justify-content:space-between;margin-bottom:8px}@media screen and (max-width: 700px){.signin[_ngcontent-%COMP%]   .pafbox[_ngcontent-%COMP%]{font-size:14px}}@media screen and (max-width: 640px){.signin[_ngcontent-%COMP%]   .pafbox[_ngcontent-%COMP%]{padding:16px;font-size:16px;display:block}}.signin[_ngcontent-%COMP%]   .pafbox[_ngcontent-%COMP%]   .box1[_ngcontent-%COMP%]   .makesure[_ngcontent-%COMP%]{padding:4px 12px;border-radius:4px;background:#df3b22;color:#fff;width:-moz-fit-content;width:fit-content}.signin[_ngcontent-%COMP%]   .pafbox[_ngcontent-%COMP%]   .box1[_ngcontent-%COMP%]   .realsure[_ngcontent-%COMP%]{background-color:#009e9c}.signin[_ngcontent-%COMP%]   .pafbox[_ngcontent-%COMP%]   .box2[_ngcontent-%COMP%]{color:#23181599}.signin[_ngcontent-%COMP%]   .pafbox[_ngcontent-%COMP%]:last-child{margin-bottom:0}.checkbox[_ngcontent-%COMP%]{border:1px solid #008FC7;padding:12px 16px;border-radius:4px;color:#231815cc;background-color:#fff}.sign[_ngcontent-%COMP%]{background-color:#e6f0f3;border-radius:4px;height:100px}@media screen and (max-width: 640px){.sign[_ngcontent-%COMP%]{height:200px}.card[_ngcontent-%COMP%]{width:100%}.card[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{width:100%;margin-top:16px}}.not-pay[_ngcontent-%COMP%]{padding:4px 16px;background-color:#f1502f14;font-size:16px;color:#f1502f}.pay[_ngcontent-%COMP%]{padding:4px 16px;background-color:#009e9c14;font-size:16px;color:#009e9c}.hover\\\\:bg-opacity-75[_ngcontent-%COMP%]:hover{--tw-bg-opacity: .75}.hover\\\\:shadow-lg[_ngcontent-%COMP%]:hover{--tw-shadow: 0 10px 15px -3px rgb(0 0 0 / .1), 0 4px 6px -4px rgb(0 0 0 / .1);--tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}@media not all and (min-width: 1280px){.max-xl\\\\:grid-cols-6[_ngcontent-%COMP%]{grid-template-columns:repeat(6,minmax(0,1fr))}}@media not all and (min-width: 1024px){.max-lg\\\\:bottom-12[_ngcontent-%COMP%]{bottom:3rem}.max-lg\\\\:w-full[_ngcontent-%COMP%]{width:100%}.max-lg\\\\:grid-cols-4[_ngcontent-%COMP%]{grid-template-columns:repeat(4,minmax(0,1fr))}}@media not all and (min-width: 768px){.max-md\\\\:bottom-14[_ngcontent-%COMP%]{bottom:3.5rem}.max-md\\\\:my-4[_ngcontent-%COMP%]{margin-top:1rem;margin-bottom:1rem}.max-md\\\\:ml-0[_ngcontent-%COMP%]{margin-left:0}.max-md\\\\:mt-10[_ngcontent-%COMP%]{margin-top:2.5rem}.max-md\\\\:mt-5[_ngcontent-%COMP%]{margin-top:1.25rem}.max-md\\\\:hidden[_ngcontent-%COMP%]{display:none}.max-md\\\\:h-\\\\__ph-0__[_ngcontent-%COMP%]{height:65%}.max-md\\\\:\\\\!w-\\\\__ph-0__[_ngcontent-%COMP%]{width:450px!important}.max-md\\\\:w-\\\\__ph-0__[_ngcontent-%COMP%]{width:500px}.max-md\\\\:w-full[_ngcontent-%COMP%]{width:100%}.max-md\\\\:max-w-full[_ngcontent-%COMP%]{max-width:100%}.max-md\\\\:flex-col[_ngcontent-%COMP%]{flex-direction:column}.max-md\\\\:flex-col-reverse[_ngcontent-%COMP%]{flex-direction:column-reverse}.max-md\\\\:flex-wrap[_ngcontent-%COMP%]{flex-wrap:wrap}.max-md\\\\:items-start[_ngcontent-%COMP%]{align-items:flex-start}}@media not all and (min-width: 640px){.max-sm\\\\:bottom-10[_ngcontent-%COMP%]{bottom:2.5rem}.max-sm\\\\:ml-8[_ngcontent-%COMP%]{margin-left:2rem}.max-sm\\\\:block[_ngcontent-%COMP%]{display:block}.max-sm\\\\:\\\\!w-\\\\__ph-0__[_ngcontent-%COMP%]{width:340px!important}.max-sm\\\\:w-\\\\__ph-0__[_ngcontent-%COMP%]{width:380px}.max-sm\\\\:\\\\!grid-cols-2[_ngcontent-%COMP%]{grid-template-columns:repeat(2,minmax(0,1fr))!important}.max-sm\\\\:\\\\!justify-between[_ngcontent-%COMP%]{justify-content:space-between!important}.max-sm\\\\:gap-0[_ngcontent-%COMP%]{gap:0px}.max-sm\\\\:px-0[_ngcontent-%COMP%]{padding-left:0;padding-right:0}.max-sm\\\\:pl-0[_ngcontent-%COMP%]{padding-left:0}.max-sm\\\\:text-base[_ngcontent-%COMP%]{font-size:1rem;line-height:1.5rem}}@media (min-width: 640px){.sm\\\\:mx-0[_ngcontent-%COMP%]{margin-left:0;margin-right:0}.sm\\\\:mb-0[_ngcontent-%COMP%]{margin-bottom:0}.sm\\\\:mt-1[_ngcontent-%COMP%]{margin-top:.25rem}.sm\\\\:flex[_ngcontent-%COMP%]{display:flex}.sm\\\\:grid-cols-2[_ngcontent-%COMP%]{grid-template-columns:repeat(2,minmax(0,1fr))}.sm\\\\:items-center[_ngcontent-%COMP%]{align-items:center}.sm\\\\:pl-3[_ngcontent-%COMP%]{padding-left:.75rem}}@media (min-width: 768px){.md\\\\:flex[_ngcontent-%COMP%]{display:flex}.md\\\\:grid-cols-3[_ngcontent-%COMP%]{grid-template-columns:repeat(3,minmax(0,1fr))}.md\\\\:pb-20[_ngcontent-%COMP%]{padding-bottom:5rem}}@media (min-width: 1024px){.lg\\\\:\\\\!mx-0[_ngcontent-%COMP%]{margin-left:0!important;margin-right:0!important}.lg\\\\:\\\\!mt-0[_ngcontent-%COMP%]{margin-top:0!important}.lg\\\\:flex[_ngcontent-%COMP%]{display:flex}.lg\\\\:\\\\!w-\\\\__ph-0__[_ngcontent-%COMP%]{width:180px!important}.lg\\\\:\\\\!w-\\\\__ph-0__[_ngcontent-%COMP%]{width:800px!important}.lg\\\\:\\\\!w-\\\\__ph-0__[_ngcontent-%COMP%]{width:900px!important}.lg\\\\:\\\\!flex-row[_ngcontent-%COMP%]{flex-direction:row!important}.lg\\\\:\\\\!px-4[_ngcontent-%COMP%]{padding-left:1rem!important;padding-right:1rem!important}.lg\\\\:\\\\!px-\\\\__ph-0__[_ngcontent-%COMP%]{padding-left:22px!important;padding-right:22px!important}.lg\\\\:\\\\!py-\\\\__ph-0__[_ngcontent-%COMP%]{padding-top:18px!important;padding-bottom:18px!important}}\"]\n  });\n}", "map": {"version": 3, "names": ["RouterModule", "DialogModule", "CommonModule", "CheckboxModule", "FormsModule", "SignaturePadComponent", "DialogPopupComponent", "ToastMessage", "MessageService", "ToastModule", "DateFormatPipe", "EEvent", "LoadingService", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "docData_r3", "CCreateDT", "ɵɵpipeBind1", "CSignDate", "ɵɵlistener", "SignatureComponent_Conditional_6_ng_container_0_button_22_Template_button_click_0_listener", "ɵɵrestoreView", "_r5", "ɵɵnextContext", "$implicit", "ctx_r3", "ɵɵresetView", "showDialog", "ɵɵelement", "ɵɵelementContainerStart", "ɵɵtemplate", "SignatureComponent_Conditional_6_ng_container_0_div_8_Template", "SignatureComponent_Conditional_6_ng_container_0_div_9_Template", "SignatureComponent_Conditional_6_ng_container_0_Template_button_click_10_listener", "_r2", "downloadFile", "SignatureComponent_Conditional_6_ng_container_0_span_17_Template", "SignatureComponent_Conditional_6_ng_container_0_span_18_Template", "SignatureComponent_Conditional_6_ng_container_0_span_19_Template", "SignatureComponent_Conditional_6_ng_container_0_button_22_Template", "SignatureComponent_Conditional_6_ng_container_0_div_23_Template", "CDocumentName", "ɵɵproperty", "CSign", "CPayStatus", "ɵɵpureFunction1", "_c2", "SignatureComponent_Conditional_6_ng_container_0_Template", "docsData", "SignatureComponent", "constructor", "_finalDocService", "_toastService", "utilityService", "_eventService", "_fileService", "router", "signature", "agree", "textData", "title", "header", "content", "titleButtonLeft", "titleButtonRight", "visible", "currentDocId", "selectedDoc", "ngOnInit", "getFinalDocBefore", "handleSignatureData", "signatureDataUrl", "getSignatureFromChild", "signaturePadComponent", "getSignatureValue", "updateSign", "loading", "apiFinalDocumentUpdateSignPost$Json", "body", "CFinalDocID", "subscribe", "res", "StatusCode", "getFinalDocAfter", "showSucessMSG", "setTimeout", "window", "location", "reload", "showErrorMSG", "apiFinalDocumentGetFinalDocBeforePost$Json", "Entries", "apiFinalDocumentGetFinalDocAfterPost$Json", "push", "action", "payload", "docData", "CLink", "console", "error", "getFile", "next", "response", "blob", "filename", "baseFileName", "finalFileName", "getFileNameWithExtension", "type", "downloadBlobAsFile", "open", "Date", "getTime", "CID", "close", "actionButtonRight", "actionButtonLeft", "clear", "goback", "navigateByUrl", "_", "ɵɵdirectiveInject", "i1", "FinalDocumentService", "i2", "i3", "UtilityService", "i4", "EventService", "i5", "FileService", "i6", "Router", "_2", "selectors", "viewQuery", "SignatureComponent_Query", "rf", "ctx", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "SignatureComponent_Template", "SignatureComponent_Conditional_6_Template", "SignatureComponent_Template_button_click_9_listener", "_r1", "ɵɵtwoWayListener", "SignatureComponent_Template_app_dialog_popup_visibleChange_11_listener", "$event", "ɵɵtwoWayBindingSet", "SignatureComponent_Template_app_dialog_popup_actionButtonLeft_11_listener", "SignatureComponent_Template_app_dialog_popup_actionButtonRight_11_listener", "SignatureComponent_Template_img_click_17_listener", "SignatureComponent_Template_p_checkbox_ngModelChange_20_listener", "ɵɵconditional", "length", "ɵɵtwoWayProperty", "ɵɵstyleMap", "ɵɵpureFunction0", "_c1", "i7", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i8", "Checkbox", "i9", "NgControlStatus", "NgModel", "i10", "Toast", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\SaleChangeFront\\src\\app\\pages\\signature\\signature.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\SaleChangeFront\\src\\app\\pages\\signature\\signature.component.html"], "sourcesContent": ["import { Component, OnInit, ViewChild } from '@angular/core';\r\nimport { Router, RouterModule } from '@angular/router';\r\nimport { DialogModule } from 'primeng/dialog';\r\nimport { CommonModule } from '@angular/common';\r\nimport { CheckboxModule } from 'primeng/checkbox';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { SignaturePadComponent } from '../../components/signature-pad/signature-pad.component';\r\nimport { DialogPopupComponent } from '../../components/dialog-popup/dialog-popup.component';\r\nimport { ToastMessage } from '../../shared/services/message.service';\r\nimport { MessageService } from 'primeng/api';\r\nimport { UtilityService } from '../../shared/services/utility.service';\r\nimport { ToastModule } from 'primeng/toast';\r\nimport { ContentDialog } from '../../../model/choice.model';\r\nimport { GetFinalDocRes } from '../../../services/api/models';\r\nimport { DateFormatPipe } from '../../shared/pipes/date-format.pipe';\r\nimport { FinalDocumentService } from '../../../services/api/services/final-document.service';\r\nimport { EEvent, EventService } from '../../shared/services/event.service';\r\nimport { LoadingService } from '../../shared/services/loading.service';\r\nimport { FileService } from '../../../services/File.service';\r\n\r\n@Component({\r\n  selector: 'app-signature',\r\n  standalone: true,\r\n  imports: [\r\n    RouterModule, DialogModule, CommonModule,\r\n    CheckboxModule, CommonModule, FormsModule,\r\n    ToastModule,\r\n    DialogPopupComponent, SignaturePadComponent, DateFormatPipe\r\n  ],\r\n  providers: [\r\n    MessageService,\r\n    ToastMessage,\r\n  ],\r\n  templateUrl: './signature.component.html',\r\n  styleUrl: './signature.component.scss'\r\n})\r\nexport class SignatureComponent implements OnInit {\r\n  @ViewChild('signaturePadComponent') signaturePadComponent!: SignaturePadComponent;\r\n\r\n  signature: string = \"\"\r\n\r\n  docsData: GetFinalDocRes[] = []\r\n  agree: boolean = false;\r\n\r\n  textData: ContentDialog = {\r\n    title: 'sign',\r\n    header: \"確認簽署\",\r\n    content: \"\",\r\n    titleButtonLeft: \"返回上頁\",\r\n    titleButtonRight: \"確認\"\r\n  }\r\n\r\n  visible: boolean = false;\r\n  currentDocId: number = -1\r\n  selectedDoc = {} as GetFinalDocRes;\r\n  constructor(\r\n    private _finalDocService: FinalDocumentService,\r\n    private _toastService: ToastMessage,\r\n    private utilityService: UtilityService,\r\n    private _eventService: EventService,\r\n    private _fileService: FileService,\r\n    private router: Router\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    this.getFinalDocBefore()\r\n  }\r\n\r\n  handleSignatureData(signatureDataUrl: string): void {\r\n    this.signature = signatureDataUrl;\r\n  }\r\n\r\n  getSignatureFromChild() {\r\n    this.signature = this.signaturePadComponent.getSignatureValue();\r\n    return this.signature\r\n  }\r\n\r\n  updateSign() {\r\n    if (this.agree) {\r\n      LoadingService.loading(true);\r\n      this._finalDocService.apiFinalDocumentUpdateSignPost$Json({\r\n        body: {\r\n          CSign: this.getSignatureFromChild(),\r\n          CFinalDocID: this.currentDocId!\r\n        }\r\n      }).subscribe(res => {\r\n        if (res.StatusCode === 0) {\r\n          LoadingService.loading(false);\r\n          this.getFinalDocAfter()\r\n          this._toastService.showSucessMSG('更新成功');\r\n          setTimeout(function () {\r\n            window.location.reload();\r\n          }, 500)\r\n        }\r\n        this.visible = false;\r\n      })\r\n    } else {\r\n      this._toastService.showErrorMSG(\"[我已閱讀聲明] 必填\");\r\n      this.visible = true;\r\n    }\r\n  }\r\n\r\n  getFinalDocBefore() {\r\n    this._finalDocService.apiFinalDocumentGetFinalDocBeforePost$Json({}).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        this.docsData = res.Entries! ?? []\r\n      }\r\n    })\r\n  }\r\n\r\n  getFinalDocAfter() {\r\n    this._finalDocService.apiFinalDocumentGetFinalDocAfterPost$Json({}).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        this.docsData = res.Entries! ?? []\r\n        this._eventService.push({\r\n          action: EEvent.UPDATE_SIGN,\r\n          payload: res.Entries!\r\n        })\r\n      }\r\n    })\r\n  }\r\n  downloadFile(docData: GetFinalDocRes) {\r\n    if (!docData.CLink || !docData.CDocumentName) {\r\n      console.error('Document link or name is not available');\r\n      return;\r\n    }\r\n    this._fileService.getFile(docData.CLink, docData.CDocumentName).subscribe({\r\n      next: (response) => {\r\n        const { blob, filename } = response;\r\n        \r\n        // 使用 UtilityService 的智能檔名處理\r\n        const baseFileName = filename || docData.CDocumentName || 'document';\r\n        const finalFileName = this.utilityService.getFileNameWithExtension(baseFileName, blob.type);\r\n\r\n        // 使用簡化的下載邏輯\r\n        this.utilityService.downloadBlobAsFile(blob, finalFileName);\r\n      },\r\n      error: (error) => {\r\n        console.error('下載文件失敗:', error);\r\n        this._toastService.showErrorMSG('下載文件失敗');\r\n        // 如果 FileService 失敗，回退到原來的方法\r\n        window.open((docData.CLink + '?date=' + new Date().getTime()), '_blank');\r\n      }\r\n    });\r\n  }\r\n\r\n  showDialog(docData: GetFinalDocRes) {\r\n    this.visible = true\r\n    this.currentDocId = docData.CID!\r\n    this.selectedDoc = docData\r\n  }\r\n\r\n  close() {\r\n    this.visible = false;\r\n    this.currentDocId = -1\r\n    this.selectedDoc = {} as GetFinalDocRes;\r\n  }\r\n\r\n  actionButtonRight() {\r\n    this.updateSign()\r\n  }\r\n\r\n  actionButtonLeft() {\r\n    this.visible = false;\r\n    this.agree = false\r\n    this.currentDocId = -1\r\n    this.signaturePadComponent.clear()\r\n  }\r\n\r\n  goback() {\r\n    this.router.navigateByUrl(\"/\");\r\n  }\r\n}\r\n", "<div class=\"wrapper\">\r\n  <div class=\"content\">\r\n    <div class=\"flex justify-center\">\r\n      <div class=\"signin\">\r\n        <div class=\"title\">Step3 確認簽署</div>\r\n        @if (docsData.length > 0) {\r\n        <ng-container *ngFor=\"let docData of docsData\">\r\n          <div class=\"card flex justify-content-center\">\r\n            <div class=\"pafbox\">\r\n              <div class=\"block sm:flex items-center box1\">\r\n\r\n                <div class=\"flex items-center sm:mb-0 mb-2 \">\r\n                  <span>\r\n                    {{docData?.CDocumentName}}\r\n                  </span>\r\n                </div>\r\n              </div>\r\n              <div class=\"block sm:flex items-center box2\">\r\n\r\n                <div class=\"mr-4\" *ngIf=\"!docData.CSign\">產檔日期: {{docData.CCreateDT ? (docData.CCreateDT | dateFormat) :\r\n                  ''}}</div>\r\n                <div class=\"mr-4\" *ngIf=\"docData.CSign\">確認日期: {{docData.CSignDate ? (docData.CSignDate | dateFormat) :\r\n                  ''}}</div>\r\n\r\n                <button class=\"flex items-center gap-x-1 rounded-full px-3 py-1 mr-4\" (click)=\"downloadFile(docData)\"\r\n                  style=\"background: linear-gradient(to right, rgba(0, 143, 199, 0.2) 0%, rgba(0, 143, 199, 0.2) 100%);\">\r\n                  <span class=\"font-semibold\">查看文件</span>\r\n                  <img src=\"assets/PDF.svg\" class=\"cursor-pointer\">\r\n                </button>\r\n\r\n                <div class=\"mr-4\">\r\n                  <span class=\"pl-4 pr-1\">繳款狀態</span>\r\n                  <span class=\"not-pay rounded\" *ngIf=\"docData.CPayStatus == 0\">未繳款</span>\r\n                  <span class=\"pay rounded\" *ngIf=\"docData.CPayStatus == 1\">已繳款</span>\r\n                  <span class=\"pay rounded\" *ngIf=\"docData.CPayStatus == 2\">無須付款</span>\r\n                </div>\r\n                <div class=\"makesures mr-1 sm:mb-0 mb-2\" [ngClass]=\"{'realsures': docData.CSign}\"> {{ docData.CSign ?\r\n                  '已簽署' : '未簽署' }}</div>\r\n\r\n                <button class=\"button2 !w-[120px]\" *ngIf=\"!docData.CSign\" (click)=\"showDialog(docData)\">前往簽署</button>\r\n                <div *ngIf=\"docData.CSign\" class=\"!w-[120px]\"></div>\r\n              </div>\r\n\r\n            </div>\r\n          </div>\r\n\r\n\r\n          <!-- <div class=\"card flex justify-content-center\">\r\n            <div class=\"pafbox\">\r\n              <div class=\"block sm:flex items-center box1\">\r\n                <div class=\"makesures mr-1 sm:mb-0 mb-2\" [ngClass]=\"{'realsures': docData.CSign}\"> {{ docData.CSign ?\r\n                  '已確認' : '待確認' }}</div>\r\n                <div class=\"flex items-center sm:mb-0 mb-2 \">\r\n                  <span>\r\n                    {{docData?.CDocumentName}}\r\n                  </span>\r\n                </div>\r\n              </div>\r\n              <div class=\"block sm:flex items-center box2\">\r\n                <button class=\"flex items-center gap-x-1 rounded-full px-3 py-1 mr-4\"\r\n                  style=\"background: linear-gradient(to right, rgba(0, 143, 199, 0.2) 0%, rgba(0, 143, 199, 0.2) 100%);\">\r\n                  <span class=\"font-semibold\">查看文件</span>\r\n                  <img src=\"assets/PDF.svg\" class=\"cursor-pointer\" (click)=\"downloadFile(docData)\">\r\n                </button>\r\n                <div class=\"mr-4\" *ngIf=\"!docData.CSign\">產檔日期: {{docData.CCreateDT ? (docData.CCreateDT | dateFormat) :\r\n                  ''}}</div>\r\n                <div class=\"mr-4\" *ngIf=\"docData.CSign\">確認日期: {{docData.CSignDate ? (docData.CSignDate | dateFormat) :\r\n                  ''}}</div>\r\n                <div class=\"mr-4\">\r\n                  <span class=\"pl-4 pr-1\">繳款狀態</span>\r\n                  <span class=\"not-pay rounded\" *ngIf=\"docData.CPayStatus == 0\">未繳款</span>\r\n                  <span class=\"pay rounded\" *ngIf=\"docData.CPayStatus != 0\">已繳款</span>\r\n                </div>\r\n                <button class=\"button2\" *ngIf=\"!docData.CSign\" (click)=\"showDialog(docData)\">前往簽署</button>\r\n                <div *ngIf=\"docData.CSign\" class=\"w-[160px]\"></div>\r\n              </div>\r\n            </div>\r\n          </div> -->\r\n        </ng-container>\r\n        }\r\n        <div class=\"flex justify-center\">\r\n          <a class=\"w-full text-center\">\r\n            <button class=\"button1 my-12\" (click)=\"goback()\">返回主選單</button>\r\n          </a>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n\r\n\r\n<app-dialog-popup [textData]=\"textData\" [(visible)]=\"visible\" (actionButtonLeft)=\"actionButtonLeft()\"\r\n  (actionButtonRight)=\"actionButtonRight()\">\r\n  <div class=\"w-full px-4\">\r\n    <div class=\"w-full\">\r\n      <button class=\"text-center flex items-center gap-x-1 rounded-full py-1 m-auto\"\r\n        style=\"background: linear-gradient(to right, rgba(0, 143, 199, 0.2) 0%, rgba(0, 143, 199, 0.2) 100%); padding: 0 60px;\">\r\n        <span class=\"font-semibold\">查看文件</span>\r\n        <img src=\"assets/PDF.svg\" class=\"cursor-pointer\" (click)=\"downloadFile(selectedDoc)\">\r\n      </button>\r\n    </div>\r\n    <div class=\"mt-4 w-full text-stone-600 checkbox-zone\">\r\n      <div class=\"bg-white p-4\">\r\n        <p-checkbox [binary]=\"true\" label=\"我已確認並同意所有客變圖面內容。\" [(ngModel)]=\"agree\"></p-checkbox>\r\n      </div>\r\n    </div>\r\n    <div class=\"h-fit max-h-[200px] text-xl font-light overflow-y-scroll mt-2\">\r\n      請點選下方灰色區塊進行簽名\r\n    </div>\r\n    <div class=\"mt-4\">\r\n      <app-signature-pad #signaturePadComponent></app-signature-pad>\r\n    </div>\r\n  </div>\r\n</app-dialog-popup>\r\n<p-toast pRipple position=\"top-right\" [style]=\"{'width': '22rem'}\"></p-toast>"], "mappings": "AACA,SAAiBA,YAAY,QAAQ,iBAAiB;AACtD,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,qBAAqB,QAAQ,wDAAwD;AAC9F,SAASC,oBAAoB,QAAQ,sDAAsD;AAC3F,SAASC,YAAY,QAAQ,uCAAuC;AACpE,SAASC,cAAc,QAAQ,aAAa;AAE5C,SAASC,WAAW,QAAQ,eAAe;AAG3C,SAASC,cAAc,QAAQ,qCAAqC;AAEpE,SAASC,MAAM,QAAsB,qCAAqC;AAC1E,SAASC,cAAc,QAAQ,uCAAuC;;;;;;;;;;;;;;;;;;;;;ICEtDC,EAAA,CAAAC,cAAA,cAAyC;IAAAD,EAAA,CAAAE,MAAA,GACnC;;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAD6BH,EAAA,CAAAI,SAAA,EACnC;IADmCJ,EAAA,CAAAK,kBAAA,+BAAAC,UAAA,CAAAC,SAAA,GAAAP,EAAA,CAAAQ,WAAA,OAAAF,UAAA,CAAAC,SAAA,WACnC;;;;;IACNP,EAAA,CAAAC,cAAA,cAAwC;IAAAD,EAAA,CAAAE,MAAA,GAClC;;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAD4BH,EAAA,CAAAI,SAAA,EAClC;IADkCJ,EAAA,CAAAK,kBAAA,+BAAAC,UAAA,CAAAG,SAAA,GAAAT,EAAA,CAAAQ,WAAA,OAAAF,UAAA,CAAAG,SAAA,WAClC;;;;;IAUJT,EAAA,CAAAC,cAAA,eAA8D;IAAAD,EAAA,CAAAE,MAAA,yBAAG;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACxEH,EAAA,CAAAC,cAAA,eAA0D;IAAAD,EAAA,CAAAE,MAAA,yBAAG;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACpEH,EAAA,CAAAC,cAAA,eAA0D;IAAAD,EAAA,CAAAE,MAAA,+BAAI;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;IAKvEH,EAAA,CAAAC,cAAA,iBAAwF;IAA9BD,EAAA,CAAAU,UAAA,mBAAAC,2FAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAC,GAAA;MAAA,MAAAP,UAAA,GAAAN,EAAA,CAAAc,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAhB,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAASD,MAAA,CAAAE,UAAA,CAAAZ,UAAA,CAAmB;IAAA,EAAC;IAACN,EAAA,CAAAE,MAAA,+BAAI;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IACrGH,EAAA,CAAAmB,SAAA,cAAoD;;;;;;IAlC5DnB,EAAA,CAAAoB,uBAAA,GAA+C;IAMrCpB,EALR,CAAAC,cAAA,cAA8C,cACxB,cAC2B,cAEE,WACrC;IACJD,EAAA,CAAAE,MAAA,GACF;IAEJF,EAFI,CAAAG,YAAA,EAAO,EACH,EACF;IACNH,EAAA,CAAAC,cAAA,cAA6C;IAI3CD,EAFA,CAAAqB,UAAA,IAAAC,8DAAA,kBAAyC,IAAAC,8DAAA,kBAED;IAGxCvB,EAAA,CAAAC,cAAA,kBACyG;IADnCD,EAAA,CAAAU,UAAA,mBAAAc,kFAAA;MAAA,MAAAlB,UAAA,GAAAN,EAAA,CAAAY,aAAA,CAAAa,GAAA,EAAAV,SAAA;MAAA,MAAAC,MAAA,GAAAhB,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAASD,MAAA,CAAAU,YAAA,CAAApB,UAAA,CAAqB;IAAA,EAAC;IAEnGN,EAAA,CAAAC,cAAA,gBAA4B;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACvCH,EAAA,CAAAmB,SAAA,eAAiD;IACnDnB,EAAA,CAAAG,YAAA,EAAS;IAGPH,EADF,CAAAC,cAAA,eAAkB,gBACQ;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAGnCH,EAFA,CAAAqB,UAAA,KAAAM,gEAAA,mBAA8D,KAAAC,gEAAA,mBACJ,KAAAC,gEAAA,mBACA;IAC5D7B,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAAkF;IAACD,EAAA,CAAAE,MAAA,IACjE;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAGxBH,EADA,CAAAqB,UAAA,KAAAS,kEAAA,qBAAwF,KAAAC,+DAAA,kBAC1C;IAIpD/B,EAHI,CAAAG,YAAA,EAAM,EAEF,EACF;;;;;IA/BIH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,UAAA,kBAAAA,UAAA,CAAA0B,aAAA,MACF;IAKiBhC,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAiC,UAAA,UAAA3B,UAAA,CAAA4B,KAAA,CAAoB;IAEpBlC,EAAA,CAAAI,SAAA,EAAmB;IAAnBJ,EAAA,CAAAiC,UAAA,SAAA3B,UAAA,CAAA4B,KAAA,CAAmB;IAWLlC,EAAA,CAAAI,SAAA,GAA6B;IAA7BJ,EAAA,CAAAiC,UAAA,SAAA3B,UAAA,CAAA6B,UAAA,MAA6B;IACjCnC,EAAA,CAAAI,SAAA,EAA6B;IAA7BJ,EAAA,CAAAiC,UAAA,SAAA3B,UAAA,CAAA6B,UAAA,MAA6B;IAC7BnC,EAAA,CAAAI,SAAA,EAA6B;IAA7BJ,EAAA,CAAAiC,UAAA,SAAA3B,UAAA,CAAA6B,UAAA,MAA6B;IAEjBnC,EAAA,CAAAI,SAAA,EAAwC;IAAxCJ,EAAA,CAAAiC,UAAA,YAAAjC,EAAA,CAAAoC,eAAA,KAAAC,GAAA,EAAA/B,UAAA,CAAA4B,KAAA,EAAwC;IAAElC,EAAA,CAAAI,SAAA,EACjE;IADiEJ,EAAA,CAAAK,kBAAA,MAAAC,UAAA,CAAA4B,KAAA,mDACjE;IAEkBlC,EAAA,CAAAI,SAAA,EAAoB;IAApBJ,EAAA,CAAAiC,UAAA,UAAA3B,UAAA,CAAA4B,KAAA,CAAoB;IAClDlC,EAAA,CAAAI,SAAA,EAAmB;IAAnBJ,EAAA,CAAAiC,UAAA,SAAA3B,UAAA,CAAA4B,KAAA,CAAmB;;;;;IAlCjClC,EAAA,CAAAqB,UAAA,IAAAiB,wDAAA,6BAA+C;;;;IAAbtC,EAAA,CAAAiC,UAAA,YAAAjB,MAAA,CAAAuB,QAAA,CAAW;;;AD8BrD,OAAM,MAAOC,kBAAkB;EAmB7BC,YACUC,gBAAsC,EACtCC,aAA2B,EAC3BC,cAA8B,EAC9BC,aAA2B,EAC3BC,YAAyB,EACzBC,MAAc;IALd,KAAAL,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,MAAM,GAANA,MAAM;IAtBhB,KAAAC,SAAS,GAAW,EAAE;IAEtB,KAAAT,QAAQ,GAAqB,EAAE;IAC/B,KAAAU,KAAK,GAAY,KAAK;IAEtB,KAAAC,QAAQ,GAAkB;MACxBC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,MAAM;MACdC,OAAO,EAAE,EAAE;MACXC,eAAe,EAAE,MAAM;MACvBC,gBAAgB,EAAE;KACnB;IAED,KAAAC,OAAO,GAAY,KAAK;IACxB,KAAAC,YAAY,GAAW,CAAC,CAAC;IACzB,KAAAC,WAAW,GAAG,EAAoB;EAQ9B;EAEJC,QAAQA,CAAA;IACN,IAAI,CAACC,iBAAiB,EAAE;EAC1B;EAEAC,mBAAmBA,CAACC,gBAAwB;IAC1C,IAAI,CAACd,SAAS,GAAGc,gBAAgB;EACnC;EAEAC,qBAAqBA,CAAA;IACnB,IAAI,CAACf,SAAS,GAAG,IAAI,CAACgB,qBAAqB,CAACC,iBAAiB,EAAE;IAC/D,OAAO,IAAI,CAACjB,SAAS;EACvB;EAEAkB,UAAUA,CAAA;IACR,IAAI,IAAI,CAACjB,KAAK,EAAE;MACdlD,cAAc,CAACoE,OAAO,CAAC,IAAI,CAAC;MAC5B,IAAI,CAACzB,gBAAgB,CAAC0B,mCAAmC,CAAC;QACxDC,IAAI,EAAE;UACJnC,KAAK,EAAE,IAAI,CAAC6B,qBAAqB,EAAE;UACnCO,WAAW,EAAE,IAAI,CAACb;;OAErB,CAAC,CAACc,SAAS,CAACC,GAAG,IAAG;QACjB,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;UACxB1E,cAAc,CAACoE,OAAO,CAAC,KAAK,CAAC;UAC7B,IAAI,CAACO,gBAAgB,EAAE;UACvB,IAAI,CAAC/B,aAAa,CAACgC,aAAa,CAAC,MAAM,CAAC;UACxCC,UAAU,CAAC;YACTC,MAAM,CAACC,QAAQ,CAACC,MAAM,EAAE;UAC1B,CAAC,EAAE,GAAG,CAAC;;QAET,IAAI,CAACvB,OAAO,GAAG,KAAK;MACtB,CAAC,CAAC;KACH,MAAM;MACL,IAAI,CAACb,aAAa,CAACqC,YAAY,CAAC,aAAa,CAAC;MAC9C,IAAI,CAACxB,OAAO,GAAG,IAAI;;EAEvB;EAEAI,iBAAiBA,CAAA;IACf,IAAI,CAAClB,gBAAgB,CAACuC,0CAA0C,CAAC,EAAE,CAAC,CAACV,SAAS,CAACC,GAAG,IAAG;MACnF,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAAClC,QAAQ,GAAGiC,GAAG,CAACU,OAAQ,IAAI,EAAE;;IAEtC,CAAC,CAAC;EACJ;EAEAR,gBAAgBA,CAAA;IACd,IAAI,CAAChC,gBAAgB,CAACyC,yCAAyC,CAAC,EAAE,CAAC,CAACZ,SAAS,CAACC,GAAG,IAAG;MAClF,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAAClC,QAAQ,GAAGiC,GAAG,CAACU,OAAQ,IAAI,EAAE;QAClC,IAAI,CAACrC,aAAa,CAACuC,IAAI,CAAC;UACtBC,MAAM;UACNC,OAAO,EAAEd,GAAG,CAACU;SACd,CAAC;;IAEN,CAAC,CAAC;EACJ;EACAxD,YAAYA,CAAC6D,OAAuB;IAClC,IAAI,CAACA,OAAO,CAACC,KAAK,IAAI,CAACD,OAAO,CAACvD,aAAa,EAAE;MAC5CyD,OAAO,CAACC,KAAK,CAAC,wCAAwC,CAAC;MACvD;;IAEF,IAAI,CAAC5C,YAAY,CAAC6C,OAAO,CAACJ,OAAO,CAACC,KAAK,EAAED,OAAO,CAACvD,aAAa,CAAC,CAACuC,SAAS,CAAC;MACxEqB,IAAI,EAAGC,QAAQ,IAAI;QACjB,MAAM;UAAEC,IAAI;UAAEC;QAAQ,CAAE,GAAGF,QAAQ;QAEnC;QACA,MAAMG,YAAY,GAAGD,QAAQ,IAAIR,OAAO,CAACvD,aAAa,IAAI,UAAU;QACpE,MAAMiE,aAAa,GAAG,IAAI,CAACrD,cAAc,CAACsD,wBAAwB,CAACF,YAAY,EAAEF,IAAI,CAACK,IAAI,CAAC;QAE3F;QACA,IAAI,CAACvD,cAAc,CAACwD,kBAAkB,CAACN,IAAI,EAAEG,aAAa,CAAC;MAC7D,CAAC;MACDP,KAAK,EAAGA,KAAK,IAAI;QACfD,OAAO,CAACC,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;QAC/B,IAAI,CAAC/C,aAAa,CAACqC,YAAY,CAAC,QAAQ,CAAC;QACzC;QACAH,MAAM,CAACwB,IAAI,CAAEd,OAAO,CAACC,KAAK,GAAG,QAAQ,GAAG,IAAIc,IAAI,EAAE,CAACC,OAAO,EAAE,EAAG,QAAQ,CAAC;MAC1E;KACD,CAAC;EACJ;EAEArF,UAAUA,CAACqE,OAAuB;IAChC,IAAI,CAAC/B,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,YAAY,GAAG8B,OAAO,CAACiB,GAAI;IAChC,IAAI,CAAC9C,WAAW,GAAG6B,OAAO;EAC5B;EAEAkB,KAAKA,CAAA;IACH,IAAI,CAACjD,OAAO,GAAG,KAAK;IACpB,IAAI,CAACC,YAAY,GAAG,CAAC,CAAC;IACtB,IAAI,CAACC,WAAW,GAAG,EAAoB;EACzC;EAEAgD,iBAAiBA,CAAA;IACf,IAAI,CAACxC,UAAU,EAAE;EACnB;EAEAyC,gBAAgBA,CAAA;IACd,IAAI,CAACnD,OAAO,GAAG,KAAK;IACpB,IAAI,CAACP,KAAK,GAAG,KAAK;IAClB,IAAI,CAACQ,YAAY,GAAG,CAAC,CAAC;IACtB,IAAI,CAACO,qBAAqB,CAAC4C,KAAK,EAAE;EACpC;EAEAC,MAAMA,CAAA;IACJ,IAAI,CAAC9D,MAAM,CAAC+D,aAAa,CAAC,GAAG,CAAC;EAChC;EAAC,QAAAC,CAAA,G;qBAvIUvE,kBAAkB,EAAAxC,EAAA,CAAAgH,iBAAA,CAAAC,EAAA,CAAAC,oBAAA,GAAAlH,EAAA,CAAAgH,iBAAA,CAAAG,EAAA,CAAAzH,YAAA,GAAAM,EAAA,CAAAgH,iBAAA,CAAAI,EAAA,CAAAC,cAAA,GAAArH,EAAA,CAAAgH,iBAAA,CAAAM,EAAA,CAAAC,YAAA,GAAAvH,EAAA,CAAAgH,iBAAA,CAAAQ,EAAA,CAAAC,WAAA,GAAAzH,EAAA,CAAAgH,iBAAA,CAAAU,EAAA,CAAAC,MAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAlBpF,kBAAkB;IAAAqF,SAAA;IAAAC,SAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;;;;;;;qCAPlB,CACTrI,cAAc,EACdD,YAAY,CACb,GAAAM,EAAA,CAAAkI,mBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,4BAAAP,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;QC5BKhI,EAJR,CAAAC,cAAA,aAAqB,aACE,aACc,aACX,aACC;QAAAD,EAAA,CAAAE,MAAA,qCAAU;QAAAF,EAAA,CAAAG,YAAA,EAAM;QACnCH,EAAA,CAAAqB,UAAA,IAAAmH,yCAAA,uBAA2B;QA6EvBxI,EAFJ,CAAAC,cAAA,aAAiC,WACD,gBACqB;QAAnBD,EAAA,CAAAU,UAAA,mBAAA+H,oDAAA;UAAAzI,EAAA,CAAAY,aAAA,CAAA8H,GAAA;UAAA,OAAA1I,EAAA,CAAAiB,WAAA,CAASgH,GAAA,CAAApB,MAAA,EAAQ;QAAA,EAAC;QAAC7G,EAAA,CAAAE,MAAA,sCAAK;QAMlEF,EANkE,CAAAG,YAAA,EAAS,EAC7D,EACA,EACF,EACF,EACF,EACF;QAGNH,EAAA,CAAAC,cAAA,2BAC4C;QADJD,EAAA,CAAA2I,gBAAA,2BAAAC,uEAAAC,MAAA;UAAA7I,EAAA,CAAAY,aAAA,CAAA8H,GAAA;UAAA1I,EAAA,CAAA8I,kBAAA,CAAAb,GAAA,CAAAzE,OAAA,EAAAqF,MAAA,MAAAZ,GAAA,CAAAzE,OAAA,GAAAqF,MAAA;UAAA,OAAA7I,EAAA,CAAAiB,WAAA,CAAA4H,MAAA;QAAA,EAAqB;QAC3D7I,EAD4D,CAAAU,UAAA,8BAAAqI,0EAAA;UAAA/I,EAAA,CAAAY,aAAA,CAAA8H,GAAA;UAAA,OAAA1I,EAAA,CAAAiB,WAAA,CAAoBgH,GAAA,CAAAtB,gBAAA,EAAkB;QAAA,EAAC,+BAAAqC,2EAAA;UAAAhJ,EAAA,CAAAY,aAAA,CAAA8H,GAAA;UAAA,OAAA1I,EAAA,CAAAiB,WAAA,CAC9EgH,GAAA,CAAAvB,iBAAA,EAAmB;QAAA,EAAC;QAKnC1G,EAJN,CAAAC,cAAA,cAAyB,eACH,kBAEwG,gBAC5F;QAAAD,EAAA,CAAAE,MAAA,gCAAI;QAAAF,EAAA,CAAAG,YAAA,EAAO;QACvCH,EAAA,CAAAC,cAAA,eAAqF;QAApCD,EAAA,CAAAU,UAAA,mBAAAuI,kDAAA;UAAAjJ,EAAA,CAAAY,aAAA,CAAA8H,GAAA;UAAA,OAAA1I,EAAA,CAAAiB,WAAA,CAASgH,GAAA,CAAAvG,YAAA,CAAAuG,GAAA,CAAAvE,WAAA,CAAyB;QAAA,EAAC;QAExF1D,EAFI,CAAAG,YAAA,EAAqF,EAC9E,EACL;QAGFH,EAFJ,CAAAC,cAAA,eAAsD,eAC1B,sBACiD;QAApBD,EAAA,CAAA2I,gBAAA,2BAAAO,iEAAAL,MAAA;UAAA7I,EAAA,CAAAY,aAAA,CAAA8H,GAAA;UAAA1I,EAAA,CAAA8I,kBAAA,CAAAb,GAAA,CAAAhF,KAAA,EAAA4F,MAAA,MAAAZ,GAAA,CAAAhF,KAAA,GAAA4F,MAAA;UAAA,OAAA7I,EAAA,CAAAiB,WAAA,CAAA4H,MAAA;QAAA,EAAmB;QAE5E7I,EAF6E,CAAAG,YAAA,EAAa,EAClF,EACF;QACNH,EAAA,CAAAC,cAAA,eAA2E;QACzED,EAAA,CAAAE,MAAA,wFACF;QAAAF,EAAA,CAAAG,YAAA,EAAM;QACNH,EAAA,CAAAC,cAAA,eAAkB;QAChBD,EAAA,CAAAmB,SAAA,kCAA8D;QAGpEnB,EAFI,CAAAG,YAAA,EAAM,EACF,EACW;QACnBH,EAAA,CAAAmB,SAAA,mBAA6E;;;QA7GrEnB,EAAA,CAAAI,SAAA,GA0EC;QA1EDJ,EAAA,CAAAmJ,aAAA,IAAAlB,GAAA,CAAA1F,QAAA,CAAA6G,MAAA,cA0EC;QAYSpJ,EAAA,CAAAI,SAAA,GAAqB;QAArBJ,EAAA,CAAAiC,UAAA,aAAAgG,GAAA,CAAA/E,QAAA,CAAqB;QAAClD,EAAA,CAAAqJ,gBAAA,YAAApB,GAAA,CAAAzE,OAAA,CAAqB;QAYzCxD,EAAA,CAAAI,SAAA,GAAe;QAAfJ,EAAA,CAAAiC,UAAA,gBAAe;QAA0BjC,EAAA,CAAAqJ,gBAAA,YAAApB,GAAA,CAAAhF,KAAA,CAAmB;QAW1CjD,EAAA,CAAAI,SAAA,GAA4B;QAA5BJ,EAAA,CAAAsJ,UAAA,CAAAtJ,EAAA,CAAAuJ,eAAA,IAAAC,GAAA,EAA4B;;;mBD1F9DrK,YAAY,EAAEC,YAAY,EAAEC,YAAY,EAAAoK,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,IAAA,EACxCtK,cAAc,EAAAuK,EAAA,CAAAC,QAAA,EAAgBvK,WAAW,EAAAwK,EAAA,CAAAC,eAAA,EAAAD,EAAA,CAAAE,OAAA,EACzCrK,WAAW,EAAAsK,GAAA,CAAAC,KAAA,EACX1K,oBAAoB,EAAED,qBAAqB,EAAEK,cAAc;IAAAuK,MAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}