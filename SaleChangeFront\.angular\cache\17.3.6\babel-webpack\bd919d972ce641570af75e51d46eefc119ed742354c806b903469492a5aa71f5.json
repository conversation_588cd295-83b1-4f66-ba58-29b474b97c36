{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CheckboxModule } from 'primeng/checkbox';\nimport { FormsModule } from '@angular/forms';\nimport { CommonModule } from '@angular/common';\nimport { ButtonModule } from 'primeng/button';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../../services/api/services\";\nimport * as i2 from \"../../../../../services/File.service\";\nimport * as i3 from \"@angular/platform-browser\";\nimport * as i4 from \"primeng/checkbox\";\nimport * as i5 from \"primeng/button\";\nimport * as i6 from \"@angular/forms\";\nfunction StepIvChoiceComponent_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"object\", 2)(1, \"iframe\", 3)(2, \"p\");\n    i0.ɵɵtext(3, \"\\u60A8\\u7684\\u700F\\u89BD\\u5668\\u7121\\u6CD5\\u986F\\u793A PDF\\u3002\\u8ACB \");\n    i0.ɵɵelementStart(4, \"a\", 4);\n    i0.ɵɵtext(5, \" \\u9EDE\\u64CA\\u6B64\\u8655\\u4E0B\\u8F09 PDF \\u6587\\u4EF6 \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"data\", ctx_r0.safePdfUrl, i0.ɵɵsanitizeResourceUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", ctx_r0.safePdfUrl, i0.ɵɵsanitizeResourceUrl);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"href\", ctx_r0.safePdfUrl, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction StepIvChoiceComponent_Conditional_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 5)(1, \"div\", 6);\n    i0.ɵɵelement(2, \"div\", 7);\n    i0.ɵɵelementStart(3, \"h3\", 8);\n    i0.ɵɵtext(4, \"\\u8F09\\u5165\\u4E2D...\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 9);\n    i0.ɵɵtext(6, \"\\u6B63\\u5728\\u6E96\\u5099\\u5BA2\\u8B8A\\u8AAA\\u660E\\u6587\\u4EF6\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction StepIvChoiceComponent_Conditional_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"div\", 11)(2, \"div\", 12);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(3, \"svg\", 13);\n    i0.ɵɵelement(4, \"path\", 14)(5, \"path\", 15)(6, \"path\", 16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(7, \"h3\", 17);\n    i0.ɵɵtext(8, \"\\u5BA2\\u8B8A\\u8AAA\\u660E\\u6587\\u4EF6\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p\", 18);\n    i0.ɵɵtext(10, \"\\u6587\\u4EF6\\u6E96\\u5099\\u4E2D\\uFF0C\\u8ACB\\u7A0D\\u5F8C\\u91CD\\u65B0\\u6574\\u7406\\u9801\\u9762\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 19)(12, \"span\", 20);\n    i0.ɵɵtext(13, \"\\u6216\\u9EDE\\u64CA\\u4E0B\\u65B9\\u6309\\u9215\\u91CD\\u65B0\\u8F09\\u5165\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function StepIvChoiceComponent_Conditional_6_Template_button_click_14_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.refreshPdf());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(15, \"svg\", 22);\n    i0.ɵɵelement(16, \"path\", 23)(17, \"path\", 24)(18, \"path\", 25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(19, \" \\u91CD\\u65B0\\u8F09\\u5165 \");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction StepIvChoiceComponent_Conditional_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 26)(1, \"div\", 27)(2, \"div\", 28)(3, \"p-checkbox\", 29);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function StepIvChoiceComponent_Conditional_7_Template_p_checkbox_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r0.agreeToGoNextStep4, $event) || (ctx_r0.agreeToGoNextStep4 = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function StepIvChoiceComponent_Conditional_7_Template_p_checkbox_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.changeCheckbox($event));\n    });\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(4, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function StepIvChoiceComponent_Conditional_7_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.next());\n    });\n    i0.ɵɵtext(5, \" \\u4E0B\\u4E00\\u6B65 \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"binary\", true);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r0.agreeToGoNextStep4);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", !ctx_r0.agreeToGoNextStep4);\n  }\n}\nfunction StepIvChoiceComponent_Conditional_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"div\", 32)(2, \"div\", 33);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(3, \"svg\", 34);\n    i0.ɵɵelement(4, \"path\", 35);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(5, \"p\", 36);\n    i0.ɵɵtext(6, \" \\u76EE\\u524D\\u7121\\u6CD5\\u986F\\u793A\\u5BA2\\u8B8A\\u8AAA\\u660E\\u6587\\u4EF6\");\n    i0.ɵɵelement(7, \"br\");\n    i0.ɵɵtext(8, \" \\u5982\\u9700\\u7E7C\\u7E8C\\uFF0C\\u8ACB\\u78BA\\u8A8D\\u60A8\\u5DF2\\u4E86\\u89E3\\u76F8\\u95DC\\u5BA2\\u8B8A\\u898F\\u5247 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function StepIvChoiceComponent_Conditional_8_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.nextWithoutPdf());\n    });\n    i0.ɵɵtext(10, \" \\u78BA\\u8A8D\\u7E7C\\u7E8C \");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class StepIvChoiceComponent {\n  constructor(_houseService, _fileService, sanitizer) {\n    this._houseService = _houseService;\n    this._fileService = _fileService;\n    this.sanitizer = sanitizer;\n    this.agreeToGoNextStep4 = false;\n    this.agreeToGoNextStep4Change = new EventEmitter();\n    this.nextEvent = new EventEmitter();\n    this.detailPictureUrl = null;\n    this.safePdfUrl = null;\n    this.pdfDataUrl = null; // 添加 base64 data URL\n    this.isLoadingPdf = false; // 加入載入狀態\n  }\n  ngOnInit() {\n    this.getImage();\n  }\n  getImage() {\n    this.isLoadingPdf = true; // 開始載入\n    this._houseService.apiHouseGetSpecialNoticeFilePost$Json({}).subscribe(res => {\n      if (res.Entries && res.Entries.CFileName && res.Entries.CFileUrl) {\n        // 從 CFileUrl 中解析出相對路徑和檔名\n        const fileUrl = res.Entries.CFileUrl;\n        const fileName = res.Entries.CFileName; // 使用 FileService 取得 PDF blob\n        this._fileService.getFile(fileUrl, fileName).subscribe(response => {\n          if (response.body) {\n            this.detailPicture = response.body;\n            // 方法1: 創建 blob URL\n            this.detailPictureUrl = URL.createObjectURL(response.body);\n          }\n          this.safePdfUrl = this.sanitizer.bypassSecurityTrustResourceUrl(this.detailPictureUrl);\n          // 方法2: 轉換為 base64 data URL (更好的兼容性)\n          const reader = new FileReader();\n          reader.onload = () => {\n            this.pdfDataUrl = reader.result;\n            // 如果 blob URL 不工作，使用 data URL\n            if (this.pdfDataUrl) {\n              this.safePdfUrl = this.sanitizer.bypassSecurityTrustResourceUrl(this.pdfDataUrl);\n            }\n            this.isLoadingPdf = false; // 載入完成\n            console.log('PDF Data URL 已準備就緒');\n          };\n          reader.readAsDataURL(blob);\n          console.log('PDF URL 已準備就緒:', this.detailPictureUrl);\n        }, error => {\n          console.error('取得 PDF 文件時發生錯誤:', error);\n          this.isLoadingPdf = false; // 載入錯誤時也要停止載入狀態\n        });\n      } else {\n        this.isLoadingPdf = false; // 沒有檔案時停止載入狀態\n      }\n    }, error => {\n      console.error('取得檔案資訊時發生錯誤:', error);\n      this.isLoadingPdf = false; // 載入錯誤時也要停止載入狀態\n    });\n  }\n  next() {\n    this.nextEvent.emit();\n  }\n  changeCheckbox(event) {\n    this.agreeToGoNextStep4Change.emit(event);\n  }\n  refreshPdf() {\n    // 重新載入PDF文件\n    this.ngOnInit();\n  }\n  nextWithoutPdf() {\n    // 無PDF檔案時的下一步，可以加入額外的確認邏輯\n    this.nextEvent.emit();\n  }\n  ngOnDestroy() {\n    // 清理 blob URL 以避免記憶體洩漏\n    if (this.detailPictureUrl) {\n      URL.revokeObjectURL(this.detailPictureUrl);\n    }\n  }\n  static #_ = this.ɵfac = function StepIvChoiceComponent_Factory(t) {\n    return new (t || StepIvChoiceComponent)(i0.ɵɵdirectiveInject(i1.HouseService), i0.ɵɵdirectiveInject(i2.FileService), i0.ɵɵdirectiveInject(i3.DomSanitizer));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: StepIvChoiceComponent,\n    selectors: [[\"app-step-iv-choice\"]],\n    inputs: {\n      agreeToGoNextStep4: \"agreeToGoNextStep4\"\n    },\n    outputs: {\n      agreeToGoNextStep4Change: \"agreeToGoNextStep4Change\",\n      nextEvent: \"nextEvent\"\n    },\n    standalone: true,\n    features: [i0.ɵɵStandaloneFeature],\n    decls: 9,\n    vars: 2,\n    consts: [[1, \"flex\", \"flex-col\", \"justify-center\", \"items-center\", \"w-full\"], [1, \"text-black\", \"step-title\"], [\"type\", \"application/pdf\", \"width\", \"100%\", \"height\", \"500\", 1, \"mt-3\", \"w-full\", 2, \"border\", \"1px solid #ccc\", 3, \"data\"], [\"width\", \"100%\", \"height\", \"500\", \"title\", \"\\u5BA2\\u8B8A\\u8AAA\\u660EPDF\", 2, \"border\", \"1px solid #ccc\", 3, \"src\"], [\"download\", \"\\u5BA2\\u8B8A\\u8AAA\\u660E.pdf\", \"target\", \"_blank\", 3, \"href\"], [1, \"loading-container\"], [1, \"loading-content\"], [1, \"loading-spinner\"], [1, \"loading-title\"], [1, \"loading-description\"], [1, \"no-pdf-container\"], [1, \"no-pdf-content\"], [1, \"pdf-icon\"], [\"width\", \"48\", \"height\", \"48\", \"viewBox\", \"0 0 24 24\", \"fill\", \"none\", \"xmlns\", \"http://www.w3.org/2000/svg\"], [\"d\", \"M14 2H6C4.9 2 4 2.9 4 4v16c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V8l-6-6z\", \"stroke\", \"#6B7280\", \"stroke-width\", \"2\", \"fill\", \"none\"], [\"d\", \"M14 2v6h6\", \"stroke\", \"#6B7280\", \"stroke-width\", \"2\", \"fill\", \"none\"], [\"d\", \"M16 13H8M16 17H8M10 9H8\", \"stroke\", \"#6B7280\", \"stroke-width\", \"2\"], [1, \"no-pdf-title\"], [1, \"no-pdf-description\"], [1, \"refresh-hint\"], [1, \"refresh-text\"], [\"type\", \"button\", 1, \"refresh-btn\", 3, \"click\"], [\"width\", \"16\", \"height\", \"16\", \"viewBox\", \"0 0 24 24\", \"fill\", \"none\", \"xmlns\", \"http://www.w3.org/2000/svg\"], [\"d\", \"M3 12a9 9 0 019-9 9.75 9.75 0 016.74 2.74L21 8\", \"stroke\", \"currentColor\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"d\", \"M21 3v5h-5M21 12a9 9 0 01-9 9 9.75 9.75 0 01-6.74-2.74L3 16\", \"stroke\", \"currentColor\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"d\", \"M3 21v-5h5\", \"stroke\", \"currentColor\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [1, \"w-[100%]\", \"flex\", \"items-start\", \"justify-start\", \"sec-e-check\"], [1, \"text-stone-600\", \"checkbox-zone\", \"w-full\"], [1, \"bg-white\", 2, \"padding\", \"12px 16px\"], [\"label\", \"\\u6211\\u5DF2\\u95B1\\u8B80\\u4E26\\u540C\\u610F\\u4EE5\\u4E0A\\u8AAA\\u660E\\u5167\\u5BB9\\u3002\", 3, \"ngModelChange\", \"binary\", \"ngModel\"], [\"pButton\", \"\", 1, \"button2\", \"!w-48\", \"butn1\", \"!text-center\", \"flex\", \"justify-center\", \"items-center\", 3, \"click\", \"disabled\"], [1, \"w-[100%]\", \"flex\", \"flex-col\", \"items-center\", \"justify-center\", \"sec-e-check\"], [1, \"warning-box\", \"mb-4\"], [1, \"warning-icon\"], [\"width\", \"20\", \"height\", \"20\", \"viewBox\", \"0 0 24 24\", \"fill\", \"none\", \"xmlns\", \"http://www.w3.org/2000/svg\"], [\"d\", \"M12 9v4M12 17h.01M7.86 2h8.28L22 7.86v8.28L16.14 22H7.86L2 16.14V7.86L7.86 2z\", \"stroke\", \"#f59e0b\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [1, \"warning-text\"], [\"pButton\", \"\", 1, \"button2\", \"!w-48\", \"butn1\", \"!text-center\", \"flex\", \"justify-center\", \"items-center\", 3, \"click\"]],\n    template: function StepIvChoiceComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"section\")(1, \"div\", 0)(2, \"span\", 1);\n        i0.ɵɵtext(3, \" \\u5BA2\\u8B8A\\u539F\\u5247 \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(4, StepIvChoiceComponent_Conditional_4_Template, 6, 3, \"object\", 2)(5, StepIvChoiceComponent_Conditional_5_Template, 7, 0)(6, StepIvChoiceComponent_Conditional_6_Template, 20, 0)(7, StepIvChoiceComponent_Conditional_7_Template, 6, 3)(8, StepIvChoiceComponent_Conditional_8_Template, 11, 0);\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(4);\n        i0.ɵɵconditional(4, !!ctx.safePdfUrl ? 4 : ctx.isLoadingPdf ? 5 : 6);\n        i0.ɵɵadvance(3);\n        i0.ɵɵconditional(7, !!ctx.safePdfUrl ? 7 : 8);\n      }\n    },\n    dependencies: [CheckboxModule, i4.Checkbox, ButtonModule, i5.ButtonDirective, FormsModule, i6.NgControlStatus, i6.NgModel, CommonModule],\n    styles: [\"@charset \\\"UTF-8\\\";@keyframes _ngcontent-%COMP%_radioAppear{0%{transform:translate(-50%,-50%) scale(0);opacity:0}50%{transform:translate(-50%,-50%) scale(1.3);opacity:.8}to{transform:translate(-50%,-50%) scale(1);opacity:1}}@keyframes _ngcontent-%COMP%_radioRipple{0%{width:0;height:0;opacity:.6}to{width:44px;height:44px;opacity:0}}@keyframes _ngcontent-%COMP%_buttonPress{0%{transform:scale(1)}50%{transform:scale(.98)}to{transform:scale(1)}}@keyframes _ngcontent-%COMP%_fadeInUp{0%{opacity:0;transform:translateY(20px)}to{opacity:1;transform:translateY(0)}}@keyframes _ngcontent-%COMP%_slideInRight{0%{opacity:0;transform:translate(30px)}to{opacity:1;transform:translate(0)}}@keyframes _ngcontent-%COMP%_shimmer{0%{background-position:-200px 0}to{background-position:200px 0}}@keyframes _ngcontent-%COMP%_pulse{0%,to{transform:scale(1);opacity:1}50%{transform:scale(1.05);opacity:.8}}.animate-fade-in-up[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fadeInUp .6s ease-out}.animate-slide-in-right[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_slideInRight .4s ease-out}.animate-pulse[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_pulse 2s infinite}.animate-shimmer[_ngcontent-%COMP%]{background:linear-gradient(90deg,transparent,rgba(255,255,255,.4),transparent);background-size:200px 100%;animation:_ngcontent-%COMP%_shimmer 1.5s infinite}.hover-lift[_ngcontent-%COMP%]{transition:transform .3s ease,box-shadow .3s ease}.hover-lift[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 4px 12px #00000026}.hover-scale[_ngcontent-%COMP%]{transition:transform .3s ease}.hover-scale[_ngcontent-%COMP%]:hover{transform:scale(1.02)}.hover-glow[_ngcontent-%COMP%]{transition:box-shadow .3s ease}.hover-glow[_ngcontent-%COMP%]:hover{box-shadow:0 0 20px #b8a6764d}html[_ngcontent-%COMP%]{line-height:initial;-webkit-text-size-adjust:100%;width:100%;height:100%}body[_ngcontent-%COMP%]{margin:0;font-size:1rem;color:#23181599;font-family:Noto Sans TC!important;box-sizing:border-box;width:100%;height:auto;overflow:auto;overscroll-behavior:none;letter-spacing:.32px}html[_ngcontent-%COMP%], body[_ngcontent-%COMP%]{-webkit-text-size-adjust:none;text-size-adjust:none}main[_ngcontent-%COMP%]{display:block}h1[_ngcontent-%COMP%]{font-size:1.75rem;font-weight:400}hr[_ngcontent-%COMP%]{box-sizing:content-box;height:0;overflow:visible;margin:10px 0;border:none;border-top:1px solid #000}pre[_ngcontent-%COMP%]{font-family:monospace,monospace;font-size:1rem}a[_ngcontent-%COMP%]{background-color:transparent;text-decoration:none;-webkit-tap-highlight-color:transparent}abbr[title][_ngcontent-%COMP%]{border-bottom:none;text-decoration:underline;-webkit-text-decoration:underline dotted;text-decoration:underline dotted}b[_ngcontent-%COMP%]{font-family:Noto Sans TC;font-weight:500}strong[_ngcontent-%COMP%]{font-family:Noto Sans TC;font-weight:600}code[_ngcontent-%COMP%], kbd[_ngcontent-%COMP%], samp[_ngcontent-%COMP%]{font-family:monospace,monospace;font-size:1em}small[_ngcontent-%COMP%]{font-size:80%}sub[_ngcontent-%COMP%], sup[_ngcontent-%COMP%]{font-size:75%;line-height:0;position:relative;vertical-align:baseline}sub[_ngcontent-%COMP%]{bottom:-.25em}sup[_ngcontent-%COMP%]{top:-.5em}p[_ngcontent-%COMP%]{font-size:1rem}img[_ngcontent-%COMP%]{border-style:none;max-width:100%}button[_ngcontent-%COMP%], input[_ngcontent-%COMP%], optgroup[_ngcontent-%COMP%], select[_ngcontent-%COMP%], textarea[_ngcontent-%COMP%]{font-family:Noto Sans TC,\\\\5fae\\\\8edf\\\\6b63\\\\9ed1\\\\9ad4,Arial,sans-serif;font-size:1rem;line-height:1.15;margin:0;color:#2f2f2f;-webkit-tap-highlight-color:transparent;letter-spacing:2px}select[_ngcontent-%COMP%]:focus, input[_ngcontent-%COMP%]:focus, button[_ngcontent-%COMP%]:focus{outline:none}button[_ngcontent-%COMP%]{padding:0;background-color:transparent;border:none;cursor:pointer}button[_ngcontent-%COMP%], input[_ngcontent-%COMP%]{overflow:visible}button[_ngcontent-%COMP%], select[_ngcontent-%COMP%]{text-transform:none}button[_ngcontent-%COMP%], [type=button][_ngcontent-%COMP%], [type=reset][_ngcontent-%COMP%], [type=submit][_ngcontent-%COMP%]{-webkit-appearance:button}button[_ngcontent-%COMP%]::-moz-focus-inner, [type=button][_ngcontent-%COMP%]::-moz-focus-inner, [type=reset][_ngcontent-%COMP%]::-moz-focus-inner, [type=submit][_ngcontent-%COMP%]::-moz-focus-inner{border-style:none;padding:0}button[_ngcontent-%COMP%]:-moz-focusring, [type=button][_ngcontent-%COMP%]:-moz-focusring, [type=reset][_ngcontent-%COMP%]:-moz-focusring, [type=submit][_ngcontent-%COMP%]:-moz-focusring{outline:1px dotted ButtonText}fieldset[_ngcontent-%COMP%]{padding:.35em .75em .625em}div[role=tab][_ngcontent-%COMP%]{-webkit-tap-highlight-color:transparent}legend[_ngcontent-%COMP%]{box-sizing:border-box;color:inherit;display:table;max-width:100%;padding:0;white-space:normal}progress[_ngcontent-%COMP%]{vertical-align:baseline}textarea[_ngcontent-%COMP%]{overflow:auto}[type=checkbox][_ngcontent-%COMP%], [type=radio][_ngcontent-%COMP%]{box-sizing:border-box;padding:0}[type=number][_ngcontent-%COMP%]::-webkit-inner-spin-button, [type=number][_ngcontent-%COMP%]::-webkit-outer-spin-button{height:auto}[type=search][_ngcontent-%COMP%]{-webkit-appearance:textfield;outline-offset:-2px}[type=search][_ngcontent-%COMP%]::-webkit-search-decoration{-webkit-appearance:none}[_ngcontent-%COMP%]::-webkit-file-upload-button{-webkit-appearance:button;font:inherit}details[_ngcontent-%COMP%]{display:block}summary[_ngcontent-%COMP%]{display:list-item}template[_ngcontent-%COMP%]{display:none}[hidden][_ngcontent-%COMP%]{display:none}table[_ngcontent-%COMP%]{border-spacing:0px}ol[_ngcontent-%COMP%], ul[_ngcontent-%COMP%]{padding-left:0;margin-top:0}.form-check-input[_ngcontent-%COMP%]:focus{outline:none;box-shadow:none}.container[_ngcontent-%COMP%]{width:100%}@media (min-width: 640px){.container[_ngcontent-%COMP%]{max-width:640px}}@media (min-width: 768px){.container[_ngcontent-%COMP%]{max-width:768px}}@media (min-width: 1024px){.container[_ngcontent-%COMP%]{max-width:1024px}}@media (min-width: 1280px){.container[_ngcontent-%COMP%]{max-width:1280px}}@media (min-width: 1536px){.container[_ngcontent-%COMP%]{max-width:1536px}}.visible[_ngcontent-%COMP%]{visibility:visible}.collapse[_ngcontent-%COMP%]{visibility:collapse}.static[_ngcontent-%COMP%]{position:static}.fixed[_ngcontent-%COMP%]{position:fixed}.absolute[_ngcontent-%COMP%]{position:absolute}.relative[_ngcontent-%COMP%]{position:relative}.inset-0[_ngcontent-%COMP%]{inset:0}.inset-y-0[_ngcontent-%COMP%]{top:0;bottom:0}.bottom-0[_ngcontent-%COMP%]{bottom:0}.bottom-1[_ngcontent-%COMP%]{bottom:.25rem}.bottom-2[_ngcontent-%COMP%]{bottom:.5rem}.bottom-3[_ngcontent-%COMP%]{bottom:.75rem}.left-0[_ngcontent-%COMP%]{left:0}.left-1\\\\/2[_ngcontent-%COMP%]{left:50%}.left-2[_ngcontent-%COMP%]{left:.5rem}.left-3[_ngcontent-%COMP%]{left:.75rem}.right-0[_ngcontent-%COMP%]{right:0}.right-1[_ngcontent-%COMP%]{right:.25rem}.right-2[_ngcontent-%COMP%]{right:.5rem}.right-3[_ngcontent-%COMP%]{right:.75rem}.top-1[_ngcontent-%COMP%]{top:.25rem}.top-1\\\\/2[_ngcontent-%COMP%]{top:50%}.top-2[_ngcontent-%COMP%]{top:.5rem}.top-3[_ngcontent-%COMP%]{top:.75rem}.z-10[_ngcontent-%COMP%]{z-index:10}.z-50[_ngcontent-%COMP%]{z-index:50}.m-0[_ngcontent-%COMP%]{margin:0}.m-2[_ngcontent-%COMP%]{margin:.5rem}.m-auto[_ngcontent-%COMP%]{margin:auto}.mx-3[_ngcontent-%COMP%]{margin-left:.75rem;margin-right:.75rem}.mx-4[_ngcontent-%COMP%]{margin-left:1rem;margin-right:1rem}.mx-5[_ngcontent-%COMP%]{margin-left:1.25rem;margin-right:1.25rem}.mx-\\\\__ph-0__[_ngcontent-%COMP%]{margin-left:20%;margin-right:20%}.my-12[_ngcontent-%COMP%]{margin-top:3rem;margin-bottom:3rem}.my-2[_ngcontent-%COMP%]{margin-top:.5rem;margin-bottom:.5rem}.my-4[_ngcontent-%COMP%]{margin-top:1rem;margin-bottom:1rem}.my-6[_ngcontent-%COMP%]{margin-top:1.5rem;margin-bottom:1.5rem}.\\\\!mt-0[_ngcontent-%COMP%]{margin-top:0!important}.mb-2[_ngcontent-%COMP%]{margin-bottom:.5rem}.mb-3[_ngcontent-%COMP%]{margin-bottom:.75rem}.mb-4[_ngcontent-%COMP%]{margin-bottom:1rem}.mb-6[_ngcontent-%COMP%]{margin-bottom:1.5rem}.ml-1[_ngcontent-%COMP%]{margin-left:.25rem}.ml-2[_ngcontent-%COMP%]{margin-left:.5rem}.mr-1[_ngcontent-%COMP%]{margin-right:.25rem}.mr-2[_ngcontent-%COMP%]{margin-right:.5rem}.mr-3[_ngcontent-%COMP%]{margin-right:.75rem}.mr-4[_ngcontent-%COMP%]{margin-right:1rem}.mr-5[_ngcontent-%COMP%]{margin-right:1.25rem}.mr-8[_ngcontent-%COMP%]{margin-right:2rem}.mt-1[_ngcontent-%COMP%]{margin-top:.25rem}.mt-10[_ngcontent-%COMP%]{margin-top:2.5rem}.mt-14[_ngcontent-%COMP%]{margin-top:3.5rem}.mt-2[_ngcontent-%COMP%]{margin-top:.5rem}.mt-3[_ngcontent-%COMP%]{margin-top:.75rem}.mt-4[_ngcontent-%COMP%]{margin-top:1rem}.mt-5[_ngcontent-%COMP%]{margin-top:1.25rem}.mt-6[_ngcontent-%COMP%]{margin-top:1.5rem}.mt-7[_ngcontent-%COMP%]{margin-top:1.75rem}.mt-8[_ngcontent-%COMP%]{margin-top:2rem}.mt-\\\\__ph-0__[_ngcontent-%COMP%]{margin-top:16px}.block[_ngcontent-%COMP%]{display:block}.flex[_ngcontent-%COMP%]{display:flex}.table[_ngcontent-%COMP%]{display:table}.table-row[_ngcontent-%COMP%]{display:table-row}.grid[_ngcontent-%COMP%]{display:grid}.hidden[_ngcontent-%COMP%]{display:none}.\\\\!h-\\\\__ph-0__[_ngcontent-%COMP%]{height:107px!important}.\\\\!h-full[_ngcontent-%COMP%]{height:100%!important}.h-10[_ngcontent-%COMP%]{height:2.5rem}.h-16[_ngcontent-%COMP%]{height:4rem}.h-28[_ngcontent-%COMP%]{height:7rem}.h-32[_ngcontent-%COMP%]{height:8rem}.h-6[_ngcontent-%COMP%]{height:1.5rem}.h-8[_ngcontent-%COMP%]{height:2rem}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:100px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:145px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:280px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:40vh}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:47px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:480px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:4px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:50px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:55px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:665px}.h-\\\\__ph-0__[_ngcontent-%COMP%]{height:80px}.h-auto[_ngcontent-%COMP%]{height:auto}.h-fit[_ngcontent-%COMP%]{height:-moz-fit-content;height:fit-content}.max-h-\\\\__ph-0__[_ngcontent-%COMP%]{max-height:200px}.max-h-\\\\__ph-0__[_ngcontent-%COMP%]{max-height:500px}.max-h-full[_ngcontent-%COMP%]{max-height:100%}.max-h-screen[_ngcontent-%COMP%]{max-height:100vh}.\\\\!min-h-\\\\__ph-0__[_ngcontent-%COMP%]{min-height:60vh!important}.\\\\!w-40[_ngcontent-%COMP%]{width:10rem!important}.\\\\!w-48[_ngcontent-%COMP%]{width:12rem!important}.\\\\!w-\\\\__ph-0__[_ngcontent-%COMP%]{width:107px!important}.\\\\!w-\\\\__ph-0__[_ngcontent-%COMP%]{width:120px!important}.\\\\!w-full[_ngcontent-%COMP%]{width:100%!important}.w-1\\\\/3[_ngcontent-%COMP%]{width:33.333333%}.w-10[_ngcontent-%COMP%]{width:2.5rem}.w-16[_ngcontent-%COMP%]{width:4rem}.w-2\\\\/3[_ngcontent-%COMP%]{width:66.666667%}.w-28[_ngcontent-%COMP%]{width:7rem}.w-6[_ngcontent-%COMP%]{width:1.5rem}.w-7\\\\/12[_ngcontent-%COMP%]{width:58.333333%}.w-8[_ngcontent-%COMP%]{width:2rem}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:100%}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:150px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:160px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:180px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:30rem}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:400px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:45%}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:485px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:50%}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:550px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:660px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:760px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:80%}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:80px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:968px}.w-\\\\__ph-0__[_ngcontent-%COMP%]{width:97%}.w-full[_ngcontent-%COMP%]{width:100%}.\\\\!max-w-full[_ngcontent-%COMP%]{max-width:100%!important}.max-w-4xl[_ngcontent-%COMP%]{max-width:56rem}.max-w-\\\\__ph-0__[_ngcontent-%COMP%]{max-width:1216px}.max-w-full[_ngcontent-%COMP%]{max-width:100%}.flex-1[_ngcontent-%COMP%]{flex:1 1 0%}.flex-shrink[_ngcontent-%COMP%]{flex-shrink:1}.flex-shrink-0[_ngcontent-%COMP%]{flex-shrink:0}.grow[_ngcontent-%COMP%]{flex-grow:1}.border-collapse[_ngcontent-%COMP%]{border-collapse:collapse}.-translate-x-1\\\\/2[_ngcontent-%COMP%]{--tw-translate-x: -50%;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.-translate-y-1\\\\/2[_ngcontent-%COMP%]{--tw-translate-y: -50%;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.transform[_ngcontent-%COMP%]{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.grid-cols-1[_ngcontent-%COMP%]{grid-template-columns:repeat(1,minmax(0,1fr))}.grid-cols-10[_ngcontent-%COMP%]{grid-template-columns:repeat(10,minmax(0,1fr))}.grid-cols-2[_ngcontent-%COMP%]{grid-template-columns:repeat(2,minmax(0,1fr))}.grid-cols-3[_ngcontent-%COMP%]{grid-template-columns:repeat(3,minmax(0,1fr))}.grid-cols-4[_ngcontent-%COMP%]{grid-template-columns:repeat(4,minmax(0,1fr))}.flex-row[_ngcontent-%COMP%]{flex-direction:row}.flex-col[_ngcontent-%COMP%]{flex-direction:column}.flex-wrap[_ngcontent-%COMP%]{flex-wrap:wrap}.items-start[_ngcontent-%COMP%]{align-items:flex-start}.items-center[_ngcontent-%COMP%]{align-items:center}.justify-start[_ngcontent-%COMP%]{justify-content:flex-start}.justify-end[_ngcontent-%COMP%]{justify-content:flex-end}.justify-center[_ngcontent-%COMP%]{justify-content:center}.justify-between[_ngcontent-%COMP%]{justify-content:space-between}.gap-2[_ngcontent-%COMP%]{gap:.5rem}.gap-3[_ngcontent-%COMP%]{gap:.75rem}.gap-4[_ngcontent-%COMP%]{gap:1rem}.gap-x-1[_ngcontent-%COMP%]{column-gap:.25rem}.self-center[_ngcontent-%COMP%]{align-self:center}.overflow-hidden[_ngcontent-%COMP%]{overflow:hidden}.overflow-y-scroll[_ngcontent-%COMP%]{overflow-y:scroll}.break-words[_ngcontent-%COMP%]{overflow-wrap:break-word}.rounded[_ngcontent-%COMP%]{border-radius:.25rem}.rounded-3xl[_ngcontent-%COMP%]{border-radius:1.5rem}.rounded-full[_ngcontent-%COMP%]{border-radius:9999px}.rounded-lg[_ngcontent-%COMP%]{border-radius:.5rem}.rounded-md[_ngcontent-%COMP%]{border-radius:.375rem}.rounded-b[_ngcontent-%COMP%]{border-bottom-right-radius:.25rem;border-bottom-left-radius:.25rem}.border[_ngcontent-%COMP%]{border-width:1px}.border-2[_ngcontent-%COMP%]{border-width:2px}.border-solid[_ngcontent-%COMP%]{border-style:solid}.border-blue-400[_ngcontent-%COMP%]{--tw-border-opacity: 1;border-color:rgb(96 165 250 / var(--tw-border-opacity))}.border-blue-500[_ngcontent-%COMP%]{--tw-border-opacity: 1;border-color:rgb(59 130 246 / var(--tw-border-opacity))}.border-gray-300[_ngcontent-%COMP%]{--tw-border-opacity: 1;border-color:rgb(209 213 219 / var(--tw-border-opacity))}.bg-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-bg-opacity: 1;background-color:rgb(0 143 199 / var(--tw-bg-opacity))}.bg-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-bg-opacity: 1;background-color:rgb(184 166 118 / var(--tw-bg-opacity))}.bg-\\\\__ph-0__[_ngcontent-%COMP%]{background-color:#f3f1ea80}.bg-\\\\__ph-0__[_ngcontent-%COMP%]{background-color:#f3f1ea99}.bg-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-bg-opacity: 1;background-color:rgb(217 217 217 / var(--tw-bg-opacity))}.bg-black[_ngcontent-%COMP%]{--tw-bg-opacity: 1;background-color:rgb(0 0 0 / var(--tw-bg-opacity))}.bg-gray-500[_ngcontent-%COMP%]{--tw-bg-opacity: 1;background-color:rgb(107 114 128 / var(--tw-bg-opacity))}.bg-white[_ngcontent-%COMP%]{--tw-bg-opacity: 1;background-color:rgb(255 255 255 / var(--tw-bg-opacity))}.bg-opacity-50[_ngcontent-%COMP%]{--tw-bg-opacity: .5}.bg-opacity-70[_ngcontent-%COMP%]{--tw-bg-opacity: .7}.bg-opacity-75[_ngcontent-%COMP%]{--tw-bg-opacity: .75}.bg-opacity-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-bg-opacity: .04}.object-contain[_ngcontent-%COMP%]{object-fit:contain}.object-cover[_ngcontent-%COMP%]{object-fit:cover}.p-2[_ngcontent-%COMP%]{padding:.5rem}.p-4[_ngcontent-%COMP%]{padding:1rem}.p-\\\\__ph-0__[_ngcontent-%COMP%]{padding:16px}.px-1[_ngcontent-%COMP%]{padding-left:.25rem;padding-right:.25rem}.px-2[_ngcontent-%COMP%]{padding-left:.5rem;padding-right:.5rem}.px-3[_ngcontent-%COMP%]{padding-left:.75rem;padding-right:.75rem}.px-4[_ngcontent-%COMP%]{padding-left:1rem;padding-right:1rem}.px-6[_ngcontent-%COMP%]{padding-left:1.5rem;padding-right:1.5rem}.py-0[_ngcontent-%COMP%]{padding-top:0;padding-bottom:0}.py-0\\\\.5[_ngcontent-%COMP%]{padding-top:.125rem;padding-bottom:.125rem}.py-1[_ngcontent-%COMP%]{padding-top:.25rem;padding-bottom:.25rem}.py-2[_ngcontent-%COMP%]{padding-top:.5rem;padding-bottom:.5rem}.py-3[_ngcontent-%COMP%]{padding-top:.75rem;padding-bottom:.75rem}.py-4[_ngcontent-%COMP%]{padding-top:1rem;padding-bottom:1rem}.pb-4[_ngcontent-%COMP%]{padding-bottom:1rem}.pb-5[_ngcontent-%COMP%]{padding-bottom:1.25rem}.pb-8[_ngcontent-%COMP%]{padding-bottom:2rem}.pl-4[_ngcontent-%COMP%]{padding-left:1rem}.pl-6[_ngcontent-%COMP%]{padding-left:1.5rem}.pr-1[_ngcontent-%COMP%]{padding-right:.25rem}.pr-3[_ngcontent-%COMP%]{padding-right:.75rem}.pr-4[_ngcontent-%COMP%]{padding-right:1rem}.text-left[_ngcontent-%COMP%]{text-align:left}.\\\\!text-center[_ngcontent-%COMP%]{text-align:center!important}.text-center[_ngcontent-%COMP%]{text-align:center}.text-right[_ngcontent-%COMP%]{text-align:right}.text-2xl[_ngcontent-%COMP%]{font-size:1.5rem;line-height:2rem}.text-3xl[_ngcontent-%COMP%]{font-size:1.875rem;line-height:2.25rem}.text-base[_ngcontent-%COMP%]{font-size:1rem;line-height:1.5rem}.text-lg[_ngcontent-%COMP%]{font-size:1.125rem;line-height:1.75rem}.text-sm[_ngcontent-%COMP%]{font-size:.875rem;line-height:1.25rem}.text-xl[_ngcontent-%COMP%]{font-size:1.25rem;line-height:1.75rem}.text-xs[_ngcontent-%COMP%]{font-size:.75rem;line-height:1rem}.font-bold[_ngcontent-%COMP%]{font-weight:700}.font-light[_ngcontent-%COMP%]{font-weight:300}.font-medium[_ngcontent-%COMP%]{font-weight:500}.font-normal[_ngcontent-%COMP%]{font-weight:400}.font-semibold[_ngcontent-%COMP%]{font-weight:600}.leading-relaxed[_ngcontent-%COMP%]{line-height:1.625}.tracking-wider[_ngcontent-%COMP%]{letter-spacing:.05em}.text-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(0 143 199 / var(--tw-text-opacity))}.text-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(35 24 21 / var(--tw-text-opacity))}.text-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(51 51 51 / var(--tw-text-opacity))}.text-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(182 152 103 / var(--tw-text-opacity))}.text-\\\\__ph-0__[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(184 166 118 / var(--tw-text-opacity))}.text-black[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(0 0 0 / var(--tw-text-opacity))}.text-blue-400[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(96 165 250 / var(--tw-text-opacity))}.text-gray-500[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(107 114 128 / var(--tw-text-opacity))}.text-gray-600[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(75 85 99 / var(--tw-text-opacity))}.text-stone-600[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(87 83 78 / var(--tw-text-opacity))}.text-stone-900[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(28 25 23 / var(--tw-text-opacity))}.text-white[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(255 255 255 / var(--tw-text-opacity))}.opacity-50[_ngcontent-%COMP%]{opacity:.5}.shadow[_ngcontent-%COMP%]{--tw-shadow: 0 1px 3px 0 rgb(0 0 0 / .1), 0 1px 2px -1px rgb(0 0 0 / .1);--tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.grayscale[_ngcontent-%COMP%]{--tw-grayscale: grayscale(100%);filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)}.filter[_ngcontent-%COMP%]{filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)}.transition[_ngcontent-%COMP%]{transition-property:color,background-color,border-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,-webkit-backdrop-filter;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,backdrop-filter;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,backdrop-filter,-webkit-backdrop-filter;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.15s}.transition-all[_ngcontent-%COMP%]{transition-property:all;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.15s}.transition-shadow[_ngcontent-%COMP%]{transition-property:box-shadow;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.15s}.duration-200[_ngcontent-%COMP%]{transition-duration:.2s}.ease-in-out[_ngcontent-%COMP%]{transition-timing-function:cubic-bezier(.4,0,.2,1)}.content[_ngcontent-%COMP%]{width:100%}@media screen and (max-width: 1024px){.content[_ngcontent-%COMP%]{width:100%;padding:0 16px 50px}}.pc[_ngcontent-%COMP%], .inpc[_ngcontent-%COMP%]{display:initial}@media screen and (max-width: 1024px){.inpc[_ngcontent-%COMP%]{display:none}}.flat[_ngcontent-%COMP%]{display:none}@media screen and (max-width: 1024px){.flat[_ngcontent-%COMP%]{display:initial}}.mb[_ngcontent-%COMP%]{display:none}@media screen and (max-width: 912px){.mb[_ngcontent-%COMP%]{display:initial}}.cursor-pointer[_ngcontent-%COMP%]{cursor:pointer}.wrapper[_ngcontent-%COMP%]{display:flex;justify-content:center}.wrapper[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]{position:relative}.wrapper[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]:after{content:\\\"\\\";position:absolute;background-image:url(/assets/background/background2.png);width:180px;height:150px;background-size:unset;background-position:left;z-index:-1;right:0;bottom:20px}.button1[_ngcontent-%COMP%]{width:200px;height:47px;border:1px solid #CDCDCD;color:#3a4246;border-radius:28px;background-origin:border-box;background-clip:content-box,border-box;transition:all .3s ease}.button1[_ngcontent-%COMP%]:hover{border-color:#b8a676;color:#ae9b66;background-color:#b8a6760d}.button1[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#b8a676;transition:color .3s ease}.button1[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{color:#ae9b66}@media screen and (max-width: 912px){.button1[_ngcontent-%COMP%]{width:100%}}.button2[_ngcontent-%COMP%]{width:200px;padding:12px 24px;height:47px;color:#fff;background:linear-gradient(90deg,#ae9b66,#b8a676);border-radius:24px;box-shadow:0 2px 8px #ae9b6633;transition:all .3s ease}.button2[_ngcontent-%COMP%]:hover{background:linear-gradient(90deg,#9b8a5a,#ae9b66);transform:translateY(-1px);box-shadow:0 4px 12px #ae9b664d}.button2[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#fff}@media screen and (max-width: 912px){.button2[_ngcontent-%COMP%]{width:100%}}.button3[_ngcontent-%COMP%]{width:180px;padding:12px 24px;height:47px;color:#fff;background:linear-gradient(90deg,#ae9b66,#b8a676);border-radius:24px;box-shadow:0 2px 8px #ae9b6633;transition:all .3s ease}.button3[_ngcontent-%COMP%]:hover{background:linear-gradient(90deg,#9b8a5a,#ae9b66);transform:translateY(-1px);box-shadow:0 4px 12px #ae9b664d}.button3[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#fff}@media screen and (max-width: 912px){.button3[_ngcontent-%COMP%]{width:100%}}.button4[_ngcontent-%COMP%]{width:180px;padding:12px 24px;height:47px;color:#fff;background:#979797;border-radius:24px;transition:all .3s ease}.button4[_ngcontent-%COMP%]:hover{background:#7e7e7e}.button4[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#fff}@media screen and (max-width: 912px){.button4[_ngcontent-%COMP%]{width:100%}}.checkbox-zone[_ngcontent-%COMP%]{padding:2px;border-radius:2px;position:relative;background:linear-gradient(90deg,#ae9b66,#b8a676);box-shadow:0 1px 4px #ae9b664d}.avoid-resize-img[_ngcontent-%COMP%]{max-width:none}.fit-size[_ngcontent-%COMP%]{height:inherit;width:inherit;object-fit:cover;object-position:center}.p-calendar[_ngcontent-%COMP%]{width:100%}.p-calendar[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{border-bottom:1px solid rgba(144,150,157,.4);border-radius:0}.p-calendar[_ngcontent-%COMP%]   .p-inputtext[_ngcontent-%COMP%]{border:none;border-radius:0;border-bottom:1px solid rgba(144,150,157,.4);padding:12.5px 0;color:#23181599}@media screen and (max-width: 912px){.p-calendar[_ngcontent-%COMP%]   .p-inputtext[_ngcontent-%COMP%]{background:transparent}}.p-dropdown[_ngcontent-%COMP%]{border:none;width:100%}.p-dropdown[_ngcontent-%COMP%]   .p-inputtext[_ngcontent-%COMP%]{border:none;border-radius:0;border-bottom:1px solid rgba(144,150,157,.4);padding:12.5px 0;color:#231815}@media screen and (max-width: 912px){.p-dropdown[_ngcontent-%COMP%]   .p-inputtext[_ngcontent-%COMP%]{background:transparent}}.p-dropdown[_ngcontent-%COMP%]   .p-dropdown-trigger[_ngcontent-%COMP%]{border-radius:0;border-bottom:1px solid rgba(144,150,157,.4)}.p-dropdown[_ngcontent-%COMP%]:not(.p-disabled).p-focus{box-shadow:none}.p-radiobutton[_ngcontent-%COMP%]{position:relative;display:inline-flex;align-items:center}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box[_ngcontent-%COMP%]{width:22px;height:22px;border:2px solid #CDCDCD;background-color:#fff;border-radius:50%;transition:all .3s cubic-bezier(.25,.46,.45,.94);position:relative;box-shadow:0 1px 3px #ae9b661a;cursor:pointer}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box[_ngcontent-%COMP%]:hover{border-color:#b8a676;background:radial-gradient(circle at center,#b8a67614,#b8a67605);transform:scale(1.05);box-shadow:0 2px 8px #ae9b6626}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box[_ngcontent-%COMP%]:focus{outline:none;box-shadow:0 0 0 3px #b8a67640,0 2px 8px #ae9b6633;border-color:#a39460}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box[_ngcontent-%COMP%]:active{transform:scale(.98)}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box.p-highlight[_ngcontent-%COMP%]{background:linear-gradient(90deg,#ae9b66,#b8a676);border-color:#ae9b66;box-shadow:0 2px 12px #ae9b6640,inset 0 1px 2px #fff3}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box.p-highlight[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:50%;left:50%;width:8px;height:8px;background:radial-gradient(circle,#fff,#ffffffe6);border-radius:50%;transform:translate(-50%,-50%) scale(0);box-shadow:0 1px 2px #0003;animation:_ngcontent-%COMP%_radioAppear .2s ease forwards}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box.p-highlight[_ngcontent-%COMP%]:hover{background:linear-gradient(90deg,#9b8a5a,#ae9b66);border-color:#9b8a5a;transform:scale(1.05);box-shadow:0 3px 15px #ae9b6659,inset 0 1px 2px #ffffff40}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box.p-highlight[_ngcontent-%COMP%]:focus{box-shadow:0 0 0 3px #b8a6764d,0 3px 15px #ae9b6659}.p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-icon[_ngcontent-%COMP%]{display:none}@keyframes _ngcontent-%COMP%_radioAppear{0%{transform:translate(-50%,-50%) scale(0);opacity:0}50%{transform:translate(-50%,-50%) scale(1.2);opacity:.8}to{transform:translate(-50%,-50%) scale(1);opacity:1}}@keyframes _ngcontent-%COMP%_radioRipple{0%{transform:translate(-50%,-50%) scale(0);opacity:.6}to{transform:translate(-50%,-50%) scale(4);opacity:0}}.p-panel[_ngcontent-%COMP%]   .p-panel-header[_ngcontent-%COMP%]{background-color:#fff;color:#846a52;font-size:16px;border:1px solid #E6F0F3;border-radius:6px;padding:16px 32px}.p-panel-expanded[_ngcontent-%COMP%]   .p-panel-header[_ngcontent-%COMP%]{padding:16px 32px 8px;border-bottom:none!important;border-bottom-right-radius:0;border-bottom-left-radius:0}@media screen and (max-width: 912px){.p-panel-expanded[_ngcontent-%COMP%]   .p-panel-header[_ngcontent-%COMP%]{padding:16px 16px 8px}}.p-panel[_ngcontent-%COMP%]   .p-panel-header[_ngcontent-%COMP%]   .p-panel-header-icon[_ngcontent-%COMP%]{transform:rotate(-180deg)!important;transition:transform .3s linear}.p-panel-expanded[_ngcontent-%COMP%]   .p-panel-header[_ngcontent-%COMP%]   .p-panel-header-icon[_ngcontent-%COMP%]{transform:rotate(0)!important;transition:transform .3s linear}.p-panel[_ngcontent-%COMP%]   .p-panel-header[_ngcontent-%COMP%]   .p-panel-header-icon[_ngcontent-%COMP%]:focus{box-shadow:none}.p-panel[_ngcontent-%COMP%]   .p-panel-content[_ngcontent-%COMP%]{padding:0 32px 16px}@media screen and (max-width: 912px){.p-panel[_ngcontent-%COMP%]   .p-panel-content[_ngcontent-%COMP%]{padding:0 16px 16px}}.p-panel[_ngcontent-%COMP%]   .p-panel-header[_ngcontent-%COMP%]   .p-panel-header-icon[_ngcontent-%COMP%]{width:24px;height:24px}.p-dialog-mask[_ngcontent-%COMP%]{width:100%;height:100%;background-color:#0006;top:0;z-index:20;pointer-events:all!important}.p-dialog[_ngcontent-%COMP%]   .p-dialog-header[_ngcontent-%COMP%]{padding:32px 24px 16px;color:#231815;font-size:24px;text-align:center;display:flex;justify-content:center}.p-dialog[_ngcontent-%COMP%]   .p-dialog-content[_ngcontent-%COMP%]{padding:32px 24px 0;background:linear-gradient(180deg,#f3f1ea,#fff)}.p-dialog[_ngcontent-%COMP%]   .p-dialog-header[_ngcontent-%COMP%]   .p-dialog-header-icon[_ngcontent-%COMP%]{display:none}.p-dialog[_ngcontent-%COMP%]   .p-dialog-footer[_ngcontent-%COMP%]{padding:24px 24px 40px;display:flex;justify-content:center}.p-checkbox[_ngcontent-%COMP%]{width:18px;height:18px;display:inline-flex;align-items:center}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box[_ngcontent-%COMP%]{width:18px;height:18px;border:2px solid #CDCDCD;border-radius:3px;transition:.3s ease;background:#fff;position:relative;display:flex;align-items:center;justify-content:center}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box[_ngcontent-%COMP%]:hover{border-color:#b8a676}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box[_ngcontent-%COMP%]:focus{outline:none;border-color:#b8a676;box-shadow:0 0 0 2px #b8a67633}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box.p-highlight[_ngcontent-%COMP%]{background:#b8a676!important;border-color:#b8a676!important}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box[_ngcontent-%COMP%]   .p-checkbox-icon[_ngcontent-%COMP%]{color:#fff!important;font-size:12px;width:12px;height:12px;display:flex;align-items:center;justify-content:center}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box.p-highlight[_ngcontent-%COMP%]   .p-checkbox-icon[_ngcontent-%COMP%]{transform:scale(1);opacity:1}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box[_ngcontent-%COMP%]:not(.p-highlight)   .p-checkbox-icon[_ngcontent-%COMP%]{transform:scale(0);opacity:0}.p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box.p-highlight[_ngcontent-%COMP%]   .p-checkbox-icon[_ngcontent-%COMP%]:before{content:\\\"\\\\e915\\\";font-family:primeicons;font-weight:400;font-style:normal;font-size:12px;display:inline-block}.p-checkbox-label[_ngcontent-%COMP%]{margin-left:8px;cursor:pointer;-webkit-user-select:none;user-select:none}.input[_ngcontent-%COMP%]{width:100%;border:none;padding:12.5px 0;border-bottom:1px solid rgba(144,150,157,.4);border-radius:0}[_ngcontent-%COMP%]::-webkit-scrollbar{width:4px;height:4px}[_ngcontent-%COMP%]::-webkit-scrollbar-button{background:transparent;border-radius:2px}[_ngcontent-%COMP%]::-webkit-scrollbar-track-piece{background:transparent}[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{border-radius:4px;background-color:#90969d33}[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:transparent}.p-radiobutton-label[_ngcontent-%COMP%]{color:#231815!important;font-weight:400;cursor:pointer;transition:all .3s ease}.p-radiobutton-label[_ngcontent-%COMP%]:hover{color:#ae9b66!important}label[for*=requirement_][_ngcontent-%COMP%]{color:#231815!important;font-weight:400}label[for*=requirement_][_ngcontent-%COMP%]:hover{color:#ae9b66!important}input[type=radio][_ngcontent-%COMP%]:checked + label[_ngcontent-%COMP%], .p-radiobutton[_ngcontent-%COMP%]   .p-radiobutton-box.p-highlight[_ngcontent-%COMP%] + label[_ngcontent-%COMP%]{color:#ae9b66!important;font-weight:600}[_nghost-%COMP%]   .bigbox[_ngcontent-%COMP%]{overflow:auto;width:100%;height:500px;text-align:center}[_nghost-%COMP%]   .bigbox[_ngcontent-%COMP%]   .bigimg[_ngcontent-%COMP%]{width:auto;max-width:none}.planreview-box[_ngcontent-%COMP%]{display:flex;flex-direction:row}@media screen and (max-width: 912px){.planreview-box[_ngcontent-%COMP%]{flex-direction:column}}button[_ngcontent-%COMP%]{margin:48px 0}.dialog-content[_ngcontent-%COMP%]{display:flex;justify-content:center}.choicebutn[_ngcontent-%COMP%]{padding:4px 16px;width:81px;height:auto;margin-top:8px;cursor:pointer}.button2[_ngcontent-%COMP%]{margin:16px 0!important}.step-title[_ngcontent-%COMP%]{font-size:24px;padding-bottom:24px;font-weight:700;display:block}.sec-e-check[_ngcontent-%COMP%]{padding:24px 0}.loading-container[_ngcontent-%COMP%]{width:100%;min-height:40vh;display:flex;justify-content:center;align-items:center;background:linear-gradient(135deg,#f8fafc,#e2e8f0);border:1px solid #e2e8f0;border-radius:12px;margin:16px 0}.loading-content[_ngcontent-%COMP%]{text-align:center;padding:2rem}.loading-spinner[_ngcontent-%COMP%]{width:40px;height:40px;border:3px solid #e2e8f0;border-top:3px solid #3b82f6;border-radius:50%;margin:0 auto 1.5rem;animation:_ngcontent-%COMP%_spin 1s linear infinite}@keyframes _ngcontent-%COMP%_spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.loading-title[_ngcontent-%COMP%]{font-size:1.125rem;font-weight:600;color:#374151;margin:0 0 .5rem}.loading-description[_ngcontent-%COMP%]{font-size:.95rem;color:#6b7280;margin:0}.no-pdf-container[_ngcontent-%COMP%]{width:100%;min-height:40vh;display:flex;justify-content:center;align-items:center;background:linear-gradient(135deg,#f8fafc,#e2e8f0);border:2px dashed #cbd5e1;border-radius:12px;margin:16px 0;transition:all .3s ease}.no-pdf-container[_ngcontent-%COMP%]:hover{border-color:#94a3b8;background:linear-gradient(135deg,#f1f5f9,#e2e8f0)}.no-pdf-content[_ngcontent-%COMP%]{text-align:center;padding:2rem;max-width:400px}.pdf-icon[_ngcontent-%COMP%]{margin:0 auto 1.5rem;width:48px;height:48px;display:flex;align-items:center;justify-content:center;background:#f1f5f9;border-radius:50%;padding:12px}.pdf-icon[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%]{opacity:.7}.no-pdf-title[_ngcontent-%COMP%]{font-size:1.25rem;font-weight:600;color:#374151;margin:0 0 .75rem}.no-pdf-description[_ngcontent-%COMP%]{font-size:.95rem;color:#6b7280;margin:0 0 1.5rem;line-height:1.5}.refresh-hint[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;gap:.75rem}.refresh-text[_ngcontent-%COMP%]{font-size:.875rem;color:#9ca3af}.refresh-btn[_ngcontent-%COMP%]{display:inline-flex;align-items:center;gap:.5rem;padding:.5rem 1rem;background:#3b82f6;color:#fff;border:none;border-radius:6px;font-size:.875rem;font-weight:500;cursor:pointer;transition:all .2s ease}.refresh-btn[_ngcontent-%COMP%]:hover{background:#2563eb;transform:translateY(-1px)}.refresh-btn[_ngcontent-%COMP%]:active{transform:translateY(0)}.refresh-btn[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%]{width:16px;height:16px}.warning-box[_ngcontent-%COMP%]{background:#fef3c7;border:1px solid #f59e0b;border-radius:8px;padding:1rem;display:inline-block;max-width:300px}.warning-icon[_ngcontent-%COMP%]{margin:0 auto .5rem;width:20px;height:20px;display:flex;align-items:center;justify-content:center}.warning-text[_ngcontent-%COMP%]{font-size:.875rem;color:#92400e;margin:0;line-height:1.4}@media screen and (max-width: 912px){.sec-e-check[_ngcontent-%COMP%]{padding:24px 16px}.button2[_ngcontent-%COMP%]{margin:12px 0!important}.warning-box[_ngcontent-%COMP%]{max-width:280px;padding:.875rem}.warning-text[_ngcontent-%COMP%]{font-size:.8rem}.loading-container[_ngcontent-%COMP%]{min-height:35vh;margin:12px 0}.loading-content[_ngcontent-%COMP%]{padding:1.5rem 1rem}.loading-spinner[_ngcontent-%COMP%]{width:32px;height:32px;margin-bottom:1rem}.loading-title[_ngcontent-%COMP%]{font-size:1rem}.loading-description[_ngcontent-%COMP%]{font-size:.875rem}.no-pdf-container[_ngcontent-%COMP%]{min-height:35vh;margin:12px 0}.no-pdf-content[_ngcontent-%COMP%]{padding:1.5rem 1rem}.pdf-icon[_ngcontent-%COMP%]{width:40px;height:40px;padding:10px;margin-bottom:1rem}.no-pdf-title[_ngcontent-%COMP%]{font-size:1.125rem}.no-pdf-description[_ngcontent-%COMP%]{font-size:.875rem}.refresh-btn[_ngcontent-%COMP%]{padding:.625rem 1.25rem;font-size:.875rem}}.hover\\\\:bg-opacity-75[_ngcontent-%COMP%]:hover{--tw-bg-opacity: .75}.hover\\\\:shadow-lg[_ngcontent-%COMP%]:hover{--tw-shadow: 0 10px 15px -3px rgb(0 0 0 / .1), 0 4px 6px -4px rgb(0 0 0 / .1);--tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}@media not all and (min-width: 1280px){.max-xl\\\\:grid-cols-6[_ngcontent-%COMP%]{grid-template-columns:repeat(6,minmax(0,1fr))}}@media not all and (min-width: 1024px){.max-lg\\\\:bottom-12[_ngcontent-%COMP%]{bottom:3rem}.max-lg\\\\:w-full[_ngcontent-%COMP%]{width:100%}.max-lg\\\\:grid-cols-4[_ngcontent-%COMP%]{grid-template-columns:repeat(4,minmax(0,1fr))}}@media not all and (min-width: 768px){.max-md\\\\:bottom-14[_ngcontent-%COMP%]{bottom:3.5rem}.max-md\\\\:my-4[_ngcontent-%COMP%]{margin-top:1rem;margin-bottom:1rem}.max-md\\\\:ml-0[_ngcontent-%COMP%]{margin-left:0}.max-md\\\\:mt-10[_ngcontent-%COMP%]{margin-top:2.5rem}.max-md\\\\:mt-5[_ngcontent-%COMP%]{margin-top:1.25rem}.max-md\\\\:hidden[_ngcontent-%COMP%]{display:none}.max-md\\\\:h-\\\\__ph-0__[_ngcontent-%COMP%]{height:65%}.max-md\\\\:\\\\!w-\\\\__ph-0__[_ngcontent-%COMP%]{width:450px!important}.max-md\\\\:w-\\\\__ph-0__[_ngcontent-%COMP%]{width:500px}.max-md\\\\:w-full[_ngcontent-%COMP%]{width:100%}.max-md\\\\:max-w-full[_ngcontent-%COMP%]{max-width:100%}.max-md\\\\:flex-col[_ngcontent-%COMP%]{flex-direction:column}.max-md\\\\:flex-col-reverse[_ngcontent-%COMP%]{flex-direction:column-reverse}.max-md\\\\:flex-wrap[_ngcontent-%COMP%]{flex-wrap:wrap}.max-md\\\\:items-start[_ngcontent-%COMP%]{align-items:flex-start}}@media not all and (min-width: 640px){.max-sm\\\\:bottom-10[_ngcontent-%COMP%]{bottom:2.5rem}.max-sm\\\\:ml-8[_ngcontent-%COMP%]{margin-left:2rem}.max-sm\\\\:block[_ngcontent-%COMP%]{display:block}.max-sm\\\\:\\\\!w-\\\\__ph-0__[_ngcontent-%COMP%]{width:340px!important}.max-sm\\\\:w-\\\\__ph-0__[_ngcontent-%COMP%]{width:380px}.max-sm\\\\:\\\\!grid-cols-2[_ngcontent-%COMP%]{grid-template-columns:repeat(2,minmax(0,1fr))!important}.max-sm\\\\:\\\\!justify-between[_ngcontent-%COMP%]{justify-content:space-between!important}.max-sm\\\\:gap-0[_ngcontent-%COMP%]{gap:0px}.max-sm\\\\:px-0[_ngcontent-%COMP%]{padding-left:0;padding-right:0}.max-sm\\\\:pl-0[_ngcontent-%COMP%]{padding-left:0}.max-sm\\\\:text-base[_ngcontent-%COMP%]{font-size:1rem;line-height:1.5rem}}@media (min-width: 640px){.sm\\\\:mx-0[_ngcontent-%COMP%]{margin-left:0;margin-right:0}.sm\\\\:mb-0[_ngcontent-%COMP%]{margin-bottom:0}.sm\\\\:mt-1[_ngcontent-%COMP%]{margin-top:.25rem}.sm\\\\:flex[_ngcontent-%COMP%]{display:flex}.sm\\\\:grid-cols-2[_ngcontent-%COMP%]{grid-template-columns:repeat(2,minmax(0,1fr))}.sm\\\\:items-center[_ngcontent-%COMP%]{align-items:center}.sm\\\\:pl-3[_ngcontent-%COMP%]{padding-left:.75rem}}@media (min-width: 768px){.md\\\\:flex[_ngcontent-%COMP%]{display:flex}.md\\\\:grid-cols-3[_ngcontent-%COMP%]{grid-template-columns:repeat(3,minmax(0,1fr))}.md\\\\:pb-20[_ngcontent-%COMP%]{padding-bottom:5rem}}@media (min-width: 1024px){.lg\\\\:\\\\!mx-0[_ngcontent-%COMP%]{margin-left:0!important;margin-right:0!important}.lg\\\\:\\\\!mt-0[_ngcontent-%COMP%]{margin-top:0!important}.lg\\\\:flex[_ngcontent-%COMP%]{display:flex}.lg\\\\:\\\\!w-\\\\__ph-0__[_ngcontent-%COMP%]{width:180px!important}.lg\\\\:\\\\!w-\\\\__ph-0__[_ngcontent-%COMP%]{width:800px!important}.lg\\\\:\\\\!w-\\\\__ph-0__[_ngcontent-%COMP%]{width:900px!important}.lg\\\\:\\\\!flex-row[_ngcontent-%COMP%]{flex-direction:row!important}.lg\\\\:\\\\!px-4[_ngcontent-%COMP%]{padding-left:1rem!important;padding-right:1rem!important}.lg\\\\:\\\\!px-\\\\__ph-0__[_ngcontent-%COMP%]{padding-left:22px!important;padding-right:22px!important}.lg\\\\:\\\\!py-\\\\__ph-0__[_ngcontent-%COMP%]{padding-top:18px!important;padding-bottom:18px!important}}\"]\n  });\n}", "map": {"version": 3, "names": ["EventEmitter", "CheckboxModule", "FormsModule", "CommonModule", "ButtonModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "ctx_r0", "safePdfUrl", "ɵɵsanitizeResourceUrl", "ɵɵadvance", "ɵɵsanitizeUrl", "ɵɵelement", "ɵɵlistener", "StepIvChoiceComponent_Conditional_6_Template_button_click_14_listener", "ɵɵrestoreView", "_r2", "ɵɵnextContext", "ɵɵresetView", "refreshPdf", "ɵɵtwoWayListener", "StepIvChoiceComponent_Conditional_7_Template_p_checkbox_ngModelChange_3_listener", "$event", "_r3", "ɵɵtwoWayBindingSet", "agreeToGoNextStep4", "changeCheckbox", "StepIvChoiceComponent_Conditional_7_Template_button_click_4_listener", "next", "ɵɵtwoWayProperty", "StepIvChoiceComponent_Conditional_8_Template_button_click_9_listener", "_r4", "nextWithoutPdf", "StepIvChoiceComponent", "constructor", "_houseService", "_fileService", "sanitizer", "agreeToGoNextStep4Change", "nextEvent", "detailPictureUrl", "pdfDataUrl", "isLoadingPdf", "ngOnInit", "getImage", "apiHouseGetSpecialNoticeFilePost$Json", "subscribe", "res", "Entries", "CFileName", "CFileUrl", "fileUrl", "fileName", "getFile", "response", "body", "detailPicture", "URL", "createObjectURL", "bypassSecurityTrustResourceUrl", "reader", "FileReader", "onload", "result", "console", "log", "readAsDataURL", "blob", "error", "emit", "event", "ngOnDestroy", "revokeObjectURL", "_", "ɵɵdirectiveInject", "i1", "HouseService", "i2", "FileService", "i3", "Dom<PERSON><PERSON><PERSON>zer", "_2", "selectors", "inputs", "outputs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "StepIvChoiceComponent_Template", "rf", "ctx", "ɵɵtemplate", "StepIvChoiceComponent_Conditional_4_Template", "StepIvChoiceComponent_Conditional_5_Template", "StepIvChoiceComponent_Conditional_6_Template", "StepIvChoiceComponent_Conditional_7_Template", "StepIvChoiceComponent_Conditional_8_Template", "ɵɵconditional", "i4", "Checkbox", "i5", "ButtonDirective", "i6", "NgControlStatus", "NgModel", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\SaleChangeFront\\src\\app\\pages\\choice\\components\\step-iv-choice\\step-iv-choice.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\SaleChangeFront\\src\\app\\pages\\choice\\components\\step-iv-choice\\step-iv-choice.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, OnInit, Output, OnDestroy } from '@angular/core';\r\nimport { HttpResponse } from '@angular/common/http';\r\nimport { CheckboxModule } from 'primeng/checkbox';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { CommonModule } from '@angular/common';\r\nimport { ButtonModule } from 'primeng/button';\r\nimport { HouseService } from '../../../../../services/api/services';\r\nimport { FileService } from '../../../../../services/File.service';\r\nimport { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';\r\n\r\n@Component({\r\n  selector: 'app-step-iv-choice',\r\n  standalone: true, imports: [\r\n    CheckboxModule,\r\n    ButtonModule,\r\n    FormsModule,\r\n    CommonModule\r\n  ],\r\n  templateUrl: './step-iv-choice.component.html',\r\n  styleUrl: './step-iv-choice.component.scss'\r\n})\r\n\r\nexport class StepIvChoiceComponent implements OnInit, OnD<PERSON>roy {\r\n  @Input() agreeToGoNextStep4: boolean = false;\r\n  @Output() agreeToGoNextStep4Change = new EventEmitter();\r\n  @Output() nextEvent = new EventEmitter()\r\n  detailPicture: Blob | string | any\r\n  detailPictureUrl: string | null = null;\r\n  safePdfUrl: SafeResourceUrl | null = null;\r\n  pdfDataUrl: string | null = null; // 添加 base64 data URL\r\n  isLoadingPdf: boolean = false; // 加入載入狀態\r\n  constructor(\r\n    private _houseService: HouseService,\r\n    private _fileService: FileService,\r\n    private sanitizer: DomSanitizer\r\n  ) {\r\n\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.getImage()\r\n  }\r\n  getImage() {\r\n    this.isLoadingPdf = true; // 開始載入\r\n    this._houseService.apiHouseGetSpecialNoticeFilePost$Json({})\r\n      .subscribe(res => {\r\n        if (res.Entries && res.Entries.CFileName && res.Entries.CFileUrl) {          // 從 CFileUrl 中解析出相對路徑和檔名\r\n          const fileUrl = res.Entries.CFileUrl;\r\n          const fileName = res.Entries.CFileName;          // 使用 FileService 取得 PDF blob\r\n          this._fileService.getFile(fileUrl, fileName).subscribe(\r\n            (response: HttpResponse<Blob>) => {\r\n              if (response.body) {\r\n                this.detailPicture = response.body;\r\n\r\n                // 方法1: 創建 blob URL\r\n                this.detailPictureUrl = URL.createObjectURL(response.body);\r\n              }\r\n              this.safePdfUrl = this.sanitizer.bypassSecurityTrustResourceUrl(this.detailPictureUrl);\r\n\r\n              // 方法2: 轉換為 base64 data URL (更好的兼容性)\r\n              const reader = new FileReader();\r\n              reader.onload = () => {\r\n                this.pdfDataUrl = reader.result as string;\r\n                // 如果 blob URL 不工作，使用 data URL\r\n                if (this.pdfDataUrl) {\r\n                  this.safePdfUrl = this.sanitizer.bypassSecurityTrustResourceUrl(this.pdfDataUrl);\r\n                }\r\n                this.isLoadingPdf = false; // 載入完成\r\n                console.log('PDF Data URL 已準備就緒');\r\n              };\r\n              reader.readAsDataURL(blob);\r\n\r\n              console.log('PDF URL 已準備就緒:', this.detailPictureUrl);\r\n            },\r\n            (error) => {\r\n              console.error('取得 PDF 文件時發生錯誤:', error);\r\n              this.isLoadingPdf = false; // 載入錯誤時也要停止載入狀態\r\n            }\r\n          );\r\n        } else {\r\n          this.isLoadingPdf = false; // 沒有檔案時停止載入狀態\r\n        }\r\n      }, (error) => {\r\n        console.error('取得檔案資訊時發生錯誤:', error);\r\n        this.isLoadingPdf = false; // 載入錯誤時也要停止載入狀態\r\n      });\r\n  }\r\n\r\n  next() {\r\n    this.nextEvent.emit();\r\n  }\r\n  changeCheckbox(event: any) {\r\n    this.agreeToGoNextStep4Change.emit(event)\r\n  }\r\n\r\n  refreshPdf() {\r\n    // 重新載入PDF文件\r\n    this.ngOnInit();\r\n  }\r\n\r\n  nextWithoutPdf() {\r\n    // 無PDF檔案時的下一步，可以加入額外的確認邏輯\r\n    this.nextEvent.emit();\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    // 清理 blob URL 以避免記憶體洩漏\r\n    if (this.detailPictureUrl) {\r\n      URL.revokeObjectURL(this.detailPictureUrl);\r\n    }\r\n  }\r\n}\r\n", "<section>\r\n  <div class=\"flex flex-col justify-center items-center w-full\"> <span class=\"text-black step-title\">\r\n      客變原則\r\n    </span> @if(!!safePdfUrl) {\r\n    <!-- 備用方案：使用 object 標籤 -->\r\n    <object class=\"mt-3 w-full\" [data]=\"safePdfUrl\" type=\"application/pdf\" width=\"100%\" height=\"500\"\r\n      style=\"border: 1px solid #ccc;\">\r\n      <!-- 如果 object 無法顯示，顯示備用內容 -->\r\n      <iframe [src]=\"safePdfUrl\" width=\"100%\" height=\"500\" style=\"border: 1px solid #ccc;\" title=\"客變說明PDF\">\r\n        <!-- 如果 iframe 也無法顯示，提供下載連結 -->\r\n        <p>您的瀏覽器無法顯示 PDF。請\r\n          <a [href]=\"safePdfUrl\" download=\"客變說明.pdf\" target=\"_blank\">\r\n            點擊此處下載 PDF 文件\r\n          </a>\r\n        </p>\r\n      </iframe>\r\n    </object>\r\n    } @else if(isLoadingPdf) {\r\n    <div class=\"loading-container\">\r\n      <div class=\"loading-content\">\r\n        <div class=\"loading-spinner\"></div>\r\n        <h3 class=\"loading-title\">載入中...</h3>\r\n        <p class=\"loading-description\">正在準備客變說明文件</p>\r\n      </div>\r\n    </div>\r\n    } @else {\r\n    <div class=\"no-pdf-container\">\r\n      <div class=\"no-pdf-content\">\r\n        <div class=\"pdf-icon\">\r\n          <svg width=\"48\" height=\"48\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n            <path d=\"M14 2H6C4.9 2 4 2.9 4 4v16c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V8l-6-6z\" stroke=\"#6B7280\"\r\n              stroke-width=\"2\" fill=\"none\" />\r\n            <path d=\"M14 2v6h6\" stroke=\"#6B7280\" stroke-width=\"2\" fill=\"none\" />\r\n            <path d=\"M16 13H8M16 17H8M10 9H8\" stroke=\"#6B7280\" stroke-width=\"2\" />\r\n          </svg>\r\n        </div>\r\n        <h3 class=\"no-pdf-title\">客變說明文件</h3>\r\n        <p class=\"no-pdf-description\">文件準備中，請稍後重新整理頁面</p>\r\n        <div class=\"refresh-hint\">\r\n          <span class=\"refresh-text\">或點擊下方按鈕重新載入</span>\r\n          <button type=\"button\" class=\"refresh-btn\" (click)=\"refreshPdf()\">\r\n            <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n              <path d=\"M3 12a9 9 0 019-9 9.75 9.75 0 016.74 2.74L21 8\" stroke=\"currentColor\" stroke-width=\"2\"\r\n                stroke-linecap=\"round\" stroke-linejoin=\"round\" />\r\n              <path d=\"M21 3v5h-5M21 12a9 9 0 01-9 9 9.75 9.75 0 01-6.74-2.74L3 16\" stroke=\"currentColor\"\r\n                stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" />\r\n              <path d=\"M3 21v-5h5\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\"\r\n                stroke-linejoin=\"round\" />\r\n            </svg>\r\n            重新載入\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    }\r\n\r\n    @if(!!safePdfUrl) {\r\n    <div class=\"w-[100%] flex items-start justify-start sec-e-check\">\r\n      <div class=\"text-stone-600 checkbox-zone w-full\">\r\n        <div class=\"bg-white\" style=\"padding: 12px 16px;\">\r\n          <p-checkbox [binary]=\"true\" label=\"我已閱讀並同意以上說明內容。\" [(ngModel)]=\"agreeToGoNextStep4\"\r\n            (ngModelChange)=\"changeCheckbox($event)\"></p-checkbox>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <button class=\"button2 !w-48 butn1 !text-center flex justify-center items-center\" pButton\r\n      [disabled]=\"!agreeToGoNextStep4\" (click)=\"next()\">\r\n      下一步\r\n    </button>\r\n    } @else {\r\n    <div class=\"w-[100%] flex flex-col items-center justify-center sec-e-check\">\r\n      <div class=\"warning-box mb-4\">\r\n        <div class=\"warning-icon\">\r\n          <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n            <path d=\"M12 9v4M12 17h.01M7.86 2h8.28L22 7.86v8.28L16.14 22H7.86L2 16.14V7.86L7.86 2z\" stroke=\"#f59e0b\"\r\n              stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" />\r\n          </svg>\r\n        </div>\r\n        <p class=\"warning-text\">\r\n          目前無法顯示客變說明文件<br>\r\n          如需繼續，請確認您已了解相關客變規則\r\n        </p>\r\n      </div>\r\n      <button class=\"button2 !w-48 butn1 !text-center flex justify-center items-center\" pButton\r\n        (click)=\"nextWithoutPdf()\">\r\n        確認繼續\r\n      </button>\r\n    </div>\r\n    }\r\n  </div>\r\n</section>"], "mappings": "AAAA,SAAoBA,YAAY,QAA0C,eAAe;AAEzF,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,gBAAgB;;;;;;;;;;ICKrCC,EALJ,CAAAC,cAAA,gBACkC,gBAEqE,QAEhG;IAAAD,EAAA,CAAAE,MAAA,8EACD;IAAAF,EAAA,CAAAC,cAAA,WAA2D;IACzDD,EAAA,CAAAE,MAAA,8DACF;IAGNF,EAHM,CAAAG,YAAA,EAAI,EACF,EACG,EACF;;;;IAXmBH,EAAA,CAAAI,UAAA,SAAAC,MAAA,CAAAC,UAAA,EAAAN,EAAA,CAAAO,qBAAA,CAAmB;IAGrCP,EAAA,CAAAQ,SAAA,EAAkB;IAAlBR,EAAA,CAAAI,UAAA,QAAAC,MAAA,CAAAC,UAAA,EAAAN,EAAA,CAAAO,qBAAA,CAAkB;IAGnBP,EAAA,CAAAQ,SAAA,GAAmB;IAAnBR,EAAA,CAAAI,UAAA,SAAAC,MAAA,CAAAC,UAAA,EAAAN,EAAA,CAAAS,aAAA,CAAmB;;;;;IAQ1BT,EADF,CAAAC,cAAA,aAA+B,aACA;IAC3BD,EAAA,CAAAU,SAAA,aAAmC;IACnCV,EAAA,CAAAC,cAAA,YAA0B;IAAAD,EAAA,CAAAE,MAAA,4BAAM;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACrCH,EAAA,CAAAC,cAAA,WAA+B;IAAAD,EAAA,CAAAE,MAAA,mEAAU;IAE7CF,EAF6C,CAAAG,YAAA,EAAI,EACzC,EACF;;;;;;IAIFH,EAFJ,CAAAC,cAAA,cAA8B,cACA,cACJ;;IACpBD,EAAA,CAAAC,cAAA,cAA+F;IAI7FD,EAHA,CAAAU,SAAA,eACiC,eACmC,eACE;IAE1EV,EADE,CAAAG,YAAA,EAAM,EACF;;IACNH,EAAA,CAAAC,cAAA,aAAyB;IAAAD,EAAA,CAAAE,MAAA,2CAAM;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACpCH,EAAA,CAAAC,cAAA,YAA8B;IAAAD,EAAA,CAAAE,MAAA,kGAAe;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAE/CH,EADF,CAAAC,cAAA,eAA0B,gBACG;IAAAD,EAAA,CAAAE,MAAA,0EAAW;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC7CH,EAAA,CAAAC,cAAA,kBAAiE;IAAvBD,EAAA,CAAAW,UAAA,mBAAAC,sEAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAC,GAAA;MAAA,MAAAT,MAAA,GAAAL,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASX,MAAA,CAAAY,UAAA,EAAY;IAAA,EAAC;;IAC9DjB,EAAA,CAAAC,cAAA,eAA+F;IAK7FD,EAJA,CAAAU,SAAA,gBACmD,gBAEiB,gBAExC;IAC9BV,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAE,MAAA,kCACF;IAGNF,EAHM,CAAAG,YAAA,EAAS,EACL,EACF,EACF;;;;;;IAOAH,EAHN,CAAAC,cAAA,cAAiE,cACd,cACG,qBAEL;IADQD,EAAA,CAAAkB,gBAAA,2BAAAC,iFAAAC,MAAA;MAAApB,EAAA,CAAAa,aAAA,CAAAQ,GAAA;MAAA,MAAAhB,MAAA,GAAAL,EAAA,CAAAe,aAAA;MAAAf,EAAA,CAAAsB,kBAAA,CAAAjB,MAAA,CAAAkB,kBAAA,EAAAH,MAAA,MAAAf,MAAA,CAAAkB,kBAAA,GAAAH,MAAA;MAAA,OAAApB,EAAA,CAAAgB,WAAA,CAAAI,MAAA;IAAA,EAAgC;IACjFpB,EAAA,CAAAW,UAAA,2BAAAQ,iFAAAC,MAAA;MAAApB,EAAA,CAAAa,aAAA,CAAAQ,GAAA;MAAA,MAAAhB,MAAA,GAAAL,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAAiBX,MAAA,CAAAmB,cAAA,CAAAJ,MAAA,CAAsB;IAAA,EAAC;IAGhDpB,EAHiD,CAAAG,YAAA,EAAa,EACpD,EACF,EACF;IAENH,EAAA,CAAAC,cAAA,iBACoD;IAAjBD,EAAA,CAAAW,UAAA,mBAAAc,qEAAA;MAAAzB,EAAA,CAAAa,aAAA,CAAAQ,GAAA;MAAA,MAAAhB,MAAA,GAAAL,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASX,MAAA,CAAAqB,IAAA,EAAM;IAAA,EAAC;IACjD1B,EAAA,CAAAE,MAAA,2BACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IATSH,EAAA,CAAAQ,SAAA,GAAe;IAAfR,EAAA,CAAAI,UAAA,gBAAe;IAAwBJ,EAAA,CAAA2B,gBAAA,YAAAtB,MAAA,CAAAkB,kBAAA,CAAgC;IAOvFvB,EAAA,CAAAQ,SAAA,EAAgC;IAAhCR,EAAA,CAAAI,UAAA,cAAAC,MAAA,CAAAkB,kBAAA,CAAgC;;;;;;IAM9BvB,EAFJ,CAAAC,cAAA,cAA4E,cAC5C,cACF;;IACxBD,EAAA,CAAAC,cAAA,cAA+F;IAC7FD,EAAA,CAAAU,SAAA,eACoE;IAExEV,EADE,CAAAG,YAAA,EAAM,EACF;;IACNH,EAAA,CAAAC,cAAA,YAAwB;IACtBD,EAAA,CAAAE,MAAA,gFAAY;IAAAF,EAAA,CAAAU,SAAA,SAAI;IAChBV,EAAA,CAAAE,MAAA,qHACF;IACFF,EADE,CAAAG,YAAA,EAAI,EACA;IACNH,EAAA,CAAAC,cAAA,iBAC6B;IAA3BD,EAAA,CAAAW,UAAA,mBAAAiB,qEAAA;MAAA5B,EAAA,CAAAa,aAAA,CAAAgB,GAAA;MAAA,MAAAxB,MAAA,GAAAL,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASX,MAAA,CAAAyB,cAAA,EAAgB;IAAA,EAAC;IAC1B9B,EAAA,CAAAE,MAAA,kCACF;IACFF,EADE,CAAAG,YAAA,EAAS,EACL;;;ADlEV,OAAM,MAAO4B,qBAAqB;EAShCC,YACUC,aAA2B,EAC3BC,YAAyB,EACzBC,SAAuB;IAFvB,KAAAF,aAAa,GAAbA,aAAa;IACb,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,SAAS,GAATA,SAAS;IAXV,KAAAZ,kBAAkB,GAAY,KAAK;IAClC,KAAAa,wBAAwB,GAAG,IAAIzC,YAAY,EAAE;IAC7C,KAAA0C,SAAS,GAAG,IAAI1C,YAAY,EAAE;IAExC,KAAA2C,gBAAgB,GAAkB,IAAI;IACtC,KAAAhC,UAAU,GAA2B,IAAI;IACzC,KAAAiC,UAAU,GAAkB,IAAI,CAAC,CAAC;IAClC,KAAAC,YAAY,GAAY,KAAK,CAAC,CAAC;EAO/B;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,QAAQ,EAAE;EACjB;EACAA,QAAQA,CAAA;IACN,IAAI,CAACF,YAAY,GAAG,IAAI,CAAC,CAAC;IAC1B,IAAI,CAACP,aAAa,CAACU,qCAAqC,CAAC,EAAE,CAAC,CACzDC,SAAS,CAACC,GAAG,IAAG;MACf,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACC,OAAO,CAACC,SAAS,IAAIF,GAAG,CAACC,OAAO,CAACE,QAAQ,EAAE;QAAW;QAC3E,MAAMC,OAAO,GAAGJ,GAAG,CAACC,OAAO,CAACE,QAAQ;QACpC,MAAME,QAAQ,GAAGL,GAAG,CAACC,OAAO,CAACC,SAAS,CAAC,CAAU;QACjD,IAAI,CAACb,YAAY,CAACiB,OAAO,CAACF,OAAO,EAAEC,QAAQ,CAAC,CAACN,SAAS,CACnDQ,QAA4B,IAAI;UAC/B,IAAIA,QAAQ,CAACC,IAAI,EAAE;YACjB,IAAI,CAACC,aAAa,GAAGF,QAAQ,CAACC,IAAI;YAElC;YACA,IAAI,CAACf,gBAAgB,GAAGiB,GAAG,CAACC,eAAe,CAACJ,QAAQ,CAACC,IAAI,CAAC;;UAE5D,IAAI,CAAC/C,UAAU,GAAG,IAAI,CAAC6B,SAAS,CAACsB,8BAA8B,CAAC,IAAI,CAACnB,gBAAgB,CAAC;UAEtF;UACA,MAAMoB,MAAM,GAAG,IAAIC,UAAU,EAAE;UAC/BD,MAAM,CAACE,MAAM,GAAG,MAAK;YACnB,IAAI,CAACrB,UAAU,GAAGmB,MAAM,CAACG,MAAgB;YACzC;YACA,IAAI,IAAI,CAACtB,UAAU,EAAE;cACnB,IAAI,CAACjC,UAAU,GAAG,IAAI,CAAC6B,SAAS,CAACsB,8BAA8B,CAAC,IAAI,CAAClB,UAAU,CAAC;;YAElF,IAAI,CAACC,YAAY,GAAG,KAAK,CAAC,CAAC;YAC3BsB,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;UACnC,CAAC;UACDL,MAAM,CAACM,aAAa,CAACC,IAAI,CAAC;UAE1BH,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAACzB,gBAAgB,CAAC;QACtD,CAAC,EACA4B,KAAK,IAAI;UACRJ,OAAO,CAACI,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;UACvC,IAAI,CAAC1B,YAAY,GAAG,KAAK,CAAC,CAAC;QAC7B,CAAC,CACF;OACF,MAAM;QACL,IAAI,CAACA,YAAY,GAAG,KAAK,CAAC,CAAC;;IAE/B,CAAC,EAAG0B,KAAK,IAAI;MACXJ,OAAO,CAACI,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACpC,IAAI,CAAC1B,YAAY,GAAG,KAAK,CAAC,CAAC;IAC7B,CAAC,CAAC;EACN;EAEAd,IAAIA,CAAA;IACF,IAAI,CAACW,SAAS,CAAC8B,IAAI,EAAE;EACvB;EACA3C,cAAcA,CAAC4C,KAAU;IACvB,IAAI,CAAChC,wBAAwB,CAAC+B,IAAI,CAACC,KAAK,CAAC;EAC3C;EAEAnD,UAAUA,CAAA;IACR;IACA,IAAI,CAACwB,QAAQ,EAAE;EACjB;EAEAX,cAAcA,CAAA;IACZ;IACA,IAAI,CAACO,SAAS,CAAC8B,IAAI,EAAE;EACvB;EAEAE,WAAWA,CAAA;IACT;IACA,IAAI,IAAI,CAAC/B,gBAAgB,EAAE;MACzBiB,GAAG,CAACe,eAAe,CAAC,IAAI,CAAChC,gBAAgB,CAAC;;EAE9C;EAAC,QAAAiC,CAAA,G;qBAxFUxC,qBAAqB,EAAA/B,EAAA,CAAAwE,iBAAA,CAAAC,EAAA,CAAAC,YAAA,GAAA1E,EAAA,CAAAwE,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAA5E,EAAA,CAAAwE,iBAAA,CAAAK,EAAA,CAAAC,YAAA;EAAA;EAAA,QAAAC,EAAA,G;UAArBhD,qBAAqB;IAAAiD,SAAA;IAAAC,MAAA;MAAA1D,kBAAA;IAAA;IAAA2D,OAAA;MAAA9C,wBAAA;MAAAC,SAAA;IAAA;IAAA8C,UAAA;IAAAC,QAAA,GAAApF,EAAA,CAAAqF,mBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCrB+B3F,EADjE,CAAAC,cAAA,cAAS,aACuD,cAAqC;QAC/FD,EAAA,CAAAE,MAAA,iCACF;QAAAF,EAAA,CAAAG,YAAA,EAAO;QAmELH,EAnEM,CAAA6F,UAAA,IAAAC,4CAAA,oBAAmB,IAAAC,4CAAA,OAcD,IAAAC,4CAAA,QAQjB,IAAAC,4CAAA,OA+BU,IAAAC,4CAAA,QAcV;QAqBblG,EADE,CAAAG,YAAA,EAAM,EACE;;;QAxFEH,EAAA,CAAAQ,SAAA,GAmDP;QAnDOR,EAAA,CAAAmG,aAAA,MAAAP,GAAA,CAAAtF,UAAA,OAAAsF,GAAA,CAAApD,YAAA,SAmDP;QAEDxC,EAAA,CAAAQ,SAAA,GAiCC;QAjCDR,EAAA,CAAAmG,aAAA,MAAAP,GAAA,CAAAtF,UAAA,SAiCC;;;mBD5EDV,cAAc,EAAAwG,EAAA,CAAAC,QAAA,EACdtG,YAAY,EAAAuG,EAAA,CAAAC,eAAA,EACZ1G,WAAW,EAAA2G,EAAA,CAAAC,eAAA,EAAAD,EAAA,CAAAE,OAAA,EACX5G,YAAY;IAAA6G,MAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}