import { CommonModule } from '@angular/common';
import { HttpResponse } from '@angular/common/http';
import { Component } from '@angular/core';
import { RouterModule } from '@angular/router';
import { MenuItem, MessageService } from 'primeng/api';
import { PanelModule } from 'primeng/panel';
import { PanelMenuModule } from 'primeng/panelmenu';
import { ToastModule } from 'primeng/toast';
import { HouseService, SpecialChangeService } from '../../../services/api/services';
import { SpecialChangeFileGroup, SpecialChangeFile } from '../../../services/api/models';
import { DateFormatPipe } from '../../shared/pipes/date-format.pipe';
import { LocalStorageService } from '../../shared/services/local-storage.service';
import { STORAGE_KEY } from '../../shared/constant/constant';
import { UtilityService } from '../../shared/services/utility.service';
import { ToastMessage } from '../../shared/services/message.service';
import { FileService } from '../../../services/File.service';
import { trigger, state, style, transition, animate } from '@angular/animations';
@Component({
  selector: 'app-history',
  standalone: true,
  providers: [
    MessageService,
    ToastMessage,
  ],
  imports: [RouterModule, PanelModule, PanelMenuModule, CommonModule, DateFormatPipe, ToastModule],
  templateUrl: './history.component.html',
  styleUrl: './history.component.scss',
  animations: [
    trigger('slideInOut', [
      state('in', style({
        height: '*',
        opacity: 1,
        overflow: 'visible'
      })),
      state('out', style({
        height: '0px',
        opacity: 0,
        overflow: 'hidden'
      })),
      transition('in => out', animate('300ms ease-in-out')),
      transition('out => in', animate('300ms ease-in-out'))
    ])
  ]
})
export class HistoryComponent {
  items: MenuItem[] = [];
  houseHoldDetaiPicture: { CFile: string | any; CName: string | any } = { CFile: '', CName: '' }
  specialChangeFiles: SpecialChangeFileGroup[] = []
  dataUser = LocalStorageService.GetLocalStorage(STORAGE_KEY.SAVE_LOGIN) as any;
  buildCaseId: any
  holdDetailId: any

  // 檔案上傳相關屬性
  uploadedFiles: File[] = [];
  isUploading: boolean = false;
  uploadError: string = '';
  maxFileSize: number = 10 * 1024 * 1024; // 10MB
  allowedFileTypes: string[] = ['.pdf', '.dwg', '.dxf', '.dwf'];
  allowedMimeTypes: string[] = ['application/pdf', 'image/vnd.dwg', 'application/acad', 'application/x-acad'];
  isDragOver: boolean = false;
  showUploadArea: boolean = false; // 控制上傳區域顯示

  constructor(
    private _houseService: HouseService,
    private _specialChangeService: SpecialChangeService,
    private _utilityService: UtilityService,
    private _toastService: ToastMessage,
    private _fileService: FileService
  ) {
  }

  ngOnInit(): void {
    this.getHouseHoldDetaiPic()
    this.buildCaseId = this.dataUser.buildCaseId
    this.holdDetailId = this.dataUser.holdDetailId
    this.getListSpecialChange()
  }

  listSpecialChange: SpecialChangeFileGroup[] | any
  // 收合狀態管理
  collapsedStates: { [key: string]: boolean } = {};

  getListSpecialChange() {
    this._specialChangeService.apiSpecialChangeGetSpecialChangeFilePost$Json({ body: this.buildCaseId }).subscribe(res => {
      if (res.Entries && res.StatusCode == 0) {
        this.listSpecialChange = res.Entries! ?? []
        // 初始化收合狀態：預設展開前三個日期群組
        this.initializeCollapsedStates();
      }
    })
  }

  // 初始化收合狀態
  private initializeCollapsedStates() {
    if (this.listSpecialChange && Array.isArray(this.listSpecialChange)) {
      this.listSpecialChange.forEach((group, index) => {
        // 前三個群組預設展開（false = 展開），其餘收合（true = 收合）
        this.collapsedStates[group.CChangeDate] = index >= 3;
      });
    }
  }

  getHouseHoldDetaiPic() {
    this._houseService.apiHouseGetHouseRegularPicturePost$Json({}).subscribe(res => {
      if (res.Entries && res.StatusCode == 0) {
        this.houseHoldDetaiPicture = {
          CFile: res.Entries.CFileURL ? res.Entries.CFileURL : '',
          CName: res.Entries.CFileName,
        }
      }
    })
  }

  getFileNameFromUrl(url: string) {
    const parts = url.split('/');
    const fileName = parts.pop();
    return fileName;
  }

  downloadFileWithoutRedirect(files: { CFile: string | any; CName: string | any }) {
    if (files.CFile && files.CName) {
      this._fileService.getFile(files.CFile, files.CName).subscribe({
        next: (response: HttpResponse<Blob>) => {
          if (response.body) {
            // 創建下載鏈接
            const url = window.URL.createObjectURL(response.body);
            const link = document.createElement('a');
            link.href = url;
            link.download = files.CName || 'download';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(url);
          }
        },
        error: (error) => {
          console.error('檔案下載失敗:', error);
          this._toastService.showErrorMSG('檔案下載失敗');
          // 如果 FileService 失敗，回退到原來的方法
          window.open(files.CFile, '_blank');
        }
      });
    }
  }

  // 檔案選擇事件處理
  onFileSelected(event: Event) {
    const input = event.target as HTMLInputElement;
    if (input.files) {
      this.handleFiles(Array.from(input.files));
    }
  }

  // 拖拽事件處理
  onDragOver(event: DragEvent) {
    event.preventDefault();
    event.stopPropagation();
    this.isDragOver = true;
  }

  onDragLeave(event: DragEvent) {
    event.preventDefault();
    event.stopPropagation();
    this.isDragOver = false;
  }

  onDragDrop(event: DragEvent) {
    event.preventDefault();
    event.stopPropagation();
    this.isDragOver = false;

    const files = event.dataTransfer?.files;
    if (files) {
      this.handleFiles(Array.from(files));
    }
  }

  // 處理檔案
  private handleFiles(files: File[]) {
    this.uploadError = '';

    for (const file of files) {
      if (!this.validateFile(file)) {
        continue;
      }

      // 檢查是否已存在相同檔案
      if (!this.uploadedFiles.some(f => f.name === file.name)) {
        this.uploadedFiles.push(file);
      }
    }
  }

  // 檔案驗證
  private validateFile(file: File): boolean {
    // 檢查檔案大小
    if (file.size > this.maxFileSize) {
      this.uploadError = `檔案 "${file.name}" 超過大小限制 (10MB)`;
      return false;
    }

    // 檢查檔案類型
    const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
    if (!this.allowedFileTypes.includes(fileExtension)) {
      this.uploadError = `檔案 "${file.name}" 格式不支援，只支援 PDF、DWG、DXF、DWF 格式`;
      return false;
    }

    return true;
  }

  // 移除檔案
  removeFile(index: number) {
    this.uploadedFiles.splice(index, 1);
    this.uploadError = '';
  }

  // 格式化檔案大小
  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  // 上傳檔案
  uploadFiles() {
    if (this.uploadedFiles.length === 0) {
      this.uploadError = '請選擇要上傳的檔案';
      return;
    }

    this.isUploading = true;
    this.uploadError = '';

    // 將所有檔案轉換為 data URL 格式（包含 MIME 類型）
    const filePromises = this.uploadedFiles.map(file => this.convertFileToBase64(file));

    Promise.all(filePromises).then(dataUrls => {
      // 組成 SpecialChangeFile 陣列
      const specialChangeFiles: SpecialChangeFile[] = dataUrls.map((dataUrl, index) => ({
        CFileBlood: dataUrl, // 完整的 data URL 格式，包含 MIME 類型
        CFileName: this.uploadedFiles[index].name,
        CFileType: this.getFileTypeFromExtension(this.uploadedFiles[index].name)
      }));

      // 調用 API
      this._specialChangeService.apiSpecialChangeUploadSpecialChangePost$Json({ body: specialChangeFiles }).subscribe({
        next: (response) => {
          this.isUploading = false;
          this.uploadedFiles = [];
          this.showUploadArea = false; // 上傳成功後隱藏上傳區域
          this.getListSpecialChange(); // 重新載入檔案列表

          // 顯示成功訊息
          this._toastService.showSucessMSG('檔案上傳成功');
        },
        error: (error) => {
          this.isUploading = false;
          this.uploadError = '上傳失敗，請稍後再試';
          console.error('Upload error:', error);
          this._toastService.showErrorMSG('檔案上傳失敗：' + (error.error?.Message || '未知錯誤'));
        }
      });
    }).catch(error => {
      this.isUploading = false;
      this.uploadError = '檔案處理失敗';
      console.error('File processing error:', error);
      this._toastService.showErrorMSG('檔案處理失敗');
    });
  }

  // 將檔案轉換為 base64 純字串（不含 data URL 前綴）
  private convertFileToBase64(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => {
        const result = reader.result as string;
        // 只取 base64 部分
        const base64 = result.split(',')[1];
        resolve(base64);
      };
      reader.onerror = error => reject(error);
    });
  }

  // 根據檔案副檔名判斷檔案類型
  private getFileTypeFromExtension(fileName: string): number {
    const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));
    switch (extension) {
      case '.pdf':
        return 1; // 假設 1 代表 PDF
      case '.dwg':
        return 2; // 假設 2 代表 DWG
      case '.dxf':
        return 3; // 假設 3 代表 DXF
      case '.dwf':
        return 4; // 假設 4 代表 DWF
      default:
        return 0; // 未知類型
    }
  }

  // 清除所有待上傳檔案
  clearFiles() {
    this.uploadedFiles = [];
    this.uploadError = '';
    this.showUploadArea = false; // 清除檔案時隱藏上傳區域
  }

  // 根據檔案名稱獲取對應圖標
  getFileIcon(fileName: string): string {
    const extension = fileName.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'pdf':
        return 'assets/PDF.svg';
      case 'dwg':
      case 'dxf':
      case 'dwf':
        return 'assets/designFile.svg';
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'bmp':
      case 'webp':
        return 'assets/imageFile.svg';
      default:
        return 'assets/PDF.svg';
    }
  }

  // 根據檔案名稱獲取檔案類型顯示文字
  getFileType(fileName: string): string {
    const extension = fileName.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'pdf':
        return 'PDF檔案';
      case 'dwg':
        return 'AutoCAD圖檔';
      case 'dxf':
        return 'CAD交換檔';
      case 'dwf':
        return 'CAD檢視檔';
      case 'jpg':
      case 'jpeg':
        return 'JPEG圖片';
      case 'png':
        return 'PNG圖片';
      case 'gif':
        return 'GIF圖片';
      case 'bmp':
        return 'BMP圖片';
      case 'webp':
        return 'WebP圖片';
      default:
        return '未知格式';
    }
  }

  // 根據檔案名稱獲取檔案類型簡短文字（用於下載按鈕）
  getFileTypeText(fileName: string): string {
    const extension = fileName.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'pdf':
        return 'PDF';
      case 'dwg':
      case 'dxf':
      case 'dwf':
        return 'CAD';
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'bmp':
      case 'webp':
        return '圖片';
      default:
        return '檔案';
    }
  }

  // 根據檔案名稱獲取下載圖示
  getDownloadIcon(fileName: string): string {
    const extension = fileName.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'pdf':
        return 'assets/PDF.svg';
      case 'dwg':
      case 'dxf':
      case 'dwf':
        return 'assets/designFile.svg';
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'bmp':
      case 'webp':
        return 'assets/imageFile.svg';
      default:
        return 'assets/PDF.svg';
    }
  }

  // 根據檔案名稱獲取下載提示文字
  getDownloadTitle(fileName: string): string {
    const extension = fileName.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'pdf':
        return '下載 PDF 檔案';
      case 'dwg':
        return '下載 AutoCAD 圖檔';
      case 'dxf':
        return '下載 CAD 交換檔';
      case 'dwf':
        return '下載 CAD 檢視檔';
      case 'jpg':
      case 'jpeg':
        return '下載 JPEG 圖片';
      case 'png':
        return '下載 PNG 圖片';
      case 'gif':
        return '下載 GIF 圖片';
      case 'bmp':
        return '下載 BMP 圖片';
      case 'webp':
        return '下載 WebP 圖片';
      default:
        return '下載檔案';
    }
  }

  // 切換上傳區域顯示
  toggleUploadArea() {
    this.showUploadArea = !this.showUploadArea;
    if (!this.showUploadArea) {
      // 隱藏上傳區域時清除檔案和錯誤訊息
      this.clearFiles();
    }
  }

  // 計算總檔案數量
  getTotalFilesCount(): number {
    if (!this.listSpecialChange || !Array.isArray(this.listSpecialChange)) {
      return 0;
    }
    return this.listSpecialChange.reduce((total, group) => {
      return total + (group.SpecialChangeFiles ? group.SpecialChangeFiles.length : 0);
    }, 0);
  }

  // 切換日期群組的收合狀態
  toggleDateGroup(changeDate: string) {
    this.collapsedStates[changeDate] = !this.collapsedStates[changeDate];
  }

  // 檢查日期群組是否已收合
  isDateGroupCollapsed(changeDate: string): boolean {
    return this.collapsedStates[changeDate] || false;
  }

  // 展開所有日期群組
  expandAllGroups() {
    Object.keys(this.collapsedStates).forEach(key => {
      this.collapsedStates[key] = false;
    });
  }

  // 收合所有日期群組
  collapseAllGroups() {
    Object.keys(this.collapsedStates).forEach(key => {
      this.collapsedStates[key] = true;
    });
  }

  // 根據來源代碼獲取來源名稱
  getSourceName(source?: number): string {
    switch (source) {
      case 1:
        return '系統';
      case 2:
        return '住戶';
      case 3:
        return '洽談';
      default:
        return '';
    }
  }

  // 根據來源代碼獲取來源樣式類別
  getSourceClass(source?: number): string {
    switch (source) {
      case 1:
        return 'source-system';
      case 2:
        return 'source-resident';
      case 3:
        return 'source-negotiation';
      default:
        return 'source-unknown';
    }
  }

  // 根據來源代碼獲取來源圖標
  getSourceIcon(source?: number): string {
    switch (source) {
      case 1:
        return 'pi-cog'; // 系統齒輪圖標
      case 2:
        return 'pi-home'; // 住戶房屋圖標
      case 3:
        return 'pi-comments'; // 洽談對話圖標
      default:
        return 'pi-question';
    }
  }

  // 根據來源代碼獲取來源描述
  getSourceDescription(source?: number): string {
    switch (source) {
      case 1:
        return '由系統自動產生';
      case 2:
        return '住戶上傳提供';
      case 3:
        return '洽談過程中產生';
      default:
        return '來源不明';
    }
  }

  // 獲取日期群組中各來源的檔案統計
  getSourceStats(files: any[]): { [key: number]: number } {
    const stats: { [key: number]: number } = {};
    files.forEach(file => {
      const source = file.CSource || 0;
      stats[source] = (stats[source] || 0) + 1;
    });
    return stats;
  }

  // 檢查是否有多種來源
  hasMultipleSources(files: any[]): boolean {
    const sources = new Set(files.map(file => file.CSource).filter(source => source));
    return sources.size > 1;
  }
}
