{"ast": null, "code": "import { environment } from '../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nexport class UtilityService {\n  constructor() {\n    this.BASE_FILE = environment.BASE_URL_API;\n  }\n  downloadFileFromUrl(file) {\n    const xhr = new XMLHttpRequest();\n    xhr.open('GET', `${this.BASE_FILE}${file.CFile}`, true);\n    xhr.responseType = 'blob';\n    xhr.onload = () => {\n      if (xhr.status === 200) {\n        const url = window.URL.createObjectURL(xhr.response);\n        const link = document.createElement('a');\n        link.href = url;\n        link.download = file.CName;\n        link.style.display = 'none';\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        window.URL.revokeObjectURL(url);\n      }\n    };\n    xhr.send();\n  }\n  getFileNameFromUrl(url) {\n    const parts = url.split('/');\n    const fileName = parts.pop();\n    return fileName;\n  }\n  downloadFileFullUrl(file) {\n    const xhr = new XMLHttpRequest();\n    xhr.open('GET', `${file.CFile}?date=${new Date().getTime()}`, true);\n    xhr.responseType = 'blob';\n    xhr.onload = () => {\n      if (xhr.status === 200) {\n        const url = window.URL.createObjectURL(xhr.response);\n        const link = document.createElement('a');\n        link.href = url;\n        link.download = file.CName;\n        link.style.display = 'none';\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        window.URL.revokeObjectURL(url);\n      }\n    };\n    xhr.send();\n  }\n  static #_ = this.ɵfac = function UtilityService_Factory(t) {\n    return new (t || UtilityService)();\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: UtilityService,\n    factory: UtilityService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["environment", "UtilityService", "constructor", "BASE_FILE", "BASE_URL_API", "downloadFileFromUrl", "file", "xhr", "XMLHttpRequest", "open", "CFile", "responseType", "onload", "status", "url", "window", "URL", "createObjectURL", "response", "link", "document", "createElement", "href", "download", "CName", "style", "display", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "send", "getFileNameFromUrl", "parts", "split", "fileName", "pop", "downloadFileFullUrl", "Date", "getTime", "_", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\SaleChangeFront\\src\\app\\shared\\services\\utility.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpResponse } from '@angular/common/http';\r\nimport { environment } from '../../../environments/environment';\r\nimport { BuildCaseGetFileRespone } from '../../../services/api/models';\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class UtilityService {\r\n  readonly BASE_FILE = environment.BASE_URL_API;\r\n\r\n  constructor() { }\r\n\r\n  downloadFileFromUrl(file: BuildCaseGetFileRespone) {\r\n    const xhr = new XMLHttpRequest();\r\n    xhr.open('GET', `${this.BASE_FILE}${file.CFile}`\r\n      , true);\r\n    xhr.responseType = 'blob';\r\n    xhr.onload = () => {\r\n      if (xhr.status === 200) {\r\n        const url = window.URL.createObjectURL(xhr.response);\r\n        const link = document.createElement('a');\r\n        link.href = url;\r\n        link.download = file.CName!;\r\n        link.style.display = 'none';\r\n        document.body.appendChild(link);\r\n        link.click();\r\n        document.body.removeChild(link);\r\n        window.URL.revokeObjectURL(url);\r\n      }\r\n    };\r\n    xhr.send();\r\n  }\r\n\r\n  getFileNameFromUrl(url: string) {\r\n    const parts = url.split('/');\r\n    const fileName = parts.pop();\r\n    return fileName;\r\n  }\r\n\r\n  downloadFileFullUrl(file: { CFile: string | any; CName: string | any }) {\r\n    const xhr = new XMLHttpRequest();\r\n    xhr.open('GET', `${file.CFile}?date=${new Date().getTime()}`\r\n      , true);\r\n    xhr.responseType = 'blob';\r\n    xhr.onload = () => {\r\n      if (xhr.status === 200) {\r\n        const url = window.URL.createObjectURL(xhr.response);\r\n        const link = document.createElement('a');\r\n        link.href = url;\r\n        link.download = file.CName!;\r\n        link.style.display = 'none';\r\n        document.body.appendChild(link);\r\n        link.click();\r\n        document.body.removeChild(link);\r\n        window.URL.revokeObjectURL(url);\r\n      }\r\n    };\r\n    xhr.send();\r\n  }\r\n}\r\n"], "mappings": "AAEA,SAASA,WAAW,QAAQ,mCAAmC;;AAK/D,OAAM,MAAOC,cAAc;EAGzBC,YAAA;IAFS,KAAAC,SAAS,GAAGH,WAAW,CAACI,YAAY;EAE7B;EAEhBC,mBAAmBA,CAACC,IAA6B;IAC/C,MAAMC,GAAG,GAAG,IAAIC,cAAc,EAAE;IAChCD,GAAG,CAACE,IAAI,CAAC,KAAK,EAAE,GAAG,IAAI,CAACN,SAAS,GAAGG,IAAI,CAACI,KAAK,EAAE,EAC5C,IAAI,CAAC;IACTH,GAAG,CAACI,YAAY,GAAG,MAAM;IACzBJ,GAAG,CAACK,MAAM,GAAG,MAAK;MAChB,IAAIL,GAAG,CAACM,MAAM,KAAK,GAAG,EAAE;QACtB,MAAMC,GAAG,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACV,GAAG,CAACW,QAAQ,CAAC;QACpD,MAAMC,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;QACxCF,IAAI,CAACG,IAAI,GAAGR,GAAG;QACfK,IAAI,CAACI,QAAQ,GAAGjB,IAAI,CAACkB,KAAM;QAC3BL,IAAI,CAACM,KAAK,CAACC,OAAO,GAAG,MAAM;QAC3BN,QAAQ,CAACO,IAAI,CAACC,WAAW,CAACT,IAAI,CAAC;QAC/BA,IAAI,CAACU,KAAK,EAAE;QACZT,QAAQ,CAACO,IAAI,CAACG,WAAW,CAACX,IAAI,CAAC;QAC/BJ,MAAM,CAACC,GAAG,CAACe,eAAe,CAACjB,GAAG,CAAC;;IAEnC,CAAC;IACDP,GAAG,CAACyB,IAAI,EAAE;EACZ;EAEAC,kBAAkBA,CAACnB,GAAW;IAC5B,MAAMoB,KAAK,GAAGpB,GAAG,CAACqB,KAAK,CAAC,GAAG,CAAC;IAC5B,MAAMC,QAAQ,GAAGF,KAAK,CAACG,GAAG,EAAE;IAC5B,OAAOD,QAAQ;EACjB;EAEAE,mBAAmBA,CAAChC,IAAkD;IACpE,MAAMC,GAAG,GAAG,IAAIC,cAAc,EAAE;IAChCD,GAAG,CAACE,IAAI,CAAC,KAAK,EAAE,GAAGH,IAAI,CAACI,KAAK,SAAS,IAAI6B,IAAI,EAAE,CAACC,OAAO,EAAE,EAAE,EACxD,IAAI,CAAC;IACTjC,GAAG,CAACI,YAAY,GAAG,MAAM;IACzBJ,GAAG,CAACK,MAAM,GAAG,MAAK;MAChB,IAAIL,GAAG,CAACM,MAAM,KAAK,GAAG,EAAE;QACtB,MAAMC,GAAG,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACV,GAAG,CAACW,QAAQ,CAAC;QACpD,MAAMC,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;QACxCF,IAAI,CAACG,IAAI,GAAGR,GAAG;QACfK,IAAI,CAACI,QAAQ,GAAGjB,IAAI,CAACkB,KAAM;QAC3BL,IAAI,CAACM,KAAK,CAACC,OAAO,GAAG,MAAM;QAC3BN,QAAQ,CAACO,IAAI,CAACC,WAAW,CAACT,IAAI,CAAC;QAC/BA,IAAI,CAACU,KAAK,EAAE;QACZT,QAAQ,CAACO,IAAI,CAACG,WAAW,CAACX,IAAI,CAAC;QAC/BJ,MAAM,CAACC,GAAG,CAACe,eAAe,CAACjB,GAAG,CAAC;;IAEnC,CAAC;IACDP,GAAG,CAACyB,IAAI,EAAE;EACZ;EAAC,QAAAS,CAAA,G;qBAnDUxC,cAAc;EAAA;EAAA,QAAAyC,EAAA,G;WAAdzC,cAAc;IAAA0C,OAAA,EAAd1C,cAAc,CAAA2C,IAAA;IAAAC,UAAA,EAFb;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}