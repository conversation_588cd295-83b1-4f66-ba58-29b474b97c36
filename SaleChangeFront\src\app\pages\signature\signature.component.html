<div class="wrapper">
  <div class="content">
    <div class="flex justify-center">
      <div class="signin">
        <div class="title">Step3 確認簽署</div>
        @if (docsData.length > 0) {
        <ng-container *ngFor="let docData of docsData">
          <div class="card flex justify-content-center">
            <div class="pafbox">
              <div class="block sm:flex items-center box1">

                <div class="flex items-center sm:mb-0 mb-2 ">
                  <span>
                    {{docData?.CDocumentName}}
                  </span>
                </div>
              </div>
              <div class="block sm:flex items-center box2">

                <div class="mr-4" *ngIf="!docData.CSign">產檔日期: {{docData.CCreateDT ? (docData.CCreateDT | dateFormat) :
                  ''}}</div>
                <div class="mr-4" *ngIf="docData.CSign">確認日期: {{docData.CSignDate ? (docData.CSignDate | dateFormat) :
                  ''}}</div>

                <button class="flex items-center gap-x-1 rounded-full px-3 py-1 mr-4 view-document-btn"
                  (click)="downloadFile(docData)">
                  <span class="font-semibold">查看文件</span>
                  <img src="assets/PDF.svg" class="cursor-pointer">
                </button>

                <div class="mr-4">
                  <span class="pl-4 pr-1">簽署狀態</span>
                  <div class="makesures mr-1 sm:mb-0 mb-2" [ngClass]="{'realsures': docData.CSign}"> {{ docData.CSign ?
                    '已簽署' : '未簽署' }}</div>
                </div>

                <button class="button2 !w-[120px]" *ngIf="!docData.CSign" (click)="showDialog(docData)">前往簽署</button>
                <div *ngIf="docData.CSign" class="!w-[120px]"></div>
              </div>

            </div>
          </div>


          <!-- <div class="card flex justify-content-center">
            <div class="pafbox">
              <div class="block sm:flex items-center box1">
                <div class="makesures mr-1 sm:mb-0 mb-2" [ngClass]="{'realsures': docData.CSign}"> {{ docData.CSign ?
                  '已確認' : '待確認' }}</div>
                <div class="flex items-center sm:mb-0 mb-2 ">
                  <span>
                    {{docData?.CDocumentName}}
                  </span>
                </div>
              </div>
              <div class="block sm:flex items-center box2">
                <button class="flex items-center gap-x-1 rounded-full px-3 py-1 mr-4 view-document-btn" (click)="downloadFile(docData)">
                  <span class="font-semibold">查看文件</span>
                  <img src="assets/PDF.svg" class="cursor-pointer">
                </button>
                <div class="mr-4" *ngIf="!docData.CSign">產檔日期: {{docData.CCreateDT ? (docData.CCreateDT | dateFormat) :
                  ''}}</div>
                <div class="mr-4" *ngIf="docData.CSign">確認日期: {{docData.CSignDate ? (docData.CSignDate | dateFormat) :
                  ''}}</div>
                <div class="mr-4">
                  <span class="pl-4 pr-1">簽署狀態</span>
                  <div class="makesures mr-1 sm:mb-0 mb-2" [ngClass]="{'realsures': docData.CSign}"> {{ docData.CSign ?
                    '已簽署' : '未簽署' }}</div>
                </div>
                <button class="button2" *ngIf="!docData.CSign" (click)="showDialog(docData)">前往簽署</button>
                <div *ngIf="docData.CSign" class="w-[160px]"></div>
              </div>
            </div>
          </div> -->
        </ng-container>
        }
        <div class="flex justify-center">
          <a class="w-full text-center">
            <button class="button1 my-12" (click)="goback()">返回主選單</button>
          </a>
        </div>
      </div>
    </div>
  </div>
</div>


<app-dialog-popup [textData]="textData" [(visible)]="visible" (actionButtonLeft)="actionButtonLeft()"
  (actionButtonRight)="actionButtonRight()">
  <div class="w-full px-4">
    <div class="w-full">
      <button class="text-center flex items-center gap-x-1 rounded-full py-1 m-auto view-document-btn"
        style="padding: 0 60px;">
        <span class="font-semibold">查看文件</span>
        <img src="assets/PDF.svg" class="cursor-pointer" (click)="downloadFile(selectedDoc)">
      </button>
    </div>
    <div class="mt-4 w-full text-stone-600 checkbox-zone">
      <div class="bg-white p-4">
        <p-checkbox [binary]="true" label="我已確認並同意所有客變圖面內容。" [(ngModel)]="agree"></p-checkbox>
      </div>
    </div>
    <div class="h-fit max-h-[200px] text-xl font-light overflow-y-scroll mt-2">
      請點選下方灰色區塊進行簽名
    </div>
    <div class="mt-4">
      <app-signature-pad #signaturePadComponent></app-signature-pad>
    </div>
  </div>
</app-dialog-popup>
<p-toast pRipple position="top-right" [style]="{'width': '22rem'}"></p-toast>
