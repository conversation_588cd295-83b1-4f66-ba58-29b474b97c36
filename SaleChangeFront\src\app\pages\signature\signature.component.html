<div class="wrapper">
  <div class="content">
    <div class="flex justify-center">
      <div class="signin">
        <div class="title">Step3 確認簽署</div>
        @if (docsData.length > 0) {
        <ng-container *ngFor="let docData of docsData">
          <div class="card flex justify-content-center">
            <div class="pafbox">
              <div class="document-container">
                <!-- 左側區域：文件資訊和狀態 -->
                <div class="document-info">
                  <div class="document-title">
                    {{docData?.CDocumentName}}
                  </div>
                  <div class="document-meta">
                    <span class="document-date">
                      {{ docData.CSign ? '確認日期' : '產檔日期' }}:
                      {{ docData.CSign ?
                      (docData.CSignDate ? (docData.CSignDate | dateFormat) : '') :
                      (docData.CCreateDT ? (docData.CCreateDT | dateFormat) : '')
                      }}
                    </span>
                    <!-- 簽署狀態 -->
                    <div class="status-badge" [ngClass]="{'signed': docData.CSign, 'unsigned': !docData.CSign}">
                      {{ docData.CSign ? '已簽署' : '未簽署' }}
                    </div>
                  </div>
                </div>

                <!-- 右側區域：操作按鈕 -->
                <div class="document-controls">
                  <!-- 查看文件按鈕 -->
                  <button class="view-document-btn" (click)="downloadFile(docData)">
                    <span>查看文件</span>
                    <img src="assets/PDF.svg" alt="PDF">
                  </button>

                  <!-- 前往簽署按鈕 -->
                  <button class="button2 sign-action-btn" *ngIf="!docData.CSign" (click)="showDialog(docData)">
                    前往簽署
                  </button>
                </div>
              </div>

            </div>
          </div>


        </ng-container>
        }
        <div class="flex justify-center">
          <a class="w-full text-center">
            <button class="button1 my-12" (click)="goback()">返回主選單</button>
          </a>
        </div>
      </div>
    </div>
  </div>
</div>


<app-dialog-popup [textData]="textData" [(visible)]="visible" (actionButtonLeft)="actionButtonLeft()"
  (actionButtonRight)="actionButtonRight()">
  <div class="w-full px-4">
    <div class="w-full">
      <button class="text-center flex items-center gap-x-1 rounded-full py-1 m-auto view-document-btn"
        style="padding: 0 60px;">
        <span class="font-semibold">查看文件</span>
        <img src="assets/PDF.svg" class="cursor-pointer" (click)="downloadFile(selectedDoc)">
      </button>
    </div>
    <div class="mt-4 w-full text-stone-600 checkbox-zone">
      <div class="bg-white p-4">
        <p-checkbox [binary]="true" label="我已確認並同意所有客變圖面內容。" [(ngModel)]="agree"></p-checkbox>
      </div>
    </div>
    <div class="h-fit max-h-[200px] text-xl font-light overflow-y-scroll mt-2">
      請點選下方灰色區塊進行簽名
    </div>
    <div class="mt-4">
      <app-signature-pad #signaturePadComponent></app-signature-pad>
    </div>
  </div>
</app-dialog-popup>
<p-toast pRipple position="top-right" [style]="{'width': '22rem'}"></p-toast>
