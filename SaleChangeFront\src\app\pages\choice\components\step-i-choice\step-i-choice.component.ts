import { Component, EventEmitter, Input, OnInit, Output, OnDestroy } from '@angular/core';
import { HttpResponse } from '@angular/common/http';
import { DropdownModule } from 'primeng/dropdown';
import { FormsModule } from '@angular/forms';
import { BaseFilePipe } from '../../../../shared/pipes/base-file.pipe';
import { DialogModule } from 'primeng/dialog';
import { PdfViewerModule } from 'ng2-pdf-viewer';
import { CheckboxModule } from 'primeng/checkbox';
import { ButtonModule } from 'primeng/button';
import { CommonModule } from '@angular/common';
import { HouseService } from '../../../../../services/api/services';
import { FileService } from '../../../../../services/File.service';

@Component({
  selector: 'app-step-i-choice',
  standalone: true,
  imports: [
    FormsModule,
    DropdownModule,
    DialogModule,
    PdfViewerModule,
    CheckboxModule,
    ButtonModule,
    CommonModule,
    BaseFilePipe
  ],
  templateUrl: './step-i-choice.component.html',
  styleUrl: './step-i-choice.component.scss'
})
export class StepIChoiceComponent implements OnInit, OnDestroy {

  @Input() agreeToGoNextStep1: boolean = false;

  @Output() agreeToGoNextStep1Change = new EventEmitter();
  @Output() nextEvent = new EventEmitter()

  detailPicture: Blob | string | any
  detailPictureUrl: string | null = null;

  constructor(
    private _houseService: HouseService,
    private _fileService: FileService
  ) { }

  ngOnInit(): void {
    this.getImage()
  }
  getImage() {
    this._houseService.apiHouseGetRegularNoticeFilePost$Json({})
      .subscribe(res => {
        if (res.Entries && res.Entries.CFileName && res.Entries.CFileUrl) {
          // 從 CFileUrl 中解析出相對路徑和檔名
          const fileUrl = res.Entries.CFileUrl;
          const fileName = res.Entries.CFileName;          // 使用 FileService 取得 PDF blob
          this._fileService.getFile(fileUrl, fileName).subscribe(
            (response: HttpResponse<Blob>) => {
              if (response.body) {
                this.detailPicture = response.body;
                // 創建 blob URL 用於顯示 PDF
                this.detailPictureUrl = URL.createObjectURL(response.body);
              }
            }
          );
        }
      });
  }

  next() {
    this.nextEvent.emit();
  }
  changeCheckbox(event: any) {
    this.agreeToGoNextStep1Change.emit(event)
  }

  ngOnDestroy(): void {
    // 清理 blob URL 以避免記憶體洩漏
    if (this.detailPictureUrl) {
      URL.revokeObjectURL(this.detailPictureUrl);
    }
  }
}
