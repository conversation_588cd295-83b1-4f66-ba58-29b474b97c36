import { HttpClient, HttpResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ServiceBase } from './service-base';

@Injectable({
  providedIn: 'root'
})
export class FileService extends ServiceBase {

  protected baseUrl = `${this.apiBaseUrl}/File`;
  constructor(
    http: HttpClient
  ) {
    super(http);
  }

  getFile(relativePath: string, fileName: string): Observable<HttpResponse<Blob>> {
    const url = `${this.baseUrl}/GetFile`;
    const params = {
      relativePath: relativePath,
      fileName: fileName
    };

    return this.http.get(url, {
      params: params,
      observe: 'response',
      responseType: 'blob'
    });
  }
}
